server.port=8082
spring.application.name=crm-approve

spring.cloud.nacos.discovery.server-addr=${nacos.address}
spring.cloud.nacos.config.server-addr=${nacos.address}
spring.cloud.nacos.discovery.namespace=${nacos.namespace}
spring.cloud.nacos.config.namespace=${nacos.namespace}

#spring.cloud.nacos.discovery.server-addr=*************:8848
#spring.cloud.nacos.config.server-addr=*************:8848
#spring.cloud.nacos.discovery.namespace=7b6ba4cf-1dc1-451d-b985-74f92f1c005b
#spring.cloud.nacos.config.namespace=7b6ba4cf-1dc1-451d-b985-74f92f1c005b