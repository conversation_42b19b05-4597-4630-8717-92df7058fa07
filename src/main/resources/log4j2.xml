<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="WARN">

    <Appenders>
        <RollingFile name="RollingFile" fileName="/home/<USER>/logs/crm-approve.log"
                     filePattern="/home/<USER>/logs/$${date:yyyy-MM}/crm-approve-%d{MM-dd-yyyy}-%i.log.gz">
            <PatternLayout>
                <Pattern>%d{yyyy-MM-dd HH:mm:ss} %-5level %class{36}.%M(%L) - %msg%n</Pattern>
            </PatternLayout>
            <Policies>
                <TimeBasedTriggeringPolicy />
                <!-- Optionally, you can add a SizeBasedTriggeringPolicy here too -->
            </Policies>
            <DefaultRolloverStrategy max="20"/>
        </RollingFile>
    </Appenders>
    <Loggers>
        <Root level="warn">
            <AppenderRef ref="RollingFile"/>
        </Root>
    </Loggers>

    <!--<Appenders>
        &lt;!&ndash; Console 日志，只输出 level 及以上级别的信息，并配置各级别日志输出颜色 &ndash;&gt;
        <Console name="Console" target="SYSTEM_OUT">
            &lt;!&ndash;控制台只输出level及以上级别的信息（onMatch），其他的直接拒绝（onMismatch）&ndash;&gt;
            <ThresholdFilter level="warn" onMatch="ACCEPT" onMismatch="DENY"/>
            <PatternLayout pattern="%highlight{%d{yyyy.MM.dd 'at' HH:mm:ss z} %-5level %class{36} %M() @%L - %msg%n}{FATAL=Bright Red, ERROR=Bright Magenta, WARN=Bright Yellow, INFO=Bright Green, DEBUG=Bright Cyan, TRACE=Bright White}"/>
        </Console>

        &lt;!&ndash; kafka 日志，输出日志到 Logstash 中做日志收集 &ndash;&gt;
        <Kafka name="Kafka" topic="logs">
            <Filters>
                <ThresholdFilter level="error" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>

            <PatternLayout pattern="${sys:agent} %d{yyyy-MM-dd 'at' HH:mm:ss z} %-5level %class{36} %L %M - %msg%xEx%n"/>
            <Property name="bootstrap.servers">${sys:kafka.address}</Property>
            <Property name="max.block.ms">5000</Property>
            <Property name="buffer.memory">67108864</Property>
            <Property name="batch.size">4096</Property>
            <Property name="linger.ms">5000</Property>
            <Property name="acks">0</Property>
        </Kafka>

        <Async name="KafkaLogger">
            <appender-ref ref="Kafka"/>
        </Async>
    </Appenders>
    <Loggers>
        &lt;!&ndash; avoid recursive logging &ndash;&gt;
        <Logger name="org.apache.kafka" level="INFO" />

        &lt;!&ndash; 如果需要打印location需加includeLocation=true &ndash;&gt;
        <AsyncRoot level="info" includeLocation="true">
            <Appender-Ref ref="Console"/>
            <Appender-Ref ref="KafkaLogger"/>
        </AsyncRoot>

    </Loggers>-->
</Configuration>