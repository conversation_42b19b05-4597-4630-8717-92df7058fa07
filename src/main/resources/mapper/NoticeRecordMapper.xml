<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ssb.approve.dao.NoticeRecordDAO">
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO crm_notice_record
        (project_id, contact_id, user_id, source, name, content, type, `status`, `read`, target_id, target_type, route, notice_type, create_by, create_time, update_by, update_time)
        VALUES
        <foreach collection="list" item="record" separator=",">
            (#{record.projectId}, #{record.contactId}, #{record.userId}, #{record.source}, #{record.name}, #{record.content}, #{record.type},
            #{record.status}, #{record.read}, #{record.targetId}, #{record.targetType}, #{record.route}, #{record.noticeType},
            #{record.createBy}, #{record.createTime}, #{record.updateBy}, #{record.updateTime})
        </foreach>
    </insert>

    <update id="updateRecord" parameterType="com.ssb.approve.entity.CrmNoticeRecord">
        UPDATE crm_notice_record
        <set>
        <if test="status != null">status = #{status},</if>
        <if test="read != null">`read` = #{read},</if>
        <if test="updateBy != null">update_by = #{updateBy},</if>
        <if test="updateTime != null">update_time = #{updateTime},</if>
        </set>
            WHERE target_id = #{targetId} and target_type = #{targetType} and notice_type = #{noticeType} and `type` = 1
    </update>

    <update id="updateProjectNoticeRecord" parameterType="com.ssb.approve.entity.CrmNoticeRecord">
        UPDATE crm_notice_record
        <set>
        <if test="status != null">status = #{status},</if>
        <if test="read != null">`read` = #{read},</if>
        <if test="updateBy != null">update_by = #{updateBy},</if>
        <if test="updateTime != null">update_time = #{updateTime},</if>
        </set>
            WHERE project_id = #{projectId} and notice_type = #{noticeType} and `type` = 1
    </update>
</mapper>
