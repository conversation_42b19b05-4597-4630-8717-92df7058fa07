<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ssb.approve.dao.SettleLogDAO">

    <resultMap id="resultMap" type="com.ssb.approve.entity.SettleLog">
        <result column="enterprise_id" property="enterpriseId"/>
        <result column="follow_id" property="followId"/>
        <result column="settle_id" property="settleId"/>
        <result column="behavior_time" property="behaviorTime"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="del_flag" property="delFlag"/>
    </resultMap>

    <!-- 保存结算日志 -->
    <insert id="save" parameterType="com.ssb.approve.entity.SettleLog" useGeneratedKeys="true" keyColumn="id" keyProperty="id">
        INSERT INTO crm_settle_log(enterprise_id, follow_id, settle_id, behavior_time, performance, payment, create_by, create_time, remark)
        VALUES (#{enterpriseId}, #{followId}, #{settleId}, #{behaviorTime}, #{performance}, #{payment}, #{createBy}, #{createTime}, #{remark})
    </insert>

    <!-- 获取结算日志  通过id -->
    <select id="getSettleLogById" resultMap="resultMap">
        SELECT id, enterprise_id, follow_id, settle_id, behavior_time, performance, payment, create_by, create_time, remark
        FROM crm_settle_log
        WHERE id = #{id} AND del_flag = 0
    </select>

    <!-- 获取总金额  通过跟进id -->
    <select id="getAllPerformanceByFollowId" resultType="int">
        SELECT sum(performance)
        FROM crm_settle_log where follow_id = #{followId} and del_flag = 0
    </select>

    <!-- 获取结算记录     通过跟进人 -->
    <select id="getSettleLogByCreateBy" resultMap="resultMap">
        SELECT id, enterprise_id, follow_id, settle_id, behavior_time, performance, payment, create_by, create_time, del_flag, remark
        FROM crm_settle_log
        WHERE enterprise_id = #{enterpriseId} AND follow_id = #{followId} AND create_by = #{createBy} AND del_flag = 0
    </select>

    <!-- 离职更新  结算记录 对接人 -->
    <update id="quitUpdateSettleLogCreateBy">
        UPDATE crm_settle_log
        SET create_by = #{toUserId}
        WHERE enterprise_id = #{enterpriseId} AND create_by = #{fromUserId} AND del_flag = 0
        AND follow_id in
        <foreach collection="followIds" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </update>

    <!-- 获取结算日志  通过结算明细id -->
    <select id="getSettleLogBySettleId" resultMap="resultMap">
        SELECT id, enterprise_id, follow_id, settle_id, behavior_time, performance, payment, create_by, create_time, remark
        FROM crm_settle_log
        WHERE settle_id = #{settleId} AND del_flag = 0
    </select>

    <update id="deleteSettleLog" parameterType="java.lang.Integer">
        update crm_settle_log set del_flag = 1 where settle_id = #{settleId}
    </update>

</mapper>