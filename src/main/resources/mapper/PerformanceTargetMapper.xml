<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ssb.approve.dao.PerformanceTargetDAO">

    <resultMap id="resultMap" type="com.ssb.approve.entity.PerformanceTarget">
        <result column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="performance" property="performance"/>
        <result column="year" property="year"/>
        <result column="month" property="month"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 保存业绩目标 -->
    <insert id="save" parameterType="com.ssb.approve.entity.PerformanceTarget" useGeneratedKeys="true" keyColumn="id" keyProperty="id">
        INSERT INTO crm_performance_target (
            user_id,
            performance,
            year,
            month,
            create_by,
            create_time,
            update_by,
            update_time
        ) VALUES (
            #{userId},
            #{performance},
            #{year},
            #{month},
            #{createBy},
            #{createTime},
            #{updateBy},
            #{updateTime}
        )
    </insert>

    <!-- 根据用户ID、年份、月份查询业绩目标 -->
    <select id="getByUserIdAndYearAndMonth" resultMap="resultMap">
        SELECT 
            id,
            user_id,
            performance,
            year,
            month,
            create_by,
            create_time,
            update_by,
            update_time
        FROM crm_performance_target
        WHERE user_id = #{userId}
        AND year = #{year}
        AND month = #{month}
    </select>

    <!-- 更新业绩目标 -->
    <update id="update" parameterType="com.ssb.approve.entity.PerformanceTarget">
        UPDATE crm_performance_target
        SET 
            performance = #{performance},
            update_by = #{updateBy},
            update_time = #{updateTime}
        WHERE id = #{id}
    </update>

    <select id="getPerformanceTarget" parameterType="com.ssb.approve.model.dto.PerformanceTargetDTO" resultType="java.math.BigDecimal">
        SELECT
            IFNULL( SUM( cpt.performance ), 0 )
        FROM
            crm_performance_target cpt
        inner join crm_user cu on cu.id = cpt.user_id
        WHERE
            cpt.user_id IN
            <foreach collection="userIdList" item="userId" separator="," open="(" close=")">
                #{userId}
            </foreach>
          AND cpt.`year` = #{year}
          AND cpt.`month` = #{month}
    </select>

</mapper> 