<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ssb.approve.dao.CommissionStrategyDifficultyDAO">

<!--    <insert id="insertDifficulty" parameterType="com.ssb.approve.entity.CommissionStrategyDifficulty">-->
<!--        insert into crm_commission_strategy_difficulty (strategy_id, classify_id, industry_first_id, industry_second_id, position_first_id, position_second_id,-->
<!--        <if test="null != high">-->
<!--            high,-->
<!--        </if>-->
<!--        <if test="null != base">-->
<!--            base,-->
<!--        </if>-->
<!--        <if test="null != low">-->
<!--            low,-->
<!--        </if>-->
<!--         create_by, create_time, update_by, update_time, del_flag)-->
<!--        VALUES (#{strategyId}, 0, #{industryFirstId}, #{industrySecondId}, #{positionFirstId}, #{positionSecondId},-->
<!--        <if test="null != high">-->
<!--            #{high},-->
<!--        </if>-->
<!--        <if test="null != base">-->
<!--            #{base},-->
<!--        </if>-->
<!--        <if test="null != low">-->
<!--            #{low},-->
<!--        </if>-->
<!--        #{createBy}, now(), #{createBy}, now(), 0)-->
<!--    </insert>-->

<!--    <update id="updateDifficulty" parameterType="com.ssb.approve.entity.CommissionStrategyDifficulty">-->
<!--        update crm_commission_strategy_difficulty-->
<!--        <set>-->
<!--            <if test="null != high">-->
<!--                high = #{high},-->
<!--            </if>-->
<!--            <if test="null != base">-->
<!--                base = #{base},-->
<!--            </if>-->
<!--            <if test="null != low">-->
<!--                low = #{low},-->
<!--            </if>-->
<!--            update_by = #{createBy}, update_time = now()-->
<!--        </set>-->
<!--        where id = #{id}-->
<!--    </update>-->

<!--    <select id="getDifficultyList" resultType="com.ssb.approve.model.vo.CommissionStrategyDifficultyVO">-->
<!--        SELECT-->
<!--            id, strategy_id strategyId,-->
<!--            industry_first_id industryFirstId,-->
<!--            (select name from crm_industry where id = industryFirstId) industryFirstName,-->
<!--            industry_second_id industrySecondId,-->
<!--            (select name from crm_industry where id = industrySecondId) industrySecondName,-->
<!--            position_first_id positionFirstId,-->
<!--            (select name from crm_position_v2 where id = positionFirstId) positionFirstName,-->
<!--            position_second_id positionSecondId,-->
<!--            (select name from crm_position_v2 where id = positionSecondId) positionSecondName,-->
<!--            settlement_rate settlementRate, high, base, low-->
<!--        FROM-->
<!--            crm_commission_strategy_difficulty-->
<!--        WHERE-->
<!--            strategy_id = #{strategyId} and del_flag = 0-->
<!--            <if test="null != industryId">-->
<!--                and (industry_first_id = #{industryId} or industry_second_id = #{industryId})-->
<!--            </if>-->
<!--            <if test="null != positionId">-->
<!--                and (position_first_id = #{positionId} or position_second_id = #{positionId})-->
<!--            </if>-->
<!--    </select>-->

<!--    <select id="getAllDifficultyList" resultType="com.ssb.approve.model.PositionIndustryModel">-->
<!--        SELECT-->
<!--            id, strategy_id strategyId,-->
<!--            industry_first_id industryFirstId,-->
<!--            industry_second_id industryId,-->
<!--            position_first_id positionFirstId,-->
<!--            position_second_id positionId-->
<!--        FROM-->
<!--            crm_commission_strategy_difficulty-->
<!--        WHERE-->
<!--            del_flag = 0-->
<!--            <if test="industrySecondId != null">-->
<!--                and industry_second_id = #{industrySecondId}-->
<!--            </if>-->
<!--    </select>-->

<!--    <insert id="batchInsertDifficulty" parameterType="com.ssb.approve.entity.CommissionStrategyDifficulty">-->
<!--        insert into crm_commission_strategy_difficulty (strategy_id, classify_id, industry_first_id, industry_second_id, position_first_id, position_second_id,high,base,low,-->
<!--        create_by, create_time, update_by, update_time, del_flag)-->
<!--        VALUES-->
<!--         <foreach collection="difficultyList" item="item" separator=",">-->
<!--             (#{item.strategyId}, #{item.classifyId}, #{item.industryFirstId}, #{item.industrySecondId}, #{item.positionFirstId}, #{item.positionSecondId}, #{item.high},-->
<!--             #{item.base}, #{item.low}, #{item.createBy}, now(), #{item.createBy}, now(), 0)-->
<!--         </foreach>-->
<!--    </insert>-->

<!--    <insert id="batchInsertPositionIndustry">-->
<!--        insert into crm_commission_strategy_difficulty (strategy_id, classify_id, industry_first_id, industry_second_id, position_first_id, position_second_id,-->
<!--        create_by, create_time, update_by, update_time, del_flag)-->
<!--        VALUES-->
<!--        <foreach collection="list" item="item" separator=",">-->
<!--            (#{item.strategyId}, 0, #{item.industryFirstId}, #{item.industryId}, #{item.positionFirstId}, #{item.positionId},-->
<!--             #{item.updateBy}, now(), #{item.updateBy}, now(), 0)-->
<!--        </foreach>-->
<!--    </insert>-->

<!--    <update id="batchUpdatePositionIndustry">-->
<!--        update crm_commission_strategy_difficulty set del_flag = 1, update_by = #{userId}, update_time = now() where-->
<!--        id in (-->
<!--        <foreach collection="list" item="item" index="index" separator=",">-->
<!--            #{item.id}-->
<!--        </foreach>-->
<!--        )-->
<!--    </update>-->

</mapper>