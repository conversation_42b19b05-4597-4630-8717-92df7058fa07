<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ssb.approve.dao.SettleDetailsDAO">

    <resultMap id="resultMap" type="com.ssb.approve.entity.SettleDetails">
        <result column="enterprise_id" property="enterpriseId"/>
        <result column="follow_id" property="followId"/>
        <result column="entry_time" property="entryTime"/>
        <result column="settle_time" property="settleTime"/>
        <result column="actual_settle_time" property="actualSettleTime"/>
        <result column="tmp_status" property="tmpStatus"/>
        <result column="leave_time" property="leaveTime"/>
        <result column="leave_cause" property="leaveCause"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="del_flag" property="delFlag"/>
    </resultMap>

    <resultMap id="voResultMap" type="com.ssb.approve.model.vo.SettleDetailsVO">
        <result column="enterprise_id" property="enterpriseId"/>
        <result column="follow_id" property="followId"/>
        <result column="entry_time" property="entryTime"/>
        <result column="settle_time" property="settleTime"/>
        <result column="actual_settle_time" property="actualSettleTime"/>
        <result column="tmp_status" property="tmpStatus"/>
        <result column="leave_time" property="leaveTime"/>
        <result column="leave_cause" property="leaveCause"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="review_user_id" property="reviewUserId"/>
        <result column="review_time" property="reviewTime"/>
    </resultMap>

    <!-- 结算明细 Resultmap -->
    <resultMap id="settleDetailsResultMap" type="com.ssb.approve.entity.SettleDetails" extends="resultMap">
        <result column="id" property="id"/>
        <association property="settleLog" column="id" select="com.ssb.approve.dao.SettleLogDAO.getSettleLogBySettleId"/>
    </resultMap>

    <!-- 保存结算明细 -->
    <insert id="save" parameterType="com.ssb.approve.entity.SettleDetails" useGeneratedKeys="true" keyColumn="id" keyProperty="id">
        INSERT INTO crm_settle_details (enterprise_id, follow_id, node, entry_time, salary, `date`, `time`, settle_time, create_by, create_time, update_by, update_time)
        VALUES (#{enterpriseId}, #{followId}, #{node}, #{entryTime}, #{salary}, #{date}, #{time}, #{settleTime}, #{create_by}, #{create_time}, #{update_by}, #{update_time})
    </insert>

    <!-- 批量保存结算明细 -->
    <insert id="saveBatch">
        INSERT INTO crm_settle_details (enterprise_id, follow_id, node, entry_time, salary, `date`, `time`, settle_time, create_by, create_time, update_by, update_time, auth_type)
        VALUES
        <foreach collection ="settleDetails" item="settleDetail" separator =",">
            (#{settleDetail.enterpriseId}, #{settleDetail.followId}, #{settleDetail.node}, #{settleDetail.entryTime}, #{settleDetail.salary}, #{settleDetail.date}, #{settleDetail.time}, #{settleDetail.settleTime}, #{settleDetail.createBy}, #{settleDetail.createTime}, #{settleDetail.updateBy}, #{settleDetail.updateTime}, #{settleDetail.authType})
        </foreach >
    </insert>

    <!-- 获取结算确认列表 -->
    <select id="getSettleAuthList" resultMap="voResultMap">
        SELECT csd.id, cc.id contactId, cp.id projectId, csd.follow_id, csd.settle_time, cc.name contactName, cc.phone contactPhone, cp.title,
        cu.name userName, csd.status, csd.tmp_status, csd.leave_time, csd.leave_cause, csd.entry_time, csd.node, csd.date,
        TIME_FORMAT(csd.time, '%H:%i') `time`, csl.performance, csl.payment, cc.type, csd.actual_settle_time,
        (case when cp.source = 3 then csd.salary / (select rate from crm_project_share where project_id = cp.share_project_id and del_flag = 0 limit 1) * 100 else csd.salary end) as salary
        FROM crm_settle_details csd
        LEFT JOIN crm_follow_up cfu on cfu.id = csd.follow_id
        LEFT JOIN crm_contact cc on cfu.contact_id = cc.id
        LEFT JOIN crm_project cp on cp.id = cfu.`project_id`
        LEFT JOIN crm_user cu on cu.id = csd.create_by
        LEFT JOIN crm_settle_log csl on csl.settle_id = csd.id AND csl.del_flag = 0
        <where>
            (
                (csd.enterprise_id = #{enterpriseId} and cp.share_project_id is null)
                or
                (cp.share_project_id in (select id from crm_project where enterprise_id = #{enterpriseId} and share_status = 1))
            )
            AND cfu.del_flag = 0
            AND csd.del_flag = 0
            AND (cp.master = #{userId} or cp.signing_user_id = #{userId})
            <!-- 待结算 -->
            <if test="settleStatus == 1">
                AND csd.status = 0
            </if>
            <!-- 已结算 -->
            <if test="settleStatus == 2">
                AND csd.status != 0
            </if>
            <!-- 普通检索 -->
            <if test="null != searchType and searchType != ''">
                <if test="searchType == 'fullName'">
                    and exists (select 0 from crm_customer where name LIKE CONCAT('%', #{search},'%') and id = cp.customer_id)
                </if>
                <if test="searchType == 'title'">
                    and cp.title LIKE CONCAT('%', #{search},'%')
                </if>
                <if test="searchType == 'contactName'">
                    and cc.name LIKE CONCAT('%', #{search},'%')
                </if>
                <if test="searchType == 'contactPhone'">
                    and cc.phone LIKE CONCAT('%', #{search},'%')
                </if>
            </if>
            <!-- 预计/实际结算时间 -->
            <if test="null != settleTimeType and settleTimeType != ''">
                <if test="settleTimeType == 'actualSettleTime' and null != beginTime and null != endTime">
                    and csd.actual_settle_time &gt;= #{beginTime} and csd.actual_settle_time &lt;= #{endTime}
                </if>
                <if test="settleTimeType == 'settleTime' and null != beginTime and null != endTime">
                    and csd.settle_time &gt;= #{beginTime} and csd.settle_time &lt;= #{endTime}
                </if>
            </if>
        </where>
        ORDER BY csd.settle_time
        limit #{pageBegin}, #{pageSize}
    </select>

    <!-- 获取结算确认 数量 -->
    <select id="getSettleAuthCount" resultType="int">
        SELECT count(csd.id)
        FROM crm_settle_details csd
        LEFT JOIN crm_follow_up cfu on cfu.id = csd.follow_id
        LEFT JOIN crm_contact cc on cfu.contact_id = cc.id
        LEFT JOIN crm_project cp on cp.id = cfu.`project_id`
        LEFT JOIN crm_user cu on cu.id = csd.create_by
        LEFT JOIN crm_settle_log csl on csl.settle_id = csd.id AND csl.del_flag = 0
        <where>
            (
                (csd.enterprise_id = #{enterpriseId} and cp.share_project_id is null)
                or
                (cp.share_project_id in (select id from crm_project where enterprise_id = #{enterpriseId} and share_status = 1))
            )
            AND cfu.del_flag = 0
            AND csd.del_flag = 0
            AND (cp.master = #{userId} or cp.signing_user_id = #{userId})
            <!-- 待结算 -->
            <if test="settleStatus == 1">
                AND csd.status = 0
            </if>
            <!-- 已结算 -->
            <if test="settleStatus == 2">
                AND csd.status != 0
            </if>
            <!-- 普通检索 -->
            <if test="null != searchType and searchType != ''">
                <if test="searchType == 'fullName'">
                    and exists (select 0 from crm_customer where name LIKE CONCAT('%', #{search},'%') and id = cp.customer_id)
                </if>
                <if test="searchType == 'title'">
                    and cp.title LIKE CONCAT('%', #{search},'%')
                </if>
                <if test="searchType == 'contactName'">
                    and cc.name LIKE CONCAT('%', #{search},'%')
                </if>
                <if test="searchType == 'contactPhone'">
                    and cc.phone LIKE CONCAT('%', #{search},'%')
                </if>
            </if>
            <!-- 预计/实际结算时间 -->
            <if test="null != settleTimeType and settleTimeType != ''">
                <if test="settleTimeType == 'actualSettleTime' and null != beginTime and null != endTime">
                    and csd.actual_settle_time &gt;= #{beginTime} and csd.actual_settle_time &lt;= #{endTime}
                </if>
                <if test="settleTimeType == 'settleTime' and null != beginTime and null != endTime">
                    and csd.settle_time &gt;= #{beginTime} and csd.settle_time &lt;= #{endTime}
                </if>
            </if>
        </where>
    </select>

    <!-- 获取结算明细 -->
    <select id="getSettleDetailsById" resultMap="resultMap">
        SELECT id, enterprise_id, follow_id, node, entry_time, salary, `date`, `time`, settle_time, actual_settle_time, `status`,
        tmp_status, leave_time, leave_cause, create_by, create_time, update_by, update_time
        FROM crm_settle_details
        WHERE id = #{id} AND del_flag = 0
    </select>

    <!-- 更新明细状态 -->
    <update id="updateSettleDetailsStatus">
        UPDATE crm_settle_details
        SET status = #{status}, update_by = #{updateBy}, update_time = #{updateTime}, actual_settle_time = #{actualSettleTime}
        <if test="null != reviewTime">
            , review_time = #{reviewTime}
        </if>
        <if test="reviewUserId != null">
            , review_user_id = #{reviewUserId}
        </if>
        WHERE id = #{id}
    </update>

    <update id="updateSettleDetailsByFollowId">
        UPDATE crm_settle_details
        SET status = #{status}, update_by = #{updateBy}, update_time = #{updateTime}
        WHERE follow_id = #{followId}
    </update>

    <!-- 获取未结算数量 -->
    <select id="unSettleCount" resultType="int">
        SELECT count(id)
        FROM crm_settle_details
        WHERE `status` = 0 AND follow_id = #{followId} AND del_flag = 0
    </select>

    <!-- 获取结算数量 -->
    <select id="getSettleCountByFollowIdStatus" resultType="int">
        SELECT count(id)
        FROM crm_settle_details
        WHERE `status` = #{status} AND follow_id = #{followId} AND del_flag = 0
    </select>

    <!-- 获取结算明细 -->
    <select id="getSettleDetailsByFollowId" resultMap="settleDetailsResultMap">
        SELECT id, enterprise_id, follow_id, node, entry_time, salary, `date`, `time`,
        (case when actual_settle_time is null then settle_time else actual_settle_time end)settle_time, `status`, create_by, create_time
        FROM crm_settle_details
        WHERE follow_id = #{followId} and del_flag = 0
    </select>

    <!-- 获取待结算数量 -->
    <select id="getWaitSettleCount" resultType="int">
        SELECT count(cfu.id)
        FROM crm_follow_up cfu
        WHERE EXISTS (
            SELECT id FROM crm_settle_details WHERE follow_id = cfu.id AND `status` = 0 and del_flag = 0
        )
        AND cfu.project_id = #{projectId} and cfu.del_flag = 0
    </select>

    <!-- 获取结算明细  通过跟进人 -->
    <select id="getSettleDetailsByCreateBy" resultMap="resultMap">
        SELECT id, enterprise_id, follow_id, node, entry_time, salary, `date`, `time`, settle_time, status, create_by, create_time, update_by, update_time, del_flag
        FROM crm_settle_details
        WHERE enterprise_id = #{enterpriseId} AND follow_id = #{followId} AND create_by = #{createBy} and del_flag = 0
    </select>

    <!-- 离职更新 结算明细 对接人 -->
    <update id="quitUpdateSettleDetailsCreateBy">
        UPDATE crm_settle_details
        SET create_by = #{toUserId}, update_time = #{updateTime}
        WHERE enterprise_id = #{enterpriseId} AND create_by = #{fromUserId} AND del_flag = 0
        AND follow_id in
        <foreach collection="followIds" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </update>

    <!-- 更新结算详情状态 -->
    <update id="updateSettleDetailsTmpStatus">
        UPDATE crm_settle_details
        SET tmp_status = #{tmpStatus}, leave_time = #{leaveTime}, leave_cause = #{leaveCause}, update_by = #{updateBy},
        update_time = #{updateTime}
        WHERE id = #{id}
    </update>

    <!-- 个人数据 结算量 -->
    <select id="getIndividualSettleData" resultMap="voResultMap">
        select * from
        (
            SELECT cfu.id, cc.id contactId, cc.name contactName, cc.phone contactPhone, cp.title, cu.name userName,
            max(csd.actual_settle_time) settle_time, csd.`status`
            FROM crm_follow_up cfu
            left join crm_contact cc on cfu.contact_id = cc.id
            left join crm_project cp on cp.id = cfu.`project_id`
            left join crm_user cu on cu.id = cfu.create_by
            LEFT JOIN crm_settle_details csd on csd.follow_id = cfu.id
            where cfu.current_status = 10 and csd.del_flag = 0
            <if test="createBySet != null and createBySet.size > 0">
                and cfu.create_by in
                <foreach collection="createBySet" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="createBySet == null or createBySet.size == 0">
                and cfu.create_by = #{userId}
            </if>
            GROUP BY cfu.id
        ) a
        where a.settle_time &gt;= #{beginTime} and a.settle_time &lt;= #{endTime}
        ORDER BY a.settle_time desc
        limit #{pageBegin}, #{pageSize}
    </select>

    <!-- 个人数据 结算量 数量-->
    <select id="getIndividualSettleDataCount" resultType="int">
        select count(*) from
        (
            SELECT cfu.id, cc.id contactId, cc.name contactName, cc.phone contactPhone, cp.title, cu.name userName,
            max(csd.actual_settle_time) settle_time, csd.`status`
            FROM crm_follow_up cfu
            left join crm_contact cc on cfu.contact_id = cc.id
            left join crm_project cp on cp.id = cfu.`project_id`
            left join crm_user cu on cu.id = cfu.create_by
            LEFT JOIN crm_settle_details csd on csd.follow_id = cfu.id
            where cfu.current_status = 10 and csd.del_flag = 0
            <if test="createBySet != null and createBySet.size > 0">
                and cfu.create_by in
                <foreach collection="createBySet" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="createBySet == null or createBySet.size == 0">
                and cfu.create_by = #{userId}
            </if>
            GROUP BY cfu.id
        ) a
        where a.settle_time &gt;= #{beginTime} and a.settle_time &lt;= #{endTime}
    </select>

    <select id="countSettledByFollowId" resultType="int">
        select count(*) from crm_settle_details where status = 0 and follow_id = #{followId} and node &lt; #{node} and del_flag = 0
    </select>

    <select id="getSettleDetailsListById" resultMap="resultMap">
        select id, enterprise_id, follow_id from crm_settle_details where status = 0 and follow_id = #{followId} and node &gt; #{node} and del_flag = 0
    </select>

    <select id="getPrevActualSettleTime" resultType="java.sql.Timestamp">
        select MAX(actual_settle_time) from crm_settle_details where node &lt; #{node} and follow_id = #{followId} and del_flag = 0
    </select>

    <select id="getTheRestDetailsList" resultMap="voResultMap">
        SELECT
            csd.id, csd.follow_id, csd.settle_time, csd.status, csd.tmp_status, csd.leave_time, csd.leave_cause, csd.entry_time,
            csd.salary, csd.node, csd.date, TIME_FORMAT(csd.time, '%H:%i') `time`, csd.actual_settle_time
        FROM crm_settle_details csd
        where node > #{node} and follow_id = #{followId} and csd.del_flag = 0
    </select>

    <update id="updateSettleTime">
        update crm_settle_details set settle_time = #{settleTime}, update_by = #{userId}, update_time = now() where id = #{id}
    </update>

    <select id="getRevokeSettleList" resultType="com.ssb.approve.model.vo.FollowRevokeVO">
        select
            csd.id,
            (case when csd.status = 1 then actual_settle_time when csd.status = 2 then settle_time end) behaviorTime,
            cc.id contactId,
            cp.title,
            cc.name contactName,
            cc.phone contactPhone,
            (case when csd.status = 1 then '已结算' when csd.status = 2 then '已离职' end) result,
            cu.name userName,
            cc.type,
            csd.node,
            cfu.id followId,
            cp.id projectId
        from crm_settle_details csd
        left join crm_follow_up cfu on cfu.id = csd.`follow_id`
        left join crm_project cp on cfu.`project_id` = cp.id
        left join crm_contact cc on cc.id = cfu.`contact_id`
        left join crm_user cu on cu.id = cfu.create_by
        <where>
            (
                csd.enterprise_id = #{enterpriseId} and cp.share_project_id is null
                or
                (cp.share_project_id in (select id from crm_project where enterprise_id = #{enterpriseId} and share_status = 1))
            )
            and csd.status in (1, 2) and csd.del_flag = 0
            <if test="null != range and range == 1">
                AND cp.master = #{userId}
            </if>
            <!-- 普通检索 -->
            <if test="null != searchType and searchType != ''">
                <if test="searchType == 'contactName'">
                    and cc.name LIKE CONCAT('%', #{search},'%')
                </if>
                <if test="searchType == 'contactPhone'">
                    and cc.phone LIKE CONCAT('%', #{search},'%')
                </if>
            </if>
            <if test="null != title">
                and cp.title LIKE CONCAT('%', #{title},'%')
            </if>
            <if test="null != beginTime and null != endTime">
                and (
                    (csd.status = 1 and csd.actual_settle_time between #{beginTime} and #{endTime})
                    or (csd.status = 2 and csd.settle_time between #{beginTime} and #{endTime})
                )
            </if>
        </where>
        ORDER BY behaviorTime desc
        limit #{pageBegin}, #{pageSize}
    </select>

    <select id="getRevokeSettleListCount" resultType="java.lang.Integer">
        select
            count(*)
        from crm_settle_details csd
        left join crm_follow_up cfu on cfu.id = csd.`follow_id`
        left join crm_project cp on cfu.`project_id` = cp.id
        left join crm_contact cc on cc.id = cfu.`contact_id`
        left join crm_user cu on cu.id = cfu.create_by
        <where>
            csd.enterprise_id = #{enterpriseId}
            and csd.status in (1, 2) and csd.del_flag = 0
            <if test="null != range and range == 1">
                AND cp.master = #{userId}
            </if>
            <!-- 普通检索 -->
            <if test="null != searchType and searchType != ''">
                <if test="searchType == 'contactName'">
                    and cc.name LIKE CONCAT('%', #{search},'%')
                </if>
                <if test="searchType == 'contactPhone'">
                    and cc.phone LIKE CONCAT('%', #{search},'%')
                </if>
            </if>
            <if test="null != title">
                and cp.title LIKE CONCAT('%', #{title},'%')
            </if>
            <if test="null != beginTime and null != endTime">
                and (
                (csd.status = 1 and csd.actual_settle_time between #{beginTime} and #{endTime})
                or (csd.status = 2 and csd.settle_time between #{beginTime} and #{endTime})
                )
            </if>
        </where>
    </select>

    <select id="existSettleByFollowId" resultType="java.lang.Integer" parameterType="java.lang.Integer">
        select count(*) from crm_settle_details where follow_id = #{followId} and del_flag = 0
    </select>

    <update id="deleteSettleDetailsByFollowId">
        update crm_settle_details set del_flag = 1, update_by = #{userId}, update_time = #{updateTime} where follow_id = #{followId}
    </update>

    <select id="countOtherSettles" resultType="java.lang.Integer">
        select
            count(*)
        from
            crm_settle_details csd
        where
            csd.status = #{status}
            and csd.follow_id = #{followId}
            and csd.del_flag = 0
            <if test="status == 1">
                and csd.node &gt; #{node}
            </if>
            <if test="status == 2">
                and csd.node &lt; #{node}
            </if>
    </select>

    <update id="updateSettleDetailsRevoke">
        UPDATE crm_settle_details
        SET actual_settle_time = null, status = 0, leave_time = null, leave_cause = null,
            update_by = #{updateBy}, update_time = #{updateTime}
        WHERE id = #{id}
    </update>

    <select id="countUnProcessedSettle" resultType="java.lang.Integer">
        select count(*) from crm_settle_details where follow_id = #{followId} and status = 0 and del_flag = 0
    </select>

    <select id="getEnterpriseProjectSettleAuthList" resultMap="voResultMap">
        SELECT csd.id, cc.id contactId, cp.id projectId, csd.follow_id, csd.settle_time, cc.name contactName, cc.phone contactPhone, cp.title,
        cu.name userName, csd.status, csd.tmp_status, csd.leave_time, csd.leave_cause, csd.entry_time, csd.salary, csd.node, csd.date,
        TIME_FORMAT(csd.time, '%H:%i') `time`, csl.performance, csl.payment, cc.type, csd.actual_settle_time, csl.remark, csd.review_time, csd.review_user_id,
        (select name from crm_user where id = csd.review_user_id) as reviewUserName
        FROM crm_settle_details csd
        LEFT JOIN crm_follow_up cfu on cfu.id = csd.follow_id
        LEFT JOIN crm_contact cc on cfu.contact_id = cc.id
        LEFT JOIN crm_project cp on cp.id = cfu.`project_id`
        LEFT JOIN crm_user cu on cu.id = csd.update_by and cu.enterprise_id = #{enterpriseId}
        LEFT JOIN crm_settle_log csl on csl.settle_id = csd.id AND csl.del_flag = 0
        <where>
            cfu.project_id = #{projectId}
            AND cfu.del_flag = 0
            AND csd.del_flag = 0
            <if test="contactName != null ">
                and cc.name LIKE CONCAT('%', #{contactName},'%')
            </if>
            <if test="followUpUser != null ">
                and cu.name LIKE CONCAT('%', #{followUpUser},'%')
            </if>
            <!-- 待结算 -->
            <if test="settleStatus == 1">
                AND csd.status = 0
            </if>
            <!-- 已结算 -->
            <if test="settleStatus == 2">
                AND csd.status = 1
            </if>
            <!-- 已离职 -->
            <if test="settleStatus == 3">
                AND csd.status = 2
            </if>
            <!-- 已中断 -->
            <if test="settleStatus == 4">
                AND csd.status = 3
            </if>

            <if test="null != beginTime and null != endTime">
                <if test="null != timeType and timeType == 1">
                    and csd.settle_time &gt;= #{beginTime} and csd.settle_time &lt;= #{endTime}
                </if>
                <if test="null != timeType and timeType == 2">
                    and csd.actual_settle_time &gt;= #{beginTime} and csd.actual_settle_time &lt;= #{endTime}
                </if>
                <if test="null != timeType and timeType == 3">
                    and csd.leave_time &gt;= #{beginTime} and csd.leave_time &lt;= #{endTime}
                </if>
                <if test="null != timeType and timeType == 4">
                    and csd.review_time &gt;= #{beginTime} and csd.review_time &lt;= #{endTime}
                </if>
            </if>
            <if test="null != authStatus">
                and csd.auth = #{authStatus}
            </if>
        </where>
        ORDER BY csd.settle_time
        limit #{pageBegin}, #{pageSize}
    </select>

    <!-- 获取结算确认 数量 -->
    <select id="getEnterpriseProjectSettleAuthCount" resultType="int">
        SELECT count(csd.id)
        FROM crm_settle_details csd
        LEFT JOIN crm_follow_up cfu on cfu.id = csd.follow_id
        LEFT JOIN crm_contact cc on cfu.contact_id = cc.id
        LEFT JOIN crm_project cp on cp.id = cfu.`project_id`
        LEFT JOIN crm_user cu on cu.id = csd.update_by and cu.enterprise_id = #{enterpriseId}
        LEFT JOIN crm_settle_log csl on csl.settle_id = csd.id AND csl.del_flag = 0
        <where>
            cfu.project_id = #{projectId}
            AND cfu.del_flag = 0
            AND csd.del_flag = 0
            <if test="contactName != null ">
                and cc.name LIKE CONCAT('%', #{contactName},'%')
            </if>
            <if test="followUpUser != null ">
                and cu.name LIKE CONCAT('%', #{followUpUser},'%')
            </if>
            <!-- 已结算 -->
            <if test="settleStatus == 2">
                AND csd.status = 1
            </if>
            <!-- 已离职 -->
            <if test="settleStatus == 3">
                AND csd.status = 2
            </if>
            <!-- 已中断 -->
            <if test="settleStatus == 4">
                AND csd.status = 3
            </if>

            <if test="null != beginTime and null != endTime">
                <if test="null != timeType and timeType == 1">
                    and csd.settle_time &gt;= #{beginTime} and csd.settle_time &lt;= #{endTime}
                </if>
                <if test="null != timeType and timeType == 2">
                    and csd.actual_settle_time &gt;= #{beginTime} and csd.actual_settle_time &lt;= #{endTime}
                </if>
                <if test="null != timeType and timeType == 3">
                    and csd.leave_time &gt;= #{beginTime} and csd.leave_time &lt;= #{endTime}
                </if>
                <if test="null != timeType and timeType == 4">
                    and csd.review_time &gt;= #{beginTime} and csd.review_time &lt;= #{endTime}
                </if>
            </if>
            <if test="null != authStatus">
                and csd.auth = #{authStatus}
            </if>
        </where>
    </select>

</mapper>