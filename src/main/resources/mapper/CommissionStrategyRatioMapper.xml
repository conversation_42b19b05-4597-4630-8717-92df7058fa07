<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ssb.approve.dao.CommissionStrategyRatioDAO">

    <insert id="batchInsert">
        insert into crm_commission_strategy_ratio (strategy_id, type, commission_level_id, high, base_high, base, base_low, low, create_by, create_time, update_by, update_time, del_flag)
         VALUES
         <foreach collection="ratioList" item="item" index="index" separator=",">
             (#{item.strategyId}, #{item.type}, #{item.commissionLevelId},
             <choose>
                 <when test="null != item.high">
                     #{item.high},
                 </when>
                 <otherwise>
                     0,
                 </otherwise>
             </choose>
             <choose>
                 <when test="null != item.baseHigh">
                     #{item.baseHigh},
                 </when>
                 <otherwise>
                     0,
                 </otherwise>
             </choose>
             <choose>
                 <when test="null != item.base">
                     #{item.base},
                 </when>
                 <otherwise>
                     0,
                 </otherwise>
             </choose>
             <choose>
                 <when test="null != item.baseLow">
                     #{item.baseLow},
                 </when>
                 <otherwise>
                     0,
                 </otherwise>
             </choose>
             <choose>
                 <when test="null != item.low">
                     #{item.low},
                 </when>
                 <otherwise>
                     0,
                 </otherwise>
             </choose>
              #{userId}, now(), #{userId}, now(), 0)
         </foreach>

    </insert>


    <select id="getRatioList" resultType="com.ssb.approve.model.vo.CommissionStrategyRatioVO">
        SELECT
            id, strategy_id strategyId, type, commission_level_id commissionLevelId, high, base, low
        FROM
            crm_commission_strategy_ratio
        WHERE
            strategy_id = #{strategyId} and del_flag = 0
    </select>

    <resultMap id="resultMap" type="com.ssb.approve.entity.CommissionStrategyRatio">
        <id column="id" property="id"/>
        <result column="strategy_id" property="strategyId"/>
        <result column="commission_level_id" property="commissionLevelId"/>
        <result column="base_high" property="baseHigh"/>
        <result column="base_low" property="baseLow"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="del_flag" property="delFlag"/>
    </resultMap>

    <!-- 获取最新提成比例 -->
    <select id="getLastCommissionStrategyRatio" resultMap="resultMap">
        select id, strategy_id, `type`, commission_level_id, high, base_high, base, base_low, low, create_by, create_time,
        update_by, update_time, del_flag
        from crm_commission_strategy_ratio
        where strategy_id = #{strategyId} and del_flag = 0
    </select>

</mapper>