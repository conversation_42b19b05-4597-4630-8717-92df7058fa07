<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ssb.approve.dao.FollowCommissionDAO">

    <resultMap id="resultMap" type="com.ssb.approve.entity.FollowCommission">
        <id column="id" property="id"/>
        <result column="follow_id" property="followId"/>
        <result column="classify_id" property="classifyId"/>
        <result column="industry_first_id" property="industryFirstId"/>
        <result column="industry_second_id" property="industrySecondId"/>
        <result column="position_first_id" property="positionFirstId"/>
        <result column="position_second_id" property="positionSecondId"/>
        <result column="strategy_id" property="strategyId"/>
        <result column="rate_content" property="rateContent"/>
        <result column="create_time" property="createTime"/>
        <result column="contract_start_time" property="contractStartTime"/>
        <result column="settlement_content" property="settlementContent"/>
        <result column="err_state" property="errState"/>
    </resultMap>

    <!-- 保存跟进提成 -->
    <insert id="save" parameterType="com.ssb.approve.entity.FollowCommission" useGeneratedKeys="true" keyColumn="id" keyProperty="id">
        INSERT INTO crm_follow_commission
        <trim prefix="(" suffix=")" suffixOverrides=",">
            follow_id,
            strategy_id,
            classify_id,
            industry_first_id,
            industry_second_id,
            position_first_id,
            position_second_id,
            difficulty,
            rate_content,
            contract_start_time,
            contract_end_time,
            settlement_content,
            create_time,
            <if test="errState != null">
                err_state,
            </if>
        </trim>

        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            #{followId},
            #{strategyId},
            #{classifyId},
            #{industryFirstId},
            #{industrySecondId},
            #{positionFirstId},
            #{positionSecondId},
            #{difficulty},
            #{rateContent},
            #{contractStartTime},
            #{contractEndTime},
            #{settlementContent},
            now(),
            <if test="errState != null">
                #{errState},
            </if>
        </trim>
    </insert>

    <!-- 获取跟进提成 -->
    <select id="getFollowCommission" resultMap="resultMap">
        select id, follow_id, classify_id,industry_first_id, industry_second_id, position_first_id, position_second_id, strategy_id,
        difficulty, rate_content, create_time, err_state, contract_start_time, settlement_content
        from crm_follow_commission
        where follow_id = #{followId}
        limit 1
    </select>

    <select id="getProjectCompleteCount" resultType="java.lang.Integer">
        SELECT
            count( * )
        FROM
            crm_follow_up
        WHERE
            current_status IN ( 1, 2, 4, 9, 10, 11 )
            AND project_id = #{projectId} and del_flag = 0
    </select>

</mapper>