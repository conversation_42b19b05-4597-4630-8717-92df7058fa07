<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ssb.approve.dao.FollowRecommendDAO">

    <select id="existsDataWithin90Days" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM crm_follow_recommend
        WHERE create_time >= DATE_SUB(NOW(), INTERVAL 90 DAY)
          and contact_id = #{contactId}
          and project_id = #{projectId}
    </select>

    <insert id="insertFollowRecommend" parameterType="com.ssb.approve.entity.CrmFollowRecommend" useGeneratedKeys="true"
            keyProperty="id">
        INSERT INTO crm_follow_recommend (contact_id,
                                          resume_id,
                                          status,
                                          auth,
                                          enterprise_id,
                                          customer_enterprise_id,
                                          project_id,
                                          remark,
                                          recommend_by,
                                          recommend_time,
                                          create_by,
                                          create_time,
                                          update_by,
                                          update_time)
        VALUES (#{contactId},
                #{resumeId},
                #{status},
                #{auth},
                #{enterpriseId},
                #{customerEnterpriseId},
                #{projectId},
                #{remark},
                #{recommendBy},
                #{recommendTime},
                #{createBy},
                #{createTime},
                #{updateBy},
                #{updateTime})
    </insert>

    <update id="updateFollowRecommend" parameterType="com.ssb.approve.entity.CrmFollowRecommend">
        UPDATE crm_follow_recommend
        <set>
            <if test="contactId != null">contact_id = #{contactId},</if>
            <if test="resumeId != null">resume_id = #{resumeId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="auth != null">auth = #{auth},</if>
            <if test="enterpriseId != null">enterprise_id = #{enterpriseId},</if>
            <if test="customerEnterpriseId != null">customer_enterprise_id = #{customerEnterpriseId},</if>
            <if test="projectId != null">project_id = #{projectId},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="recommendBy != null">recommend_by = #{recommendBy},</if>
            <if test="recommendTime != null">recommend_time = #{recommendTime},</if>
            <if test="checkedBy != null">checked_by = #{checkedBy},</if>
            <if test="checkedTime != null">checked_time = #{checkedTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </set>
        WHERE id = #{id}
    </update>

    <select id="getFollowRecommendList" parameterType="com.ssb.approve.model.dto.FollowRecommendListDTO" resultType="com.ssb.approve.model.vo.FollowRecommendListVO">
        SELECT
            cfr.id,
            cr.id resumeId,
            cc.id contactId,
            cc.`name` jobSeekerName,
            cc.phone jobSeekerPhone,
            cp.id projectId,
            cp.title projectName,
            cfr.recommend_time recommendTime,
            cfr.STATUS,
            cfr.auth
            cfr.failure_reason failureReason,
            cfr.checked_time checkedTime
        FROM
            crm_follow_recommend cfr
                INNER JOIN crm_contact cc ON cc.id = cfr.contact_id
                INNER JOIN crm_resume cr ON cr.contact_id = cc.id
                INNER JOIN crm_project cp ON cfr.project_id = cp.id
        WHERE
            cfr.recommend_by = #{userId}
            <if test="jobSeekerKeyword != null">
              and (cc.name LIKE CONCAT('%', #{jobSeekerKeyword},'%') or cc.phone LIKE CONCAT('%', #{jobSeekerKeyword},'%'))
            </if>
            <if test="projectName != null">
              and cp.title like CONCAT('%', #{projectName},'%')
            </if>
            <if test="status != null">
              and cfr.`status` = #{status}
            </if>
            <if test="auth != null">
                and cfr.auth = #{auth}
            </if>
            <if test="timeType != null and startTime != null and endTime != null">
                <choose>
                    <when test="timeType == 1">
                        and cfr.recommend_time between #{startTime} and #{endTime}
                    </when>
                    <when test="timeType == 2">
                        and cfr.checked_time between #{startTime} and #{endTime}
                    </when>
                </choose>
            </if>
            order by cfr.recommend_time desc
            LIMIT #{pageBegin}, #{pageSize}
    </select>

    <select id="getFollowRecommendListCount" parameterType="com.ssb.approve.model.dto.FollowRecommendListDTO" resultType="java.lang.Integer">
        SELECT
            count(*)
        FROM
            crm_follow_recommend cfr
                INNER JOIN crm_contact cc ON cc.id = cfr.contact_id
                INNER JOIN crm_resume cr ON cr.contact_id = cc.id
                INNER JOIN crm_project cp ON cfr.project_id = cp.id
        WHERE
            cfr.recommend_by = #{userId}
            <if test="jobSeekerKeyword != null">
              and (cc.name LIKE CONCAT('%', #{jobSeekerKeyword},'%') or cc.phone LIKE CONCAT('%', #{jobSeekerKeyword},'%'))
            </if>
            <if test="projectName != null">
              and cp.title like CONCAT('%', #{projectName},'%')
            </if>
            <if test="status != null">
              and cfr.`status` = #{status}
            </if>
            <if test="timeType != null and startTime != null and endTime != null">
                <choose>
                    <when test="timeType == 1">
                        and cfr.recommend_time between #{startTime} and #{endTime}
                    </when>
                    <when test="timeType == 2">
                        and cfr.checked_time between #{startTime} and #{endTime}
                    </when>
                </choose>
            </if>
    </select>

    <select id="getApprovedThroughReviewCount" resultType="java.lang.Integer">
        SELECT
            count(*)
        FROM
            crm_follow_recommend cfr
        WHERE
            cfr.recommend_by = #{userId}
            AND cfr.`status` = 2
    </select>

    <select id="getProjectsRecommendCount" resultType="java.util.Map">
        select project_id projectId, count(resume_id) `count`
        from crm_follow_recommend
        where
        customer_enterprise_id = #{customerEnterpriseId}
        and project_id in
        <foreach collection="projectIds" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        group by project_id
    </select>

    <select id="getFollowRecommendListByEnterprise" resultType="com.ssb.approve.model.vo.FollowRecommendListVO">
        SELECT
        cfr.id,
        cfr.resume_id resumeId,
        cfr.contact_id contactId,
        (select name from crm_user where id = cfr.recommend_by) as recommendUserName,
        cfr.recommend_time recommendTime,
        cfr.STATUS,
        cfr.auth,
        cfr.failure_reason failureReason,
        cfr.checked_time checkedTime,
        cc.`name` jobSeekerName,
        cfr.project_id projectId
        FROM
        crm_follow_recommend cfr
        LEFT JOIN crm_contact cc ON cc.id = cfr.contact_id
        WHERE
        cfr.customer_enterprise_id = #{enterpriseId}
        <if test="jobSeekerKeyword != null">
            and exists (select 1 from crm_contact where id = cfr.contact_id and name LIKE CONCAT('%', #{jobSeekerKeyword},'%'))
        </if>
        <if test="status != null">
            and cfr.`status` = #{status}
        </if>
        <if test="auth != null">
            and cfr.auth = #{auth}
        </if>
        <if test="timeType != null and startTime != null and endTime != null">
            <choose>
                <when test="timeType == 1">
                    and cfr.recommend_time between #{startTime} and #{endTime}
                </when>
                <when test="timeType == 2">
                    and cfr.checked_time between #{startTime} and #{endTime}
                </when>
            </choose>
        </if>
        <if test="recommendUserName != null">
            and exists (select 1 from crm_user where id = cfr.recommend_by and name LIKE CONCAT('%', #{recommendUserName},'%'))
        </if>
        order by cfr.id asc
        LIMIT #{pageBegin}, #{pageSize}
    </select>

    <select id="getFollowRecommendCountByEnterprise" resultType="int">
        SELECT count(0)
        FROM
        crm_follow_recommend cfr
        LEFT JOIN crm_contact cc ON cc.id = cfr.contact_id
        WHERE
        cfr.customer_enterprise_id = #{enterpriseId}
        <if test="jobSeekerKeyword != null">
            and exists (select 1 from crm_contact where id = cfr.contact_id and name LIKE CONCAT('%', #{jobSeekerKeyword},'%'))
        </if>
        <if test="status != null">
            and cfr.`status` = #{status}
        </if>
        <if test="auth != null">
            and cfr.auth = #{auth}
        </if>
        <if test="timeType != null and startTime != null and endTime != null">
            <choose>
                <when test="timeType == 1">
                    and cfr.recommend_time between #{startTime} and #{endTime}
                </when>
                <when test="timeType == 2">
                    and cfr.checked_time between #{startTime} and #{endTime}
                </when>
            </choose>
        </if>
        <if test="recommendUserName != null">
            and exists (select 1 from crm_user where id = cfr.recommend_by and name LIKE CONCAT('%', #{recommendUserName},'%'))
        </if>
    </select>
    <select id="getFollowRecommendCountByEnterpriseId" resultType="int">
        SELECT
            count(*)
        FROM
        crm_follow_recommend cfr
        WHERE
        cfr.customer_enterprise_id = #{dto.enterpriseId}
        <if test="dto.timeType != 0">
            AND cir.recommend_time between #{dto.timeStart} and #{dto.timeEnd}
        </if>
    </select>

    <select id="getFollowUpRecRemark" resultType="com.ssb.approve.entity.CrmFollowRecommend">
        SELECT
            cfr.id,
            cfr.resume_id resumeId,
            cfr.contact_id contactId,
            cfr.recommend_time recommendTime,
            cfr.STATUS,
            cfr.failure_reason failureReason,
            cfr.checked_time checkedTime,
            cfr.project_id projectId,
            cfr.remark
        FROM
            crm_follow_recommend cfr
        WHERE
            customer_enterprise_id = #{customerEnterpriseId}
        AND resume_id = #{resumeId}
        AND project_id = #{projectId}
    </select>
    <select id="getFollowRecommendById" resultType="com.ssb.approve.entity.CrmFollowRecommend">
        SELECT
            cfr.id,
            cfr.resume_id resumeId,
            cfr.contact_id contactId,
            cfr.recommend_by recommendBy,
            cfr.recommend_time recommendTime,
            cfr.STATUS,
            cfr.failure_reason failureReason,
            cfr.checked_time checkedTime,
            cfr.project_id projectId,
            cfr.remark
        FROM
            crm_follow_recommend cfr
        WHERE
            cfr.id = #{id}
    </select>
</mapper>
