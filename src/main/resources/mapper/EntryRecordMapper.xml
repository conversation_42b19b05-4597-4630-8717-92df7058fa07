<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ssb.approve.dao.EntryRecordDAO">

    <resultMap id="resultMap" type="com.ssb.approve.entity.EntryRecord">
        <result column="enterprise_id" property="enterpriseId"></result>
        <result column="follow_id" property="followId"></result>
        <result column="estimate_time" property="estimateTime"></result>
        <result column="behavior_time" property="behaviorTime"></result>
        <result column="create_by" property="createBy"></result>
        <result column="create_time" property="createTime"></result>
        <result column="update_by" property="updateBy"></result>
        <result column="update_time" property="updateTime"></result>
        <result column="auth_type" property="authType"/>
    </resultMap>

    <resultMap id="voResultMap" type="com.ssb.approve.model.vo.EntryRecordVO">
        <result column="enterprise_id" property="enterpriseId"></result>
        <result column="follow_id" property="followId"></result>
        <result column="estimate_time" property="estimateTime"></result>
        <result column="behavior_time" property="behaviorTime"></result>
        <result column="create_by" property="createBy"></result>
        <result column="create_time" property="createTime"></result>
        <result column="update_by" property="updateBy"></result>
        <result column="update_time" property="updateTime"></result>
        <result column="review_time" property="reviewTime"/>
        <result column="review_user_id" property="reviewUserId"/>
    </resultMap>

    <!-- 保存入职记录 -->
    <insert id="save" parameterType = "com.ssb.approve.entity.EntryRecord" useGeneratedKeys="true" keyColumn="id" keyProperty="id">
        insert into crm_entry_record (enterprise_id, follow_id, estimate_time, remark, create_by, create_time, update_by, update_time)
        VALUES (#{enterpriseId}, #{followId}, #{estimateTime}, #{remark}, #{createBy}, #{createTime}, #{updateBy}, #{updateTime})
    </insert>

    <!-- 获取入职记录  通过id -->
    <select id="getEntryRecordById" resultMap="resultMap">
        SELECT id, enterprise_id, follow_id, status, auth, behavior_time, estimate_time, create_by, auth_type
        from crm_entry_record
        where id = #{id}
    </select>

    <!-- 更新入职记录 -->
    <update id="updateEntryRecord">
        UPDATE crm_entry_record
        <set>
            `status` = #{status},
            <if test="auth != null">
                auth = #{auth},
            </if>
            <if test="null != reviewTime">
                review_time = #{reviewTime},
            </if>
            <if test="reviewUserId != null">
                review_user_id = #{reviewUserId},
            </if>
            behavior_time = #{behaviorTime},
            estimate_time = #{estimateTime},
            remark = #{remark},
            refuse = #{refuse},
            update_by = #{updateBy},
            update_time = #{updateTime},
        </set>
        WHERE id = #{id}
    </update>

    <update id="updateEntryRecordByFollowId">
        UPDATE crm_entry_record
        <set>
            `status` = #{status},
            <if test="auth != null">
                auth = #{auth},
            </if>
            <if test="behaviorTime != null">
                behavior_time = #{behaviorTime},
            </if>
            <if test="estimateTime != null">
                estimate_time = #{estimateTime},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
            <if test="refuse != null">
                refuse = #{refuse},
            </if>
            update_by = #{updateBy},
            update_time = #{updateTime}
        </set>
        WHERE follow_id = #{followId}
    </update>

    <!-- 获取入职确认列表 -->
    <select id="getEntryAuthList" resultMap="voResultMap">
        SELECT cer.id, cp.id projectId, cer.follow_id, cer.`estimate_time`, cer.`behavior_time`, cc.id contactId,
               cc.name contactName, cc.phone contactPhone, cp.title, cu.name userName, cer.auth, cer.status, cc.type,
               cer.remark, cer.review_time, cer.review_user_id,
               (select name from crm_user where id = cer.review_user_id) as reviewUserName
        FROM crm_entry_record cer
        left join crm_follow_up cfu on cfu.id = cer.follow_id
        left join crm_contact cc on cfu.contact_id = cc.id
        left join crm_project cp on cp.id = cfu.`project_id`
        left join crm_user cu on cu.id = cer.create_by
        <where>
            (
                (cer.enterprise_id = #{enterpriseId} and cp.share_project_id is null)
                or
                (cp.share_project_id in (select id from crm_project where enterprise_id = #{enterpriseId} and share_status = 1))
            )
                AND NOT (cer.estimate_time is null and cer.`status` = 0)
                AND cp.signing_user_id = #{userId}
            <if test="authStatus != null ">
                AND cer.auth = #{authStatus}
            </if>
            <!-- 已入职 -->
            <if test="entryStatus == 1">
                and cer.status = 1
            </if>
            <!-- 拒绝入职 -->
            <if test="entryStatus == 2">
                and cer.status = 3
            </if>
            <!-- 待入职 -->
            <if test="entryStatus == 3">
                and cer.status in (0, 2)
            </if>
            and cfu.del_flag = 0
            <if test="null != title">
                and cp.title LIKE CONCAT('%', #{title},'%')
            </if>
            <if test="null != jobSeeker">
                and (cc.name LIKE CONCAT('%', #{jobSeeker},'%') or cc.phone LIKE CONCAT('%', #{jobSeeker},'%'))
            </if>
            <if test="null != beginTime and null != endTime">
                and cer.`estimate_time` &gt;= #{beginTime} and cer.`estimate_time` &lt;= #{endTime}
            </if>
            <if test="null != beginEntryTime and null != endEntryTime">
                and cer.`behavior_time` &gt;= #{beginEntryTime} and cer.`behavior_time` &lt;= #{endEntryTime}
            </if>
            <if test="createBy != null">
                and cfu.create_by = #{createBy}
            </if>
        </where>
        ORDER BY cer.estimate_time
        limit #{pageBegin}, #{pageSize}
    </select>

    <!-- 获取入职确认数量 -->
    <select id="getEntryAuthCount" resultType="int">
        SELECT count(cer.id)
        FROM crm_entry_record cer
        left join crm_follow_up cfu on cfu.id = cer.follow_id
        left join crm_contact cc on cfu.contact_id = cc.id
        left join crm_project cp on cp.id = cfu.`project_id`
        left join crm_user cu on cu.id = cer.create_by
        <where>
            (
                (cer.enterprise_id = #{enterpriseId} and cp.share_project_id is null)
                or
                (cp.share_project_id in (select id from crm_project where enterprise_id = #{enterpriseId} and share_status = 1))
            )
                AND NOT (cer.estimate_time is null and cer.`status` = 0)
                AND cp.signing_user_id = #{userId}
            <if test="authStatus != null ">
                AND cer.auth = #{authStatus}
            </if>
            <!-- 已入职 -->
            <if test="entryStatus == 1">
                and cer.status = 1
            </if>
            <!-- 拒绝入职 -->
            <if test="entryStatus == 2">
                and cer.status = 3
            </if>
            <!-- 待入职 -->
            <if test="entryStatus == 3">
                and cer.status in (0, 2)
            </if>
            and cfu.del_flag = 0
            <if test="null != title">
                and cp.title LIKE CONCAT('%', #{title},'%')
            </if>
            <if test="null != jobSeeker">
                and (cc.name LIKE CONCAT('%', #{jobSeeker},'%') or cc.phone LIKE CONCAT('%', #{jobSeeker},'%'))
            </if>
            <if test="null != beginTime and null != endTime">
                and cer.`estimate_time` &gt;= #{beginTime} and cer.`estimate_time` &lt;= #{endTime}
            </if>
            <if test="null != beginEntryTime and null != endEntryTime">
                and cer.`behavior_time` &gt;= #{beginEntryTime} and cer.`behavior_time` &lt;= #{endEntryTime}
            </if>
            <if test="createBy != null">
                and cfu.create_by = #{createBy}
            </if>
        </where>
    </select>

    <!-- 更新入职审核状态 -->
    <update id="updateEntryAuth">
        UPDATE crm_entry_record
        SET auth = #{auth}, update_by = #{updateBy}, update_time = #{updateTime}
        WHERE id = #{id}
    </update>

    <!-- 待入职数量 -->
    <select id="getWaitEntryCount" resultType="int">
        SELECT count(cfu.id)
        FROM crm_follow_up cfu
        LEFT JOIN crm_entry_record cer ON cer.follow_id = cfu.id
        WHERE cfu.project_id = #{projectId} AND cfu.del_flag = 0 AND cer.id is not null AND cer.status in (0, 2)
    </select>

    <!-- 获取入职记录 通过跟进人 -->
    <select id="getEntryRecordByCreateBy" resultMap="resultMap">
        SELECT id, enterprise_id, follow_id, status, auth, estimate_time, behavior_time, remark, refuse, create_by, create_time, update_by, update_time
        FROM crm_entry_record
        WHERE enterprise_id = #{enterpriseId} AND follow_id = #{followId} AND create_by = #{createBy}
    </select>

    <!-- 离职更新 入职记录  对接人 -->
    <update id="quitUpdateEntryRecordCreateBy">
        UPDATE crm_entry_record
        SET create_by = #{toUserId}, update_time = #{updateTime}
        WHERE enterprise_id = #{enterpriseId} AND create_by = #{fromUserId}
        AND follow_id IN
        <foreach collection="followIds" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </update>

    <!-- 获取入职记录  通过跟进id -->
    <select id="getEntryRecordByFollowUpId" resultMap="resultMap">
        SELECT id, enterprise_id, follow_id, status, auth, behavior_time, estimate_time, create_by, remark, refuse
        from crm_entry_record
        where follow_id = #{followUpId}
    </select>

    <!-- 个人数据 入职量 -->
    <select id="getIndividualEntryData" resultMap="voResultMap">
        SELECT cer.id, cp.id projectId, cer.follow_id, cer.`estimate_time`, cer.`behavior_time`, cc.id contactId, cc.name contactName,
        cc.phone contactPhone, cp.title, cu.name userName, cer.auth, cer.status
        FROM crm_entry_record cer
        left join crm_follow_up cfu on cfu.id = cer.follow_id
        left join crm_contact cc on cfu.contact_id = cc.id
        left join crm_project cp on cp.id = cfu.`project_id`
        left join crm_user cu on cu.id = cer.create_by
        <where>
            cer.behavior_time &gt;= #{beginTime} and cer.behavior_time &lt;= #{endTime}
            <if test="createBySet != null and createBySet.size > 0">
                and cer.create_by in
                <foreach collection="createBySet" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="createBySet == null or createBySet.size == 0">
                and cer.create_by = #{userId}
            </if>
        </where>
        ORDER BY cer.estimate_time desc
        limit #{pageBegin}, #{pageSize}
    </select>

    <!-- 个人数据 入职量 数量 -->
    <select id="getIndividualEntryDataCount" resultType="int">
        SELECT count(cer.id)
        FROM crm_entry_record cer
        left join crm_follow_up cfu on cfu.id = cer.follow_id
        left join crm_contact cc on cfu.contact_id = cc.id
        left join crm_project cp on cp.id = cfu.`project_id`
        left join crm_user cu on cu.id = cer.create_by
        <where>
            cer.behavior_time &gt;= #{beginTime} and cer.behavior_time &lt;= #{endTime}
            <if test="createBySet != null and createBySet.size > 0">
                and cer.create_by in
                <foreach collection="createBySet" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="createBySet == null or createBySet.size == 0">
                and cer.create_by = #{userId}
            </if>
        </where>
    </select>


    <select id="getRevokeEntryList" resultType="com.ssb.approve.model.vo.FollowRevokeVO">
        select
            cer.id,
            cer.estimate_time behaviorTime,
            cc.id contactId,
            cp.title,
            cc.name contactName,
            cc.phone contactPhone,
            (case when cer.status = 1 then '已入职' when cer.status = 3 then '拒绝入职' end) result,
            cu.name userName,
            cc.type,
            cfu.id followId,
            cp.id projectId
        from crm_entry_record cer
        left join crm_follow_up cfu on cfu.id = cer.`follow_id`
        left join crm_project cp on cfu.`project_id` = cp.id
        left join crm_contact cc on cc.id = cfu.`contact_id`
        left join crm_user cu on cu.id = cfu.create_by
        <where>
            (
                cer.enterprise_id = #{enterpriseId} and cp.share_project_id is null
                or
                (cp.share_project_id in (select id from crm_project where enterprise_id = #{enterpriseId} and share_status = 1))
            )
            and (cer.status = 3
            or (cer.status = 1 and exists(select 0 from crm_settle_details where follow_id = cfu.id and status = 0 and del_flag = 0 group by follow_id having count(follow_id) = (select count(*) from crm_settle_details where follow_id = cfu.id and del_flag = 0)))
            )
            <if test="null != range and range == 1">
                AND cp.master = #{userId}
            </if>
            <!-- 普通检索 -->
            <if test="null != searchType and searchType != ''">
                <if test="searchType == 'contactName'">
                    and cc.name LIKE CONCAT('%', #{search},'%')
                </if>
                <if test="searchType == 'contactPhone'">
                    and cc.phone LIKE CONCAT('%', #{search},'%')
                </if>
            </if>
            <if test="null != title">
                and cp.title LIKE CONCAT('%', #{title},'%')
            </if>
            <if test="null != beginTime and null != endTime">
                and cer.estimate_time &gt;= #{beginTime} and cer.estimate_time &lt;= #{endTime}
            </if>
        </where>
        ORDER BY cer.estimate_time desc
        limit #{pageBegin}, #{pageSize}
    </select>

    <select id="getRevokeEntryListCount" resultType="java.lang.Integer">
        select
            count(*)
        from crm_entry_record cer
        left join crm_follow_up cfu on cfu.id = cer.`follow_id`
        left join crm_project cp on cfu.`project_id` = cp.id
        left join crm_contact cc on cc.id = cfu.`contact_id`
        left join crm_user cu on cu.id = cfu.create_by
        <where>
            cer.enterprise_id = #{enterpriseId}
            and (cer.status = 3
            or (cer.status = 1 and exists(select 0 from crm_settle_details where follow_id = cfu.id and status = 0 and del_flag = 0 group by follow_id having count(follow_id) = (select count(*) from crm_settle_details where follow_id = cfu.id and del_flag = 0)))
            )
            <if test="null != range and range == 1">
                AND cp.master = #{userId}
            </if>
            <!-- 普通检索 -->
            <if test="null != searchType and searchType != ''">
                <if test="searchType == 'contactName'">
                    and cc.name LIKE CONCAT('%', #{search},'%')
                </if>
                <if test="searchType == 'contactPhone'">
                    and cc.phone LIKE CONCAT('%', #{search},'%')
                </if>
            </if>
            <if test="null != title">
                and cp.title LIKE CONCAT('%', #{title},'%')
            </if>
            <if test="null != beginTime and null != endTime">
                and cer.estimate_time &gt;= #{beginTime} and cer.estimate_time &lt;= #{endTime}
            </if>
        </where>
    </select>

    <select id="existEntryRecordByFollowId" parameterType="java.lang.Integer" resultType="java.lang.Integer">
        select count(*) from crm_entry_record where follow_id = #{followId}
    </select>

    <delete id="deleteEntryRecordByFollowId" parameterType="java.lang.Integer">
        delete from crm_entry_record where follow_id = #{followId}
    </delete>

    <update id="updateRevokeEntryRecord">
        UPDATE crm_entry_record
        <set>
            `status` = #{status},
            auth = #{auth},
            behavior_time = null,
            remark = null,
            refuse = null,
            update_by = #{updateBy},
            update_time = #{updateTime},
        </set>
        WHERE id = #{id}
    </update>

    <!-- 获取入职确认列表 -->
    <select id="getEnterpriseProjectEntryAuthList" resultMap="voResultMap">
        SELECT cer.id, cp.id projectId, cer.follow_id, cer.`estimate_time`, cer.`behavior_time`, cc.id contactId, cc.name contactName, cc.phone contactPhone, cp.title, cu.name userName, cer.auth, cer.status,
               cc.type, cer.remark, cer.review_time, cer.review_user_id,
               (select name from crm_user where id = cer.review_user_id) as reviewUserName
        FROM crm_entry_record cer
        left join crm_follow_up cfu on cfu.id = cer.follow_id
        left join crm_contact cc on cfu.contact_id = cc.id
        left join crm_project cp on cp.id = cfu.`project_id`
        left join crm_user cu on cu.id = cer.update_by and cu.enterprise_id = #{enterpriseId}
        <where>
            cfu.project_id = #{projectId} and not (cer.estimate_time is null and cer.`status` = 0)
            <if test="contactName != null ">
                and cc.name LIKE CONCAT('%', #{contactName},'%')
            </if>
            <if test="followUpUser != null ">
                and cu.name LIKE CONCAT('%', #{followUpUser},'%')
            </if>
            <!-- 已入职 -->
            <if test="entryStatus == 1">
                and cer.status = 1
            </if>
            <!-- 拒绝入职 -->
            <if test="entryStatus == 2">
                and cer.status = 3
            </if>
            <!-- 待入职 -->
            <if test="entryStatus == 3">
                and cer.status in (0, 2)
            </if>
            <!-- 已中断 -->
            <if test="entryStatus == 4">
                and cer.status = 4
            </if>
            and cfu.del_flag = 0
            <if test="null != beginTime and null != endTime">
                <if test="timeType != null and timeType == 1">
                    and cer.`estimate_time` &gt;= #{beginTime} and cer.`estimate_time` &lt;= #{endTime}
                </if>
                <if test="timeType != null and timeType == 2">
                    and cer.`behavior_time` &gt;= #{beginTime} and cer.`behavior_time` &lt;= #{endTime}
                </if>
                <if test="timeType != null and timeType == 3">
                    and cer.`review_time` &gt;= #{beginTime} and cer.`review_time` &lt;= #{endTime}
                </if>
            </if>
            <if test="null != authStatus">
                and cir.auth = #{authStatus}
            </if>
        </where>
        ORDER BY cer.estimate_time
        limit #{pageBegin}, #{pageSize}
    </select>

    <!-- 获取入职确认数量 -->
    <select id="getEnterpriseProjectEntryAuthCount" resultType="int">
        SELECT count(cer.id)
        FROM crm_entry_record cer
        left join crm_follow_up cfu on cfu.id = cer.follow_id
        left join crm_contact cc on cfu.contact_id = cc.id
        left join crm_project cp on cp.id = cfu.`project_id`
        left join crm_user cu on cu.id = cer.update_by and cu.enterprise_id = #{enterpriseId}
        <where>
            cfu.project_id = #{projectId} and not (cer.estimate_time is null and cer.`status` = 0)
            <if test="contactName != null ">
                and cc.name LIKE CONCAT('%', #{contactName},'%')
            </if>
            <if test="followUpUser != null ">
                and cu.name LIKE CONCAT('%', #{followUpUser},'%')
            </if>
            <!-- 已入职 -->
            <if test="entryStatus == 1">
                and cer.status = 1
            </if>
            <!-- 拒绝入职 -->
            <if test="entryStatus == 2">
                and cer.status = 3
            </if>
            <!-- 待入职 -->
            <if test="entryStatus == 3">
                and cer.status in (0, 2)
            </if>
            <!-- 已中断 -->
            <if test="entryStatus == 4">
                and cer.status = 4
            </if>
            and cfu.del_flag = 0
            <if test="null != beginTime and null != endTime">
                <if test="timeType != null and timeType == 1">
                    and cer.`estimate_time` &gt;= #{beginTime} and cer.`estimate_time` &lt;= #{endTime}
                </if>
                <if test="timeType != null and timeType == 2">
                    and cer.`behavior_time` &gt;= #{beginTime} and cer.`behavior_time` &lt;= #{endTime}
                </if>
                <if test="timeType != null and timeType == 3">
                    and cer.`review_time` &gt;= #{beginTime} and cer.`review_time` &lt;= #{endTime}
                </if>
            </if>
            <if test="null != authStatus">
                and cir.auth = #{authStatus}
            </if>
        </where>
    </select>

    <select id="getEntryData" parameterType="com.ssb.approve.model.dto.RecruitmentDataDTO"
            resultType="com.ssb.approve.model.vo.StatisticalChartDataVO">
        SELECT
            count(*) as count,
	        DATE_FORMAT(behavior_time, '%m-%d') AS date
        FROM
            crm_entry_record
        WHERE
            <if test="type == 1">
                create_by = #{userId}
            </if>
            <if test="type == 2">
                create_by in (
                <foreach collection="userIdList" item="userId" separator=",">
                    #{userId}
                </foreach>
                )
            </if>
          AND STATUS = 1
          AND behavior_time BETWEEN #{timeStart} and #{timeEnd}
        GROUP BY
            DATE_FORMAT(behavior_time, '%m-%d')
        ORDER BY
            date;
    </select>

    <select id="getEntryCount" parameterType="com.ssb.approve.model.dto.ConversionRateDataDTO" resultType="java.lang.Integer">
        SELECT
            count(*) as count
        FROM
            crm_entry_record cer
            inner join crm_interview_record cir on cer.follow_id = cir.follow_id
        WHERE
            <if test="type == 1">
                cir.create_by = #{userId}
            </if>
            <if test="type == 2">
                cir.create_by in (
                <foreach collection="userIdList" item="userId" separator=",">
                    #{userId}
                </foreach>
                )
            </if>
          AND cer.STATUS = 1
          AND cir.behavior_time between #{timeStart} and #{timeEnd}
    </select>

    <select id="getEntryDataList" parameterType="com.ssb.approve.model.dto.RecruitmentDataListDTO"
            resultType="com.ssb.approve.model.vo.RecruitmentDataListVO">
        SELECT
            cc.id contactId,
            cc.name contactName,
            cc.phone phone,
            cp.id projectId,
            cp.title,
            ccu.id customerId,
            ccu.`name` customerName,
            cer.behavior_time entryTime,
            cu.id followUserId,
            cu.name followUserName,
            cp.source
        FROM
            crm_follow_up cfu
                INNER JOIN crm_entry_record cer ON cfu.id = cer.follow_id
                INNER JOIN crm_user cu ON cu.id = cfu.create_by
                INNER JOIN crm_contact cc on cc.id = cfu.contact_id
                INNER JOIN crm_project cp on cfu.project_id = cp.id
                INNER JOIN crm_customer ccu on ccu.id = cp.customer_id
        WHERE
            <if test="type == 1">
                cfu.create_by = #{userId}
            </if>
            <if test="type == 2">
                cfu.create_by in (
                <foreach collection="userIdList" item="userId" separator=",">
                    #{userId}
                </foreach>
                )
            </if>
            AND cu.del_flag = 0
            AND cfu.del_flag = 0
            AND cer.status = 1
            <if test="userName != null">
                AND cc.name like CONCAT('%', #{userName},'%')
            </if>
            <if test="phone != null">
                AND cc.phone like CONCAT('%', #{phone},'%')
            </if>
            <if test="title != null">
                AND cp.title like CONCAT('%', #{title},'%')
            </if>
            <if test="customerName != null">
                AND ccu.name like CONCAT('%', #{customerName},'%')
            </if>
            <if test="followUser != null">
                AND cu.name like CONCAT('%', #{followUser},'%')
            </if>
            <if test="timeStart != null and timeEnd != null">
                AND cer.behavior_time between #{timeStart} and #{timeEnd}
            </if>
        ORDER BY cer.behavior_time desc
        limit #{pageBegin}, #{pageSize}
    </select>

    <select id="getEntryDataCount" parameterType="com.ssb.approve.model.dto.RecruitmentDataListDTO"
            resultType="java.lang.Integer">
        SELECT
            count(*)
        FROM
            crm_follow_up cfu
                INNER JOIN crm_entry_record cer ON cfu.id = cer.follow_id
                INNER JOIN crm_user cu ON cu.id = cfu.create_by
                INNER JOIN crm_contact cc on cc.id = cfu.contact_id
                INNER JOIN crm_project cp on cfu.project_id = cp.id
                INNER JOIN crm_customer ccu on ccu.id = cp.customer_id
        WHERE
            <if test="type == 1">
                cfu.create_by = #{userId}
            </if>
            <if test="type == 2">
                cfu.create_by in (
                <foreach collection="userIdList" item="userId" separator=",">
                    #{userId}
                </foreach>
                )
            </if>
            AND cu.del_flag = 0
            AND cfu.del_flag = 0
            AND cer.status = 1
            <if test="userName != null">
                AND cc.name like CONCAT('%', #{userName},'%')
            </if>
            <if test="phone != null">
                AND cc.phone like CONCAT('%', #{phone},'%')
            </if>
            <if test="title != null">
                AND cp.title like CONCAT('%', #{title},'%')
            </if>
            <if test="customerName != null">
                AND ccu.name like CONCAT('%', #{customerName},'%')
            </if>
            <if test="followUser != null">
                AND cu.name like CONCAT('%', #{followUser},'%')
            </if>
            <if test="timeStart != null and timeEnd != null">
                AND cer.behavior_time between #{timeStart} and #{timeEnd}
            </if>
    </select>

    <select id="getDataStatisticsEntryCount" resultType="java.lang.Integer">
        SELECT
            count(*)
        FROM
            crm_entry_record
        WHERE
            create_by IN (
                <foreach collection="dto.userIdList" item="userId" separator=",">
                    #{userId}
                </foreach>
                )
            <if test="dto.timeType != 0">
                <choose>
                    <when test="sourceType == 1">
                        AND behavior_time between #{dto.timeStart} and #{dto.timeEnd}
                    </when>
                    <when test="sourceType == 2">
                        AND behavior_time between #{dto.compareTimeStart} and #{dto.compareTimeEnd}
                    </when>
                </choose>
            </if>
          AND status = 1
    </select>
    <select id="getEnterpriseDataStatisticsEntryCount" resultType="java.lang.Integer">
        SELECT
            count(*)
        FROM
            crm_entry_record cen
            INNER JOIN crm_follow_up cfu ON cfu.id = cen.follow_id
            INNER JOIN crm_project cp ON cfu.project_id = cp.id
            INNER JOIN crm_customer cc ON cp.customer_id = cc.id
        WHERE cc.customer_enterprise_id =  #{dto.enterpriseId}
        <if test="dto.timeType != 0">
            AND cen.behavior_time between #{dto.timeStart} and #{dto.timeEnd}
        </if>
        AND cen.status = 1
    </select>

    <select id="getWaitingForEntryList" resultType="com.ssb.approve.model.vo.EntryRecordVO">
        select t1.id, t2.project_id projectId, t2.contact_id contactId, t2.update_by updateBy, t1.behavior_time behaviorTime,
        (select title from crm_project where id = t2.project_id) as title,
        (select name from crm_user where id = t2.contact_id) as contactName,
        (select name from crm_user where id = t2.update_by) as userName
        from crm_entry_record t1
        left join crm_follow_up t2 on t1.follow_id = t2.id
        where
        t1.id in
        <foreach collection="ids" item="ite" separator="," close=")" open="(">
            #{item}
        </foreach>
        <if test="null != maxId and maxId != 0">
            and t1.id <![CDATA[ < ]]> #{maxId}
        </if>
        order by t1.id desc
        limit 20
    </select>
</mapper>