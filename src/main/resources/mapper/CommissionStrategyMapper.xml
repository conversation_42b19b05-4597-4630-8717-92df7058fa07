<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ssb.approve.dao.CommissionStrategyDAO">

    <resultMap id="strategyList" type="com.ssb.approve.model.vo.CommissionStrategyVO">
        <result property="cityLevel" column="city_level"/>
        <result property="newProjectTime" column="new_project_time"/>
        <result property="followUpCompletedCount" column="follow_up_completed_count"/>
    </resultMap>
    <select id="getStrategyList" parameterType="com.ssb.approve.model.QueryCommissionStrategyModel" resultMap="strategyList">
        SELECT
            ccs.id,
            ccs.title,
            cu.name userName,
            ccs.create_time createTime,
            0 status,
            ccs.quota,
            ccs.city_level,
            ccs.new_project_time,
            ccs.follow_up_completed_count,
            ccs.divisor
        FROM
            crm_commission_strategy ccs
            INNER JOIN crm_user cu ON ccs.create_by = cu.id
        WHERE ccs.enterprise_id = #{enterpriseId}
        <if test="null != cityLevel">
            and ccs.city_level = #{cityLevel}
        </if>
        <if test="null != title">
            and ccs.title = #{title}
        </if>
        and ccs.del_flag = 0 order by ccs.create_time desc limit #{pageBegin}, #{pageSize}
    </select>

    <select id="getStrategyListCount" parameterType="com.ssb.approve.model.QueryCommissionStrategyModel" resultType="java.lang.Integer">
        SELECT
          count(*)
        FROM
          crm_commission_strategy ccs
          INNER JOIN crm_user cu ON ccs.create_by = cu.id
        WHERE ccs.enterprise_id = #{enterpriseId}
        <if test="null != cityLevel">
            and ccs.city_level = #{cityLevel}
        </if>
        <if test="null != title">
            and ccs.title = #{title}
        </if>
        and ccs.del_flag = 0
    </select>

    <insert id="insertStrategy" parameterType="com.ssb.approve.entity.CommissionStrategy" useGeneratedKeys="true" keyColumn="id" keyProperty="id">
        insert into crm_commission_strategy
        (enterprise_id, city_level, title, quota, new_project_time, follow_up_completed_count, divisor, type, create_by, create_time, update_by, update_time, del_flag)
        values (#{enterpriseId}, #{cityLevel}, #{title}, #{quota}, #{newProjectTime}, #{followUpCompletedCount}, #{divisor}, #{type}, #{createBy}, now(), #{createBy}, now(), 0)
    </insert>

    <update id="updateStrategy" parameterType="com.ssb.approve.model.CommissionStrategyModel">
        update crm_commission_strategy
        <set>
            <if test="null != cityLevel">
                city_level = #{cityLevel},
            </if>
            <if test="null != title">
                title = #{title},
            </if>
            <if test="null != quota">
                quota = #{quota},
            </if>
            <if test="null != newProjectTime">
                new_project_time = #{newProjectTime},
            </if>
            <if test="null != followUpCompletedCount">
                follow_up_completed_count = #{followUpCompletedCount},
            </if>
            <if test="null != divisor">
                divisor = #{divisor},
            </if>
            <if test="null != updateBy">
                update_by = #{updateBy},
            </if>
            <if test="null != delFlag">
                del_flag = #{delFlag},
            </if>
            <if test="null != type">
                type = #{type},
            </if>
            update_time = now()
        </set>
        where id = #{id}
    </update>

    <resultMap id="strategyInfo" type="com.ssb.approve.model.vo.CommissionStrategyVO">
        <result column="id" property="id"/>
        <result column="city_level" property="cityLevel"/>
        <result column="new_project_time" property="newProjectTime"/>
        <result column="follow_up_completed_count" property="followUpCompletedCount"/>
        <collection property="ratioList" column="id"
                     select="com.ssb.approve.dao.CommissionStrategyRatioDAO.getRatioList"/>
        <collection property="salesCommissionCutList" column="id"
                    select="com.ssb.approve.dao.SalesCommissionCutDAO.getSalesCommissionCutList"/>
     </resultMap>

    <select id="getStrategyInfo" parameterType="java.lang.Integer" resultMap="strategyInfo">
        SELECT
            ccs.id,
            ccs.city_level,
            ccs.title,
            ccs.new_project_time,
            ccs.follow_up_completed_count,
            ccs.divisor,
            ccs.type
        FROM
            crm_commission_strategy ccs
        WHERE
            ccs.id = #{id}
    </select>

    <select id="getCommissionLevelListV2" parameterType="java.lang.Integer" resultType="com.ssb.approve.entity.ManageCommissionLevel">
        select id,level,level_name levelName,0 type from crm_manage_commission_level where type = 0 or type = 2
        union all
        select id,level,level_name levelName,1 type from crm_manage_commission_level where type = 1 or type = 2
        union all
        select id,level,level_name levelName,2 type from crm_manage_commission_level where type = 3 and level = 20
        union all
        select id,level,level_name levelName,3 type from crm_manage_commission_level where type = 3 and level = 25
    </select>

    <select id="getStrategyRatioAndContractStartTime" parameterType="java.lang.Integer" resultType="java.util.Map">
        SELECT
            ccsr.base,
            ccsr.high,
            cp.contract_start_time contractStartTime,
            ccs.new_project_time newProjectTime,
            ccs.follow_up_completed_count followUpCompletedCount
        FROM
            crm_commission_strategy ccs
            INNER JOIN crm_commission_strategy_ratio ccsr ON ccsr.strategy_id = ccs.id
            INNER JOIN crm_project cp ON cp.strategy_id = ccs.id
        WHERE
            cp.id = #{projectId}
            AND ccsr.type = 0
            AND ccsr.commission_level_id = 2
            AND ccsr.del_flag = 0
            LIMIT 1
    </select>

    <select id="getAllStrategyId" resultType="java.lang.Integer">
        select id from crm_commission_strategy where del_flag = 0
    </select>
</mapper>