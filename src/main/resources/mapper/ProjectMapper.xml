<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ssb.approve.dao.ProjectDAO">

    <select id="getProjectByCustomerList" resultType="java.lang.Integer">
        SELECT
        cp.id
        FROM
        crm_project cp
        WHERE cp.share_project_id IS NULL AND cp.customer_id in
        <foreach collection="customerList" item="customerId" open="(" separator="," close=")">
            #{customerId}
        </foreach>
    </select>
</mapper>