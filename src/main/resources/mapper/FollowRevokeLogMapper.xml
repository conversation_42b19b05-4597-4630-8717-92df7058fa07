<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ssb.approve.dao.FollowRevokeLogDAO">

    <insert id="insertFollowRevokeLog" parameterType="com.ssb.approve.entity.FollowRevokeLog">
        insert into crm_follow_revoke_log
            (enterprise_id, follow_id, target_id, type, follow_status, status_before_revocation, status_after_revocation,
            <if test="node != null">
                node,
            </if>
            <if test="revokeRemark != null">
                revoke_remark,
            </if>
            revoke_by, revoke_time)
         values (#{enterpriseId}, #{followId}, #{targetId}, #{type}, #{followStatus}, #{statusBeforeRevocation}, #{statusAfterRevocation},
            <if test="node != null">
                #{node},
            </if>
            <if test="revokeRemark != null">
                #{revokeRemark},
            </if>
            #{revokeBy}, #{revokeTime})
    </insert>

    <select id="getFollowRevokeStatisticsList" parameterType="com.ssb.approve.model.QueryFollowRevokeStatisticsModel" resultType="com.ssb.approve.model.vo.FollowRevokeStatisticsVO">
        SELECT
            cir.behavior_time behaviorTime,
            cp.title,
            cc.name contactName,
            cc.phone contactPhone,
            cfrl.type,
            cfrl.status_before_revocation statusBeforeRevocation,
            (select name from crm_user where id = cfu.create_by)userName,
            cfrl.revoke_remark revokeRemark,
            cfrl.revoke_time revokeTime,
            cu.name revokeName
        FROM
            crm_follow_revoke_log cfrl
            INNER JOIN crm_follow_up cfu ON cfrl.follow_id = cfu.id
            INNER JOIN crm_contact cc on cc.id = cfu.contact_id
            INNER JOIN crm_project cp on cp.id = cfu.project_id
            INNER JOIN crm_user cu on cu.id = cfrl.revoke_by
            LEFT JOIN crm_interview_record cir on cir.follow_id = cfu.id
         <where>
            (
                (cfrl.enterprise_id = #{enterpriseId} and cp.share_project_id is null)
                or
                (cp.share_project_id in (select id from crm_project where enterprise_id = #{enterpriseId} and share_status = 1))
            )
            <if test="revokeUser != null">
                and cu.name LIKE CONCAT('%', #{revokeUser},'%')
            </if>
             <if test="null != searchType and searchType != ''">
                 <if test="searchType == 'title'">
                     and cp.title LIKE CONCAT('%', #{search},'%')
                 </if>
                 <if test="searchType == 'contactName'">
                     and cc.name LIKE CONCAT('%', #{search},'%')
                 </if>
                 <if test="searchType == 'contactPhone'">
                     and cc.phone LIKE CONCAT('%', #{search},'%')
                 </if>
             </if>
             <if test="null != beginTime and null != endTime">
                 and cfrl.revoke_time between #{beginTime} and #{endTime}
             </if>
         </where>
        order by cfrl.revoke_time desc
        limit #{pageBegin}, #{pageSize}
    </select>

    <select id="getFollowRevokeStatisticsCount" parameterType="com.ssb.approve.model.QueryFollowRevokeStatisticsModel" resultType="java.lang.Integer">
        SELECT
          count(*)
        FROM
            crm_follow_revoke_log cfrl
            INNER JOIN crm_follow_up cfu ON cfrl.follow_id = cfu.id
            INNER JOIN crm_contact cc on cc.id = cfu.contact_id
            INNER JOIN crm_project cp on cp.id = cfu.project_id
            INNER JOIN crm_user cu on cu.id = cfrl.revoke_by
            LEFT JOIN crm_interview_record cir on cir.follow_id = cfu.id
        <where>
            (
                (cfrl.enterprise_id = #{enterpriseId} and cp.share_project_id is null)
                or
                (cp.share_project_id in (select id from crm_project where enterprise_id = #{enterpriseId} and share_status = 1))
            )
            <if test="revokeUser != null">
                and cu.name LIKE CONCAT('%', #{revokeUser},'%')
            </if>
            <if test="null != searchType and searchType != ''">
                <if test="searchType == 'title'">
                    and cp.title LIKE CONCAT('%', #{search},'%')
                </if>
                <if test="searchType == 'contactName'">
                    and cc.name LIKE CONCAT('%', #{search},'%')
                </if>
                <if test="searchType == 'contactPhone'">
                    and cc.phone LIKE CONCAT('%', #{search},'%')
                </if>
            </if>
            <if test="null != beginTime and null != endTime">
                and cfrl.revoke_time between #{beginTime} and #{endTime}
            </if>
        </where>
    </select>

</mapper>