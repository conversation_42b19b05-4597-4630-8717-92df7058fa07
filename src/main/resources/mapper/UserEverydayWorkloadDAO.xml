<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ssb.approve.dao.UserEverydayWorkloadDAO">

    <select id="getDailyData" resultType="com.ssb.approve.model.vo.UserEverydayWorkloadVO"
            parameterType="com.ssb.approve.model.dto.DailyDataDTO">
        SELECT
        IFNULL(SUM(cuew.call_count),0) callCount,
        IFNULL(SUM(cuew.wechat_count),0) wechatCount,
        IFNULL(SUM(cuew.contract_count),0) contractCount
        FROM
        crm_user_everyday_workload cuew
        INNER JOIN crm_user cu ON cu.id = cuew.user_id
        where cu.status=0
        and cu.id in (
        <foreach collection="userIdList" item="userId" separator=",">
            #{userId}
        </foreach>
        )
        <if test="yesterday==1">
            and DATE(cuew.statistics_date) = CURDATE()
        </if>
        <if test="yesterday==2">
            and DATE(cuew.statistics_date) = CURDATE() - INTERVAL 1 DAY
        </if>
    </select>
</mapper>
