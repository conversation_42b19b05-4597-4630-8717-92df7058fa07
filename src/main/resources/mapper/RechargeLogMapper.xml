<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ssb.approve.dao.RechargeLogDAO">

    <!-- 保存充值记录 -->
    <insert id="save" parameterType="com.ssb.approve.entity.RechargeLog" useGeneratedKeys="true" keyColumn="id" keyProperty="id">
        INSERT INTO crm_recharge_log(enterprise_id, project_id, customer_id, contract_id, type, salary, create_by, create_time, remark, transaction_type)
        VALUES (#{enterpriseId}, #{projectId}, #{customerId}, #{contractId}, #{type}, #{salary}, #{createBy}, #{createTime}, #{remark}, #{transactionType})
    </insert>

    <!-- 获取剩余金额 -->
    <select id="getCurrentAmount" resultType="int">
        select sum(salary)
        from crm_recharge_log
        where project_id = #{projectId}
    </select>
</mapper>