<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ssb.approve.dao.FollowUpDAO">

    <resultMap id="voResultMap" type="com.ssb.approve.model.vo.FollowUpVO">
        <result column="enterprise_id" property="enterpriseId"/>
        <result column="project_id" property="projectId"/>
        <result column="current_status" property="currentStatus"/>
        <result column="contact_id" property="contactId"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="behavior_time" property="behaviorTime"/>
        <result column="estimate_time" property="estimateTime"/>
        <result column="settle_time" property="settleTime"/>
        <result column="entry_time" property="entryTime"/>
        <result column="interview_type" property="interviewType"/>
        <result column="tmp_status" property="tmpStatus"/>
        <result column="leave_time" property="leaveTime"/>
        <result column="leave_cause" property="leaveCause"/>
    </resultMap>

    <resultMap id="voResultMapHistory" type="com.ssb.approve.model.vo.FollowUpVO" extends="voResultMap">
        <result column="id" property="id"/>
        <association property="performance" column="id" select="com.ssb.approve.dao.SettleLogDAO.getAllPerformanceByFollowId"/>
    </resultMap>

    <resultMap id="voFollowUpDetails" type="com.ssb.approve.model.vo.FollowUpVO" extends="voResultMap">
        <result column="id" property="id"/>
        <association property="interviewRecord" column="id" select="com.ssb.approve.dao.InterviewRecordDAO.getInterviewRecordByFollowUpId"/>
        <association property="entryRecord" column="id" select="com.ssb.approve.dao.EntryRecordDAO.getEntryRecordByFollowUpId"/>
        <association property="settleDetails" column="id" select="com.ssb.approve.dao.SettleDetailsDAO.getSettleDetailsByFollowId"/>
    </resultMap>

    <!-- 获取跟进信息 -->
    <select id="getFollowUpById" resultType="com.ssb.approve.entity.FollowUp">
        SELECT id, enterprise_id, contact_id, project_id, `status`, current_status, finish, create_by, create_time, update_by, update_time,
               (select source from crm_project where id = project_id) projectSource
        FROM crm_follow_up where id = #{id} and del_flag = 0
    </select>

    <!-- 检测跟进 -->
    <select id="checkFollowUp" resultType="int">
        select count(id)
        from crm_follow_up
        where contact_id = #{contactId} and project_id = #{projectId} and finish = #{finish} and del_flag = 0
    </select>

    <!-- 获取检测的跟进详情 -->
    <select id="getCheckFollowUp" resultMap="voResultMap">
        SELECT cfu.id, cu.`name` userName, cd.`name` deptName
        FROM crm_follow_up cfu
        left join crm_user cu on cfu.create_by = cu.id
        left join crm_dept cd on cd.id = cu.dept_id
        where cfu.contact_id = #{contactId} and cfu.project_id = #{projectId} and cfu.finish = #{finish} and cfu.del_flag = 0
    </select>

    <select id="getCheckFollowUpByPhone" resultMap="voResultMap">
        SELECT cfu.id, cu.`name` userName, cd.`name` deptName
        FROM crm_follow_up cfu
        INNER JOIN crm_contact cc on cc.id = cfu.contact_id
        left join crm_user cu on cfu.create_by = cu.id
        left join crm_dept cd on cd.id = cu.dept_id
        where cc.phone = (select phone from crm_contact where id = #{contactId}) and cfu.project_id = #{projectId} and cfu.finish = #{finish} and cfu.del_flag = 0
    </select>

    <!-- 保存项目跟进 -->
    <insert id="save" parameterType="com.ssb.approve.entity.FollowUp" useGeneratedKeys="true" keyColumn="id"
            keyProperty="id">
        INSERT INTO crm_follow_up (enterprise_id, contact_id, project_id, `status`, current_status, create_by, create_time, update_by, update_time, remark)
        VALUES (#{enterpriseId}, #{contactId}, #{projectId}, #{status}, #{currentStatus}, #{createBy}, now(), #{updateBy}, now(), #{remark})
    </insert>

    <!-- 获取跟进列表信息 -->
    <select id="getFollowUpList" parameterType="com.ssb.approve.model.QueryFollowUpModel" resultMap="voResultMap">
        select cfu.id, cfu.contact_id, cc.name contactName, cc.phone contactPhone, cfu.project_id, cp.title, cfu.update_time, cu.name userName, cc.type,
        cp.full_name fullName, cp.interview_upper_limit interviewUpperLimit,
        (select name from crm_position_v2 where id = cp.position2_first) positionFirstName,
        (select name from crm_position_v2 where id = cp.position2_second) positionSecondName,
        cfu.id followUpId
        from crm_follow_up cfu
        left join crm_contact cc on cc.id = cfu.`contact_id`
        left join crm_project cp on cp.id = cfu.`project_id`
        left join crm_user cu on cu.id = cfu.`create_by`
        where cfu.status = #{status} and cfu.finish = #{finish} and cfu.enterprise_id = #{enterpriseId} and cfu.create_by = #{userId}
        <!--<foreach collection="createBySet" index="index" item="item" open="(" separator="," close=")">-->
            <!--#{item}-->
        <!--</foreach>-->
        order by cfu.update_time desc
        limit #{pageBegin}, #{pageSize}
    </select>

    <!-- 获取列表总数量 -->
    <select id="getFollowUpCount" resultType="int">
        SELECT count(*)
        from crm_follow_up cfu
        left join crm_contact cc on cc.id = cfu.`contact_id`
        left join crm_project cp on cp.id = cfu.`project_id`
        left join crm_user cu on cu.id = cfu.`create_by`
        where cfu.status = #{status} and cfu.finish = #{finish} and cfu.enterprise_id = #{enterpriseId} and cfu.create_by = #{userId}
    </select>

    <!-- 更新跟进状态 -->
    <update id="updateFollowUp">
        UPDATE crm_follow_up
        <set>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="currentStatus != null">
                current_status = #{currentStatus},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="finish != null">
                finish = #{finish},
            </if>
        </set>
        WHERE id = #{id}
    </update>

    <!-- 获取已约待面试列表 -->
    <select id="getInterviewList" parameterType="com.ssb.approve.model.QueryFollowUpModel" resultMap="voResultMap">
        select cir.id, cfu.contact_id, cir.behavior_time, cp.title, cp.interview_type, cc.name contactName, cc.phone contactPhone, cu.name userName, cir.node, cir.status,
            cfu.project_id, cp.interview_upper_limit interviewUpperLimit, cir.auth, cfu.id followUpId, cc.type, cir.remark
        from crm_interview_record cir
        left join crm_follow_up cfu on cfu.id = cir.`follow_id`
        left join crm_project cp on cfu.`project_id` = cp.id
        left join crm_contact cc on cc.id = cfu.`contact_id`
        left join crm_user cu on cu.id = cfu.create_by
        WHERE cir.enterprise_id = #{enterpriseId}
          <choose>
              <when test="userIdList != null">
                  AND cir.create_by in (
                  <foreach collection="userIdList" item="userId" separator=",">
                      #{userId}
                  </foreach>
                  )
              </when>
              <otherwise>
                  AND cir.create_by = #{userId}
              </otherwise>
          </choose>
          and cfu.del_flag = 0
        <!-- 项目名称 -->
        <if test="title != null and title != ''">
            and cp.title like CONCAT('%', #{title},'%')
        </if>
        <!-- 项目 -->
        <if test="projectId != null">
            and cfu.project_id = #{projectId}
        </if>
        <!-- 面试时间 -->
        <if test="null != beginTime and null != endTime">
            and cir.behavior_time &gt;= #{beginTime} and cir.behavior_time &lt;= #{endTime}
        </if>
        <!-- 求职者名称 -->
        <if test="null != contactName">
            and cc.`name` LIKE CONCAT('%', #{contactName},'%')
        </if>
        <!-- 待面试 -->
        <if test="interviewStatus == 1">
            and cir.status in (0, 2, 4)
        </if>
        <!-- 未通过 -->
        <if test="interviewStatus == 2">
            and cir.status = 3
        </if>
        <!-- 未参加 -->
        <if test="interviewStatus == 3">
            and cir.status = 1
        </if>
        <!-- 已通过-->
        <if test="interviewStatus == 4">
            and cir.status = 5
        </if>
        ORDER BY cir.behavior_time asc, cir.auth, field(cir.`status`, 0, 2, 4 , 5, 1, 3)
        limit #{pageBegin}, #{pageSize}
    </select>

    <!-- 获取已约待数量 ^^ -->
    <select id="getInterviewCount" resultType="int">
        select count(cir.id)
        from crm_interview_record cir
        left join crm_follow_up cfu on cfu.id = cir.`follow_id`
        left join crm_project cp on cfu.`project_id` = cp.id
        left join crm_contact cc on cc.id = cfu.`contact_id`
        left join crm_user cu on cu.id = cfu.create_by
        WHERE cir.enterprise_id = #{enterpriseId}
            <choose>
                <when test="userIdList != null">
                    AND cir.create_by in (
                    <foreach collection="userIdList" item="userId" separator=",">
                        #{userId}
                    </foreach>
                    )
                </when>
                <otherwise>
                    AND cir.create_by = #{userId}
                </otherwise>
            </choose>
          and cfu.del_flag = 0
        <!-- 项目名称 -->
        <if test="title != null and title != ''">
            and cp.title like CONCAT('%', #{title},'%')
        </if>
        <!-- 项目 -->
        <if test="projectId != null">
            and cfu.project_id = #{projectId}
        </if>
        <!-- 面试时间 -->
        <if test="null != beginTime and null != endTime">
            and cir.behavior_time &gt;= #{beginTime} and cir.behavior_time &lt;= #{endTime}
        </if>
        <!-- 求职者名称 -->
        <if test="null != contactName">
            and cc.`name` LIKE CONCAT('%', #{contactName},'%')
        </if>
        <!-- 待面试 -->
        <if test="interviewStatus == 1">
            and cir.status in (0, 2, 4)
        </if>
        <!-- 未通过 -->
        <if test="interviewStatus == 2">
            and cir.status = 3
        </if>
        <!-- 未参加 -->
        <if test="interviewStatus == 3">
            and cir.status = 1
        </if>
        <!-- 已通过-->
        <if test="interviewStatus == 4">
            and cir.status = 5
        </if>
    </select>

    <!-- 获取发offer列表 -->
    <select id="getOnlyOfferList" parameterType="com.ssb.approve.model.QueryFollowUpModel" resultMap="voResultMap">
        SELECT cer.id, cfu.contact_id, cc.name contactName, cc.phone contactPhone, cp.title,cu.name userName,
        cer.status, cer.auth, cfu.project_id, cir.behavior_time, cfu.id followUpId, cc.type
        FROM crm_entry_record cer
        LEFT JOIN crm_follow_up cfu on cfu.id = cer.follow_id
        LEFT JOIN crm_interview_record cir on cir.follow_id = cer.follow_id
        LEFT JOIN crm_contact cc on cfu.contact_id = cc.id
        LEFT JOIN crm_project cp on cp.id = cfu.`project_id`
        LEFT JOIN crm_user cu on cu.id = cer.create_by
        where cer.enterprise_id = #{enterpriseId}
        <choose>
            <when test="userIdList != null">
                AND cer.create_by in (
                <foreach collection="userIdList" item="userId" separator=",">
                    #{userId}
                </foreach>
                )
            </when>
            <otherwise>
                AND cer.create_by = #{userId}
            </otherwise>
        </choose>
        and cfu.del_flag = 0
        and cer.`status` in (0, 2) and cer.estimate_time is null
        ORDER BY cir.behavior_time asc
        limit #{pageBegin}, #{pageSize}
    </select>

    <!-- 获取发offer列表  数量 -->
    <select id="getOnlyOfferCount" resultType="int">
        SELECT count(cer.id)
        FROM crm_entry_record cer
        LEFT JOIN crm_follow_up cfu on cfu.id = cer.follow_id
        LEFT JOIN crm_interview_record cir on cir.follow_id = cer.follow_id
        LEFT JOIN crm_contact cc on cfu.contact_id = cc.id
        LEFT JOIN crm_project cp on cp.id = cfu.`project_id`
        LEFT JOIN crm_user cu on cu.id = cer.create_by
        where cer.enterprise_id = #{enterpriseId}
        <choose>
            <when test="userIdList != null">
                AND cer.create_by in (
                <foreach collection="userIdList" item="userId" separator=",">
                    #{userId}
                </foreach>
                )
            </when>
            <otherwise>
                AND cer.create_by = #{userId}
            </otherwise>
        </choose>
        and cfu.del_flag = 0
        and cer.`status` in (0, 2) and cer.estimate_time is null
    </select>

    <!-- 获取待入职列表 -->
    <select id="getEntryRecordList" parameterType="com.ssb.approve.model.QueryFollowUpModel" resultMap="voResultMap">
        SELECT cer.id, cfu.contact_id, cer.estimate_time, cer.`behavior_time`, cc.name contactName, cc.phone
        contactPhone, cp.title, cfu.id followUpId, cu.name userName, cer.status, cer.auth, cfu.project_id, cc.type, cer.remark
        FROM crm_entry_record cer
        LEFT JOIN crm_follow_up cfu on cfu.id = cer.follow_id
        LEFT JOIN crm_contact cc on cfu.contact_id = cc.id
        LEFT JOIN crm_project cp on cp.id = cfu.`project_id`
        LEFT JOIN crm_user cu on cu.id = cer.create_by
        WHERE cer.enterprise_id = #{enterpriseId}
        <choose>
            <when test="userIdList != null">
                AND cer.create_by in (
                <foreach collection="userIdList" item="userId" separator=",">
                    #{userId}
                </foreach>
                )
            </when>
            <otherwise>
                AND cer.create_by = #{userId}
            </otherwise>
        </choose>
        and cfu.del_flag = 0 and
        not (cer.estimate_time is null and cer.`status` = 0)
        <!-- 入职结果  1 已入职 2 拒绝入职  3 待入职  -->
        <if test="entryStatus == 1">
            and cer.`status` = 1
        </if>
        <if test="entryStatus == 2">
            and cer.`status` = 3
        </if>
        <if test="entryStatus == 3">
            and cer.`status` in (0, 2)
        </if>
        <!-- 入职项目 -->
        <if test="title != null and title != ''">
            and cp.title like CONCAT('%', #{title},'%')
        </if>
        <!-- 项目 -->
        <if test="projectId != null">
            and cfu.project_id = #{projectId}
        </if>
        <!-- 入职时间 -->
        <if test="null != beginTime and null != endTime">
            and cer.estimate_time &gt;= #{beginTime} and cer.estimate_time &lt;= #{endTime}
        </if>
        <!-- 求职者名称 -->
        <if test="null != contactName">
            and cc.`name` LIKE CONCAT('%', #{contactName},'%')
        </if>
        ORDER BY cer.auth asc, field(cer.`status`, 0, 2, 1, 3)
        limit #{pageBegin}, #{pageSize}
    </select>

    <!-- 获取待入职数量 ^^ -->
    <select id="getEntryRecordCount" resultType="int">
        SELECT count(cer.id)
        FROM crm_entry_record cer
        LEFT JOIN crm_follow_up cfu on cfu.id = cer.follow_id
        LEFT JOIN crm_contact cc on cfu.contact_id = cc.id
        LEFT JOIN crm_project cp on cp.id = cfu.`project_id`
        LEFT JOIN crm_user cu on cu.id = cer.create_by
        WHERE cer.enterprise_id = #{enterpriseId}
        <choose>
            <when test="userIdList != null">
                AND cer.create_by in (
                <foreach collection="userIdList" item="userId" separator=",">
                    #{userId}
                </foreach>
                )
            </when>
            <otherwise>
                AND cer.create_by = #{userId}
            </otherwise>
        </choose>
        and cfu.del_flag = 0 and
        not (cer.estimate_time is null and cer.`status` = 0)
        <!-- 入职结果  1 已入职 2 拒绝入职  3 待入职  -->
        <if test="entryStatus == 1">
            and cer.`status` = 1
        </if>
        <if test="entryStatus == 2">
            and cer.`status` = 3
        </if>
        <if test="entryStatus == 3">
            and cer.`status` in (0, 2)
        </if>
        <!-- 入职项目 -->
        <if test="title != null and title != ''">
            and cp.title like CONCAT('%', #{title},'%')
        </if>
        <!-- 项目 -->
        <if test="projectId != null">
            and cfu.project_id = #{projectId}
        </if>
        <!-- 入职时间 -->
        <if test="null != beginTime and null != endTime">
            and cer.estimate_time &gt;= #{beginTime} and cer.estimate_time &lt;= #{endTime}
        </if>
        <!-- 求职者名称 -->
        <if test="null != contactName">
            and cc.`name` LIKE CONCAT('%', #{contactName},'%')
        </if>
    </select>

    <!-- 获取入职待结算列表 -->
    <select id="getSettleDetailsList" parameterType="com.ssb.approve.model.QueryFollowUpModel" resultMap="voResultMap">
        SELECT csd.id, cc.id contact_id, cp.id projectId, csd.settle_time, cc.name contactName, cc.phone contactPhone, cp.title, cu.name userName,
        csd.entry_time, csd.salary, csd.salary expectCommission, csd.node, csd.date, csd.tmp_status, csd.leave_time, csd.leave_cause, cfu.id followUpId, cc.type
        FROM crm_settle_details csd
        LEFT JOIN crm_follow_up cfu on cfu.id = csd.follow_id
        LEFT JOIN crm_contact cc on cfu.contact_id = cc.id
        LEFT JOIN crm_project cp on cp.id = cfu.`project_id`
        LEFT JOIN crm_user cu on cu.id = csd.create_by
        WHERE csd.status = 0 and csd.tmp_status = 0
        and csd.enterprise_id = #{enterpriseId}
        <choose>
            <when test="userIdList != null">
                AND csd.create_by in (
                <foreach collection="userIdList" item="userId" separator=",">
                    #{userId}
                </foreach>
                )
            </when>
            <otherwise>
                AND csd.create_by = #{userId}
            </otherwise>
        </choose>
        and cfu.del_flag = 0
        and csd.del_flag = 0
        ORDER BY csd.settle_time asc
        limit #{pageBegin}, #{pageSize}
    </select>

    <!-- 获取入职待结算数量  ^^ -->
    <select id="getSettleDetailsCount" resultType="int">
        SELECT count(csd.id)
        FROM crm_settle_details csd
        LEFT JOIN crm_follow_up cfu on cfu.id = csd.follow_id
        LEFT JOIN crm_contact cc on cfu.contact_id = cc.id
        LEFT JOIN crm_project cp on cp.id = cfu.`project_id`
        LEFT JOIN crm_user cu on cu.id = csd.create_by
        WHERE csd.status = 0 and csd.tmp_status = 0
        and csd.enterprise_id = #{enterpriseId}
        <choose>
            <when test="userIdList != null">
                AND csd.create_by in (
                <foreach collection="userIdList" item="userId" separator=",">
                    #{userId}
                </foreach>
                )
            </when>
            <otherwise>
                AND csd.create_by = #{userId}
            </otherwise>
        </choose>
        and cfu.del_flag = 0
        and csd.del_flag = 0
    </select>

    <!-- 获取历史跟进列表 -->
    <select id="getHistoryList" parameterType="com.ssb.approve.model.QueryFollowUpModel" resultMap="voResultMapHistory">
        SELECT cfu.id, cfu.id followUpId, cp.id projectId, cfu.contact_id, cc.name contactName, cc.phone contactPhone, cu.name userName, cfu.status, cfu.current_status,
        cer.behavior_time, cp.title, cfu.update_time, cc.type, cir.behavior_time interviewTime,
        cp.id projectId,
        (case when cir.failure is null then cer.refuse else cir.failure END ) reason,
        csd.actual_settle_time actualSettleTime,
        (case when cir.remark is null then cer.remark else cir.remark END ) remark,
        csl.behavior_time resignationTime
        FROM crm_follow_up cfu
        LEFT JOIN crm_project cp ON cfu.`project_id` = cp.id
        LEFT JOIN crm_contact cc ON cc.id = cfu.`contact_id`
        LEFT JOIN crm_user cu ON cu.id = cfu.create_by
        LEFT JOIN crm_entry_record cer ON cer.follow_id = cfu.id
        LEFT JOIN crm_interview_record cir ON cir.follow_id = cfu.id
        LEFT JOIN crm_settle_details csd ON csd.follow_id = cfu.id
        left join crm_settle_log csl on csl.settle_id = csd.id
        WHERE cfu.finish = 1 AND cfu.del_flag = 0
            <choose>
                <when test="userIdList != null">
                    AND cfu.create_by in (
                    <foreach collection="userIdList" item="userId" separator=",">
                        #{userId}
                    </foreach>
                    )
                </when>
                <otherwise>
                    AND cfu.create_by = #{userId}
                </otherwise>
            </choose>
          AND cfu.enterprise_id = #{enterpriseId}
        <if test="null != userName">
            AND cc.name like CONCAT('%', #{userName},'%')
        </if>
        <if test="null != phone">
            AND cc.phone like CONCAT('%', #{phone},'%')
        </if>
        <if test="null != currentStatus">
            AND cfu.current_status = #{currentStatus}
        </if>
        <if test="null != title">
            AND cp.title like CONCAT('%', #{title},'%')
        </if>
        <if test="null != searchType and searchType != ''">
            <if test="searchType == 'followUpTime'">
                AND cfu.update_time between #{beginTime} and #{endTime}
            </if>
            <if test="searchType == 'interviewTime'">
                AND cir.behavior_time between #{beginTime} and #{endTime}
            </if>
            <if test="searchType == 'entryTime'">
                AND cer.behavior_time between #{beginTime} and #{endTime}
            </if>
        </if>
        ORDER BY cfu.update_time desc
        limit #{pageBegin}, #{pageSize}
    </select>

    <!-- 获取历史跟数量 ^^^ -->
    <select id="getHistoryCount" resultType="int">
        SELECT count(cfu.id)
        FROM crm_follow_up cfu
        LEFT JOIN crm_project cp ON cfu.`project_id` = cp.id
        LEFT JOIN crm_contact cc ON cc.id = cfu.`contact_id`
        LEFT JOIN crm_user cu ON cu.id = cfu.create_by
        LEFT JOIN crm_entry_record cer ON cer.follow_id = cfu.id
        LEFT JOIN crm_interview_record cir ON cir.follow_id = cfu.id
        WHERE cfu.finish = 1 AND cfu.del_flag = 0
            <choose>
                <when test="userIdList != null">
                    AND cfu.create_by in (
                    <foreach collection="userIdList" item="userId" separator=",">
                        #{userId}
                    </foreach>
                    )
                </when>
                <otherwise>
                    AND cfu.create_by = #{userId}
                </otherwise>
            </choose>
          AND cfu.enterprise_id = #{enterpriseId}
        <if test="null != userName">
            AND cc.name like CONCAT('%', #{userName},'%')
        </if>
        <if test="null != phone">
            AND cc.phone like CONCAT('%', #{phone},'%')
        </if>
        <if test="null != currentStatus">
            AND cfu.current_status = #{currentStatus}
        </if>
        <if test="null != title">
            AND cp.title like CONCAT('%', #{title},'%')
        </if>
        <if test="null != searchType and searchType != ''">
            <if test="searchType == 'followUpTime'">
                AND cfu.update_time between #{beginTime} and #{endTime}
            </if>
            <if test="searchType == 'interviewTime'">
                AND cir.behavior_time between #{beginTime} and #{endTime}
            </if>
            <if test="searchType == 'entryTime'">
                AND cer.behavior_time between #{beginTime} and #{endTime}
            </if>
        </if>
    </select>

    <!-- 获取进行中跟进数量  通过跟进人 -->
    <select id="getFollowUpCountByCreateByAndFinish" resultType="int">
        select count(id)
        from crm_follow_up
        where create_by = #{createBy} and enterprise_id = #{enterpriseId} and del_flag = 0
        <if test="finish != null">
            and finish = #{finish}
        </if>
    </select>

    <!-- 获取未完成跟进 or 完成，已完结 days天以内的数据 -->
    <select id="getFollowUpNoFinishByCreateBy" resultMap="voResultMap">
        select id, enterprise_id, contact_id, project_id, status, current_status, finish, create_by, create_time, update_by, update_time, del_flag, remark
        from crm_follow_up
        where create_by = #{fromUserId} and del_flag = 0
        and (finish = 0
            <if test="days != null">
                or (finish = 1 and current_status = 10 and update_time > DATE_SUB(now(), INTERVAL #{days} DAY))
            </if>
         )
    </select>

    <!-- 离职更新 跟进记录 对接人 -->
    <update id="quitUpdateFollowUpCreateBy">
        UPDATE crm_follow_up
        SET create_by = #{toUserId}, update_time = #{updateTime}
        WHERE enterprise_id = #{enterpriseId} AND create_by = #{fromUserId} AND del_flag = 0
        AND id in
        <foreach collection="followIds" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </update>

    <!-- 获取项目跟进详情 -->
    <select id="getProjectFollowUpDetails" resultMap="voFollowUpDetails">
        select cfu.id, cc.name contactName, cc.phone contactPhone, cfu.current_status, cu.name userName, cu.id userId,
               cp.master, cp.signing_user_id signingUserId,
               (case when cfu.project_id = #{projectId} then 1 when cp.share_project_id = #{projectId} then 2 end) followType
        FROM crm_follow_up cfu
        LEFT JOIN crm_contact cc ON cc.id = cfu.`contact_id`
        JOIN crm_interview_record cir on cir.follow_id = cfu.id
        LEFT JOIN crm_entry_record cer ON cer.follow_id = cfu.id
        left join crm_user cu on cfu.create_by = cu.id
        inner join crm_project cp on cp.id = cfu.project_id
        where (cfu.project_id = #{projectId} or cp.share_project_id = #{projectId}) and cfu.current_status != -1 and cfu.del_flag = 0
        <!-- 面试时间 -->
        <if test="beginInterviewTime != null and endInterviewTime != null">
            and cir.behavior_time &gt;= #{beginInterviewTime} and cir.behavior_time &lt;= #{endInterviewTime}
        </if>
        <!-- 入职时间 -->
        <if test="beginEntryTime != null and endEntryTime != null">
            and cer.behavior_time &gt;= #{beginEntryTime} and cer.behavior_time &lt;= #{endEntryTime}
        </if>
        <!-- 数据统计  0 邀约   1 到面  2offer  3 入职 4 结算 -->
        <if test="dataType == 1">
            and cir.`status` in (3, 5)
        </if>
        <if test="dataType == 2">
            and cir.`status` = 5
            <if test="inState == 1">
                and cer.status in (0,2)
            </if>
            <if test="inState == 2">
                and cer.status = 3
            </if>
            <if test="inState == 3">
                and cer.status =1
            </if>
        </if>
        <if test="dataType == 3">
            and cer.`status` = 1
        </if>
        <if test="dataType == 4">
            and cfu.current_status = 10
        </if>
        <!-- 排序 -->
        <choose>
            <when test="sortFiled != null and sortType != null">
                <!-- 面试时间 -->
                <if test="sortFiled == 'interviewTime'">
                    <if test="sortType == 0">
                        ORDER BY cir.behavior_time ASC
                    </if>
                    <if test="sortType == 1">
                        ORDER BY cir.behavior_time desc
                    </if>
                </if>
                <!-- 入职时间 -->
                <if test="sortFiled == 'entryTime'">
                    <if test="sortType == 0">
                        ORDER BY cer.behavior_time ASC
                    </if>
                    <if test="sortType == 1">
                        ORDER BY cer.behavior_time desc
                    </if>
                </if>
            </when>

            <otherwise>
                ORDER BY cir.behavior_time DESC
            </otherwise>
        </choose>

        limit #{pageBegin}, #{pageSize}
    </select>

    <!-- 获取项目跟进详情  数量 -->
    <select id="getProjectFollowUpDetailsCount" resultType="java.lang.Integer">
        select count(cfu.id)
        FROM crm_follow_up cfu
        LEFT JOIN crm_contact cc ON cc.id = cfu.`contact_id`
        JOIN crm_interview_record cir on cir.follow_id = cfu.id
        LEFT JOIN crm_entry_record cer ON cer.follow_id = cfu.id
        inner join crm_project cp on cp.id = cfu.project_id
        where (cfu.project_id = #{projectId} or cp.share_project_id = #{projectId}) and cfu.current_status != -1 and cfu.del_flag = 0
        <!-- 面试时间 -->
        <if test="beginInterviewTime != null and endInterviewTime != null">
            and cir.behavior_time &gt;= #{beginInterviewTime} and cir.behavior_time &lt;= #{endInterviewTime}
        </if>
        <!-- 入职时间 -->
        <if test="beginEntryTime != null and endEntryTime != null">
            and cer.behavior_time &gt;= #{beginEntryTime} and cer.behavior_time &lt;= #{endEntryTime}
        </if>
        <!-- 数据统计  0 邀约   1 到面  2offer  3 入职 4 结算 -->
        <if test="dataType == 1">
            and cir.`status` in (3, 5)
        </if>
        <if test="dataType == 2">
            and cir.`status` = 5
            <if test="inState == 1">
                and cer.status in (0,2)
            </if>
            <if test="inState == 2">
                and cer.status = 3
            </if>
            <if test="inState == 3">
                and cer.status =1
            </if>
        </if>
        <if test="dataType == 3">
            and cer.`status` = 1
        </if>
        <if test="dataType == 4">
            and cfu.current_status = 10
        </if>
    </select>

    <select id="getSettleCount" resultType="java.lang.Integer">
        select count(*) from crm_follow_up where current_status = 10 and del_flag = 0 and project_id = #{projectId}
    </select>

    <resultMap id="appResumeFollowUpList" type="com.ssb.approve.model.vo.AppResumeFollowUpListVO">
        <result property="name" column="name"/>
        <result property="createTime" column="create_time"/>
        <result property="followStatus" column="follow_status"/>
        <result property="changeResult" column="change_result"/>
        <result property="followResult" column="current_status"/>
        <collection property="followUpPersonInCharge" column="resumeId" select="com.ssb.approve.dao.FollowUpDAO.getFollowUpPersonInCharge"/>
    </resultMap>

    <select id="getAppResumeFollowUpList" resultMap="appResumeFollowUpList">
        SELECT
            cc.name, MAX(cfu.update_time) lastFollowTime, cc.id contactId, cr.id resumeId, cc.type,
            ( SELECT GROUP_CONCAT( cp.name SEPARATOR '、' ) from crm_resume_expect_position crep inner join crm_position_v2 cp on crep.position_second2 = cp.id where crep.resume_id = cr.id)crmPositionName,
            ( SELECT name FROM crm_province WHERE id = ( SELECT province FROM crm_resume_expect_region WHERE resume_id = cr.id limit 1) ) province,
            ( SELECT name FROM crm_city WHERE id = ( SELECT city FROM crm_resume_expect_region WHERE resume_id = cr.id limit 1) ) city,
            IFNULL(( select group_concat( cd.name ) from crm_resume_expect_region carer INNER JOIN crm_district cd on cd.id = carer.district where carer.resume_id = cr.id), '全市') district,
            cce.create_time,
            cce.follow_status,
            cce.change_result,
            cfu.current_status,
            (select name from crm_user where id = cfu.create_by) userName,
            cce.first_follow_time firstFollowTime
        FROM
            crm_contact cc
            INNER JOIN crm_contact_extend cce ON cc.id = cce.contact_id
            INNER JOIN crm_resume cr ON cr.contact_id = cc.id
            LEFT JOIN crm_follow_up cfu on cfu.contact_id = cc.id
        WHERE
            cc.type = 1
            AND cc.user_from = 1
            AND cc.enterprise_id = #{eid}
            <!-- 期望职位 -->
            <if test="crmPositionFirst != null or crmPositionSecond != null">
                AND exists(
                    SELECT
                        0
                    FROM
                        crm_resume_expect_position
                    WHERE
                            resume_id = cr.id
                        <if test="crmPositionZeroth != null">
                            AND position_one_id = #{crmPositionZeroth}
                        </if>
                        <if test="crmPositionFirst != null">
                            AND position_first2 = #{crmPositionFirst}
                        </if>
                        <if test="crmPositionSecond != null">
                            AND position_second2 = #{crmPositionSecond}
                        </if>
                )
            </if>
            <!-- 期望地点 -->
            <if test="province != null or city != null or district != null">
                AND exists (select 0 from crm_resume_expect_region where resume_id = cr.id
                <if test="province != null">
                    and province = #{province}
                </if>
                <if test="city != null">
                    and city = #{city}
                </if>
                <if test="district != null">
                    and (district = #{district} or district = 0)
                </if>
                )
            </if>
            <!-- 跟进状态 -->
            <if test="followStatus != null">
                AND cce.follow_status = #{followStatus}
            </if>
            <!-- 转化结果 -->
            <if test="changeResult != null">
                AND cce.change_result = #{changeResult}
            </if>
            <!-- 跟进负责人 -->
            <if test="followUpPersonInCharge != null">
                AND EXISTS(
                    SELECT
                        0
                    FROM
                        crm_resume_expect_region carer
                    INNER JOIN crm_app_resume_range carr ON carr.city = carer.city
                    INNER JOIN crm_user cu on cu.id = carr.user_id
                    where  carr.del_flag = 0 and carer.resume_id = cr.id and cu.name like CONCAT('%', #{followUpPersonInCharge},'%')
                    UNION
                    SELECT
                        0
                    FROM
                        crm_user cu
                    INNER JOIN crm_app_resume_range carr ON cu.id = carr.user_id
                    where carr.del_flag = 0 and carr.province = 0 and carr.city = 0 and cu.name like CONCAT('%', #{followUpPersonInCharge},'%')
                )
            </if>
            <!-- 当前跟进人 -->
            <if test="followUp != null">
                AND exists(
                    SELECT
                        0
                    FROM
                        crm_user
                    WHERE
                        id = cfu.create_by and
                        name like CONCAT('%', #{followUp}, '%')
                )
            </if>
            <!-- 跟进间隔 -->
            <if test="firstFollowInterval != null">
                AND cce.first_follow_interval = #{firstFollowInterval}
            </if>
            <!-- 入库时间 -->
            <if test="beginCreateTime != null and endCreateTime != null">
                AND cce.create_time between #{beginCreateTime} and #{endCreateTime}
            </if>
            <!-- 交付状态 -->
            <if test="followResult != null">
                <choose>
                    <when test="followResult == 0">
                        AND cfu.current_status = -1
                    </when>
                    <when test="followResult == 1">
                        AND cfu.current_status = 1
                    </when>
                    <when test="followResult == 2">
                        AND cfu.current_status in (0,3,5)
                    </when>
                    <when test="followResult == 3">
                        AND cfu.current_status = 4
                    </when>
                    <when test="followResult == 4">
                        AND cfu.current_status = 2
                    </when>
                    <when test="followResult == 5">
                        AND cfu.current_status = 6
                    </when>
                    <when test="followResult == 6">
                        AND cfu.current_status = 9
                    </when>
                    <when test="followResult == 7">
                        AND cfu.current_status = 8
                    </when>
                    <when test="followResult == 8">
                        AND cfu.current_status = 7
                    </when>
                    <when test="followResult == 9">
                        AND cfu.current_status = 10
                    </when>
                    <when test="followResult == 10">
                        AND cfu.current_status = 11
                    </when>
                </choose>
            </if>
        group by cc.id order by cce.create_time desc limit #{pageBegin}, #{pageSize}
    </select>

    <select id="getAppResumeFollowUpListCount" resultType="java.lang.Integer">
        SELECT
            count(DISTINCT(cc.id))
        FROM
            crm_contact cc
            INNER JOIN crm_contact_extend cce ON cc.id = cce.contact_id
            INNER JOIN crm_resume cr ON cr.contact_id = cc.id
            LEFT JOIN crm_follow_up cfu on cfu.contact_id = cc.id
        WHERE
            cc.type = 1
            AND cc.user_from = 1
            AND cc.enterprise_id = #{eid}
        <!-- 期望职位 -->
        <if test="crmPositionFirst != null or crmPositionSecond != null">
                AND exists(
                    SELECT
                        0
                    FROM
                        crm_resume_expect_position
                    WHERE
                            resume_id = cr.id
                        <if test="crmPositionZeroth != null">
                            AND position_one_id = #{crmPositionZeroth}
                        </if>
                        <if test="crmPositionFirst != null">
                            AND position_first2 = #{crmPositionFirst}
                        </if>
                        <if test="crmPositionSecond != null">
                            AND position_second2 = #{crmPositionSecond}
                        </if>
                )
            </if>
        <!-- 期望地点 -->
        <if test="province != null or city != null or district != null">
                AND exists (select 0 from crm_resume_expect_region where resume_id = cr.id
                <if test="province != null">
                    and province = #{province}
                </if>
                <if test="city != null">
                    and city = #{city}
                </if>
                <if test="district != null">
                    and (district = #{district} or district = 0)
                </if>
                )
            </if>
        <!-- 跟进状态 -->
        <if test="followStatus != null">
                AND cce.follow_status = #{followStatus}
            </if>
        <!-- 转化结果 -->
        <if test="changeResult != null">
                AND cce.change_result = #{changeResult}
            </if>
        <!-- 跟进负责人 -->
        <if test="followUpPersonInCharge != null">
                AND EXISTS(
                    SELECT
                        0
                    FROM
                        crm_resume_expect_region carer
                    INNER JOIN crm_app_resume_range carr ON carr.city = carer.city
                    INNER JOIN crm_user cu on cu.id = carr.user_id
                    where  carr.del_flag = 0 and carer.resume_id = cr.id and cu.name like CONCAT('%', #{followUpPersonInCharge},'%')
                    UNION
                    SELECT
                        0
                    FROM
                        crm_user cu
                    INNER JOIN crm_app_resume_range carr ON cu.id = carr.user_id
                    where carr.del_flag = 0 and carr.province = 0 and carr.city = 0 and cu.name like CONCAT('%', #{followUpPersonInCharge},'%')
                )
            </if>
        <!-- 当前跟进人 -->
        <if test="followUp != null">
            AND exists(
                SELECT
                    0
                FROM
                    crm_user
                WHERE
                    id = cfu.create_by and
                    name like CONCAT('%', #{followUp}, '%')
            )
        </if>
        <!-- 跟进间隔 -->
        <if test="firstFollowInterval != null">
            AND cce.first_follow_interval = #{firstFollowInterval}
        </if>
        <!-- 入库时间 -->
        <if test="beginCreateTime != null and endCreateTime != null">
            AND cce.create_time between #{beginCreateTime} and #{endCreateTime}
        </if>
        <!-- 交付状态 -->
        <if test="followResult != null">
            <choose>
                <when test="followResult == 0">
                    AND cfu.current_status = -1
                </when>
                <when test="followResult == 1">
                    AND cfu.current_status = 1
                </when>
                <when test="followResult == 2">
                    AND cfu.current_status in (0,3,5)
                </when>
                <when test="followResult == 3">
                    AND cfu.current_status = 4
                </when>
                <when test="followResult == 4">
                    AND cfu.current_status = 2
                </when>
                <when test="followResult == 5">
                    AND cfu.current_status = 6
                </when>
                <when test="followResult == 6">
                    AND cfu.current_status = 9
                </when>
                <when test="followResult == 7">
                    AND cfu.current_status = 8
                </when>
                <when test="followResult == 8">
                    AND cfu.current_status = 7
                </when>
                <when test="followResult == 9">
                    AND cfu.current_status = 10
                </when>
                <when test="followResult == 10">
                    AND cfu.current_status = 11
                </when>
            </choose>
        </if>
    </select>

    <select id="getFollowUpPersonInCharge" resultType="java.lang.String">
        SELECT GROUP_CONCAT( user.NAME SEPARATOR '、' ) FROM(
            <!-- 期望地址匹配的与开放范围一致的 -->
            SELECT
                cu.name
            FROM
                crm_resume_expect_region carer
                INNER JOIN crm_app_resume_range carr ON carr.city = carer.city
                INNER JOIN crm_user cu on cu.id = carr.user_id
                where carr.del_flag = 0 and carer.resume_id = #{resumeId}
                and exists (
                    SELECT
                        0
                    FROM
                        crm_menu cm
                    INNER JOIN crm_role_menu crm ON cm.id = crm.menu_id
                    INNER JOIN crm_user_role cur on cur.role_id = crm.role_id
                    where cm.permission = 'openView' and cm.del_flag = 0 and cur.user_id = cu.id
                )
            UNION
            <!-- 开放范围不限的 -->
            SELECT
                cu.name
            FROM
                crm_user cu
                INNER JOIN crm_app_resume_range carr ON cu.id = carr.user_id
                where carr.del_flag = 0 and carr.province = 0 and carr.city = 0
                and exists (
                    SELECT
                        0
                    FROM
                        crm_menu cm
                    INNER JOIN crm_role_menu crm ON cm.id = crm.menu_id
                    INNER JOIN crm_user_role cur on cur.role_id = crm.role_id
                    where cm.permission = 'openView' and cm.del_flag = 0 and cur.user_id = cu.id
                )
            UNION
            <!-- 有查看权限的 -->
            SELECT
                 cu.name
            FROM
                crm_menu cm
                INNER JOIN crm_role_menu crm ON cm.id = crm.menu_id
                INNER JOIN crm_user_role cur on cur.role_id = crm.role_id
								INNER JOIN crm_user cu on cu.id = cur.user_id
                where cm.permission = 'openView' and cm.del_flag = 0
                )user
    </select>

    <select id="getContactIdByFollowId" resultType="java.lang.Integer">
        select contact_id from crm_follow_up where id = #{followId}
    </select>

    <select id="countConversionRatio" resultType="java.lang.Integer">
        select count(*) from (
            select cce.change_result, cce.follow_status
            <include refid="sqlInfo"/>
        ) res
        where
        <!-- 未跟进 -->
        <if test="type == 0">
            res.follow_status = 0
        </if>
        <!-- 跟进未转化 -->
        <if test="type == 1">
            res.follow_status = 1
            and res.change_result = 0
        </if>
        <!-- 跟进已转化 -->
        <if test="type == 2">
            res.follow_status = 1
            and res.change_result = 1
        </if>
    </select>

    <select id="countFollowUpEfficiency" resultType="java.lang.Integer">
        select count(*) from (
            select cce.follow_status,cce.first_follow_interval
                <include refid="sqlInfo"/>
            ) res
        where
        <!-- 未跟进 -->
        <if test="type == 0">
            res.follow_status = 0
        </if>
        <!-- 半小时内 -->
        <if test="type == 1">
            res.follow_status = 1
            and res.first_follow_interval = 0
        </if>
        <!-- 半小时到一小时 -->
        <if test="type == 2">
            res.follow_status = 1
            and res.first_follow_interval = 1
        </if>
        <!-- 一小时到两小时 -->
        <if test="type == 3">
            res.follow_status = 1
            and res.first_follow_interval = 2
        </if>
        <!-- 大于两小时 -->
        <if test="type == 4">
            res.follow_status = 1
            and res.first_follow_interval = 3
        </if>
    </select>

    <select id="countDeliveryStatus" resultType="com.ssb.approve.model.vo.DeliveryStatusVO">
        select count(*) count,
            (case when res.followStatus is null then -2 else res.followStatus end)followStatus
        from (
            select cfu.current_status followStatus
            <include refid="sqlInfo"/>
        ) res
        group by res.followStatus
    </select>
    
    <sql id="sqlInfo">
        FROM
            crm_contact cc
            INNER JOIN crm_contact_extend cce ON cc.id = cce.contact_id
            INNER JOIN crm_resume cr ON cr.contact_id = cc.id
            LEFT JOIN crm_follow_up cfu on cfu.contact_id = cc.id
        WHERE
            cc.type = 1
            AND cc.user_from = 1
        <if test="crmPositionFirst != null or crmPositionSecond != null">
            AND exists(
                SELECT
                    0
                FROM
                    crm_resume_expect_position
                WHERE
                    resume_id = cr.id
                    <if test="crmPositionFirst != null">
                        AND position_first2 = #{crmPositionFirst}
                    </if>
                    <if test="crmPositionSecond != null">
                        AND position_second2 = #{crmPositionSecond}
                    </if>
            )
        </if>
        <!-- 期望地点 -->
        <if test="province != null or city != null or district != null">
            AND exists(
                SELECT
                    0
                FROM
                    crm_resume_expect_region
                WHERE
                    resume_id = cr.id
                    <if test="province != null">
                        and province = #{province}
                    </if>
                    <if test="city != null">
                        and city = #{city}
                    </if>
                    <if test="district != null">
                        and district = #{district}
                    </if>
            )
        </if>
        <!-- 跟进状态 -->
        <if test="followStatus != null">
            AND cce.follow_status = #{followStatus}
        </if>
        <!-- 转化结果 -->
        <if test="changeResult != null">
            AND cce.change_result = #{changeResult}
        </if>
        <!-- 跟进负责人 -->
        <if test="followUpPersonInCharge != null">
            AND EXISTS(
                SELECT
                    0
                FROM
                    crm_resume_expect_region carer
                    INNER JOIN crm_app_resume_range carr ON carr.city = carer.city
                    INNER JOIN crm_user cu on cu.id = carr.user_id
                where carr.del_flag = 0 and carer.resume_id = cr.id and cu.name like CONCAT('%', #{followUpPersonInCharge},'%')
                UNION
                SELECT
                    0
                FROM
                    crm_user cu
                    INNER JOIN crm_app_resume_range carr ON cu.id = carr.user_id
                where carr.del_flag = 0 and carr.province = 0 and carr.city = 0 and cu.name like CONCAT('%', #{followUpPersonInCharge},'%')
            )
        </if>
        <!-- 当前跟进人 -->
        <if test="followUp != null">
            AND exists(
                SELECT
                    0
                FROM
                    crm_user
                WHERE
                    id = cfu.create_by and
                    name like CONCAT('%', #{followUp}, '%')
            )
        </if>
        <!-- 跟进间隔 -->
        <if test="firstFollowInterval != null">
            AND cce.first_follow_interval = #{firstFollowInterval}
        </if>
        <!-- 入库时间 -->
        <if test="beginCreateTime != null and endCreateTime != null">
            AND cce.create_time between #{beginCreateTime} and #{endCreateTime}
        </if>
        <!-- 交付状态 -->
        <if test="followResult != null">
            <choose>
                <when test="followResult == 0">
                    AND cfu.current_status = -1
                </when>
                <when test="followResult == 1">
                    AND cfu.current_status = 1
                </when>
                <when test="followResult == 2">
                    AND cfu.current_status in (0,3,5)
                </when>
                <when test="followResult == 3">
                    AND cfu.current_status = 4
                </when>
                <when test="followResult == 4">
                    AND cfu.current_status = 2
                </when>
                <when test="followResult == 5">
                    AND cfu.current_status = 6
                </when>
                <when test="followResult == 6">
                    AND cfu.current_status = 9
                </when>
                <when test="followResult == 7">
                    AND cfu.current_status = 8
                </when>
                <when test="followResult == 8">
                    AND cfu.current_status = 7
                </when>
                <when test="followResult == 9">
                    AND cfu.current_status = 10
                </when>
                <when test="followResult == 10">
                    AND cfu.current_status = 11
                </when>
            </choose>
        </if>
        group by cc.id
    </sql>

    <update id="updateRevokeFollowUp" parameterType="com.ssb.approve.entity.FollowUp">
        update crm_follow_up set status = #{status}, current_status = #{currentStatus}, finish = #{finish}, update_by = #{updateBy}, update_time = #{updateTime} where id = #{id}
    </update>

    <select id="getCurrentStatusById" resultType="java.lang.Integer">
        SELECT current_status from crm_follow_up where id = #{followId}
    </select>

    <select id="getOnGoingFollowUp" resultType="com.ssb.approve.model.vo.FollowUpVO">
        select
            cp.title,
            cc.name contactName,
            (select name from crm_user where id = cfu.create_by) userName
        from
            crm_follow_up cfu
        inner join crm_project cp on cfu.project_id = cp.id
        inner join crm_contact cc on cc.id = cfu.contact_id
        where cfu.create_by != (select create_by from crm_follow_up where id = #{followId})
        and cfu.contact_id = (select contact_id from crm_follow_up where id = #{followId})
        and cfu.del_flag = 0 and cfu.finish = 0 limit 1
    </select>

    <select id="getProjectIdById" resultType="java.lang.Integer">
        select project_id from crm_follow_up where id = #{followId} and del_flag = 0
    </select>

    <select id="getFollowUpUserCommissionInfoById" resultType="java.lang.Integer">
        SELECT
            cu.type
        FROM
            crm_follow_up cfu
            INNER JOIN crm_user cu ON cu.id = cfu.create_by
        where cfu.id = #{followId}
    </select>

    <resultMap id="cooperationProjectFollowUpListResult" type="com.ssb.approve.model.vo.CooperationProjectFollowUpInfoVO">
        <id column="id" property="id"/>
        <association property="settleStatus" column="id" select="com.ssb.approve.dao.SettleDetailsDAO.getSettleStatusByFollowId"/>
    </resultMap>

    <select id="getCooperationProjectFollowUpList" resultMap="cooperationProjectFollowUpListResult"
            parameterType="com.ssb.approve.model.QueryFollowUpModel">
        SELECT
               cfu.id,
            cfu.current_status currentStatus,
            cc.phone,
            cc.`name`,
            cu.`name` followUpUserName,
            cir.behavior_time interviewTime,
            cir.status interviewStatus,
            cer.behavior_time entryTime,
            cer.status entryStatus,
            (SELECT remark from crm_follow_log where follow_id = cfu.id ORDER BY create_time desc limit 1) remark
        FROM
            crm_follow_up cfu
            INNER JOIN crm_contact cc on cc.id = cfu.contact_id
            INNER JOIN crm_user cu on cu.id = cfu.create_by
            LEFT JOIN crm_interview_record cir ON cir.follow_id = cfu.id
            LEFT JOIN crm_entry_record cer ON cer.follow_id = cfu.id
        WHERE
            cfu.project_id = #{projectId}
            and cfu.enterprise_id = #{enterpriseId}
            AND cfu.current_status in (-1, 0, 3, 4, 6, 7, 8, 9, 10, 11, 12)
            <if test="beginInterviewTime != null and endInterviewTime != null">
                and cir.behavior_time between #{beginInterviewTime} and #{endInterviewTime}
            </if>
            <if test="beginEntryTime != null and endEntryTime != null">
                and cer.behavior_time between #{beginEntryTime} and #{endEntryTime}
            </if>
            <if test="currentStatus != null">
                and cfu.current_status = #{currentStatus}
            </if>
        order by cfu.update_time desc
        limit #{pageBegin}, #{pageSize}
    </select>

    <select id="getCooperationProjectFollowUpCount" resultType="java.lang.Integer" parameterType="com.ssb.approve.model.QueryFollowUpModel">
        SELECT
            count(*)
        FROM
            crm_follow_up cfu
            INNER JOIN crm_contact cc on cc.id = cfu.contact_id
            INNER JOIN crm_user cu on cu.id = cfu.create_by
            LEFT JOIN crm_interview_record cir ON cir.follow_id = cfu.id
            LEFT JOIN crm_entry_record cer ON cer.follow_id = cfu.id
        WHERE
            cfu.project_id = #{projectId}
            and cfu.enterprise_id = #{enterpriseId}
            AND cfu.current_status in (-1, 0, 4, 6, 7, 9, 10, 11, 12)
            <if test="beginInterviewTime != null and endInterviewTime != null">
                and cir.behavior_time between #{beginInterviewTime} and #{endInterviewTime}
            </if>
            <if test="beginEntryTime != null and endEntryTime != null">
                and cer.behavior_time between #{beginEntryTime} and #{endEntryTime}
            </if>
            <if test="currentStatus != null">
                and cfu.current_status = #{currentStatus}
            </if>
    </select>

    <select id="getSettleData" parameterType="com.ssb.approve.model.dto.RecruitmentDataDTO"
            resultType="com.ssb.approve.model.vo.StatisticalChartDataVO">
        SELECT
            count(*) as count,
	        DATE_FORMAT(csd.actual_settle_time, '%m-%d') AS date
        FROM
            crm_follow_up cfu
            INNER JOIN (select MAX(node), actual_settle_time, follow_id from crm_settle_details where `status` = 1 AND actual_settle_time BETWEEN #{timeStart} and #{timeEnd} GROUP BY follow_id) csd on csd.follow_id = cfu.id
        WHERE
            <if test="type == 1">
                cfu.create_by = #{userId}
            </if>
            <if test="type == 2">
                cfu.create_by in (
                <foreach collection="userIdList" item="userId" separator=",">
                    #{userId}
                </foreach>
                )
            </if>
          AND cfu.current_status = 10
          AND cfu.finish = 1
        GROUP BY
            DATE_FORMAT(csd.actual_settle_time, '%m-%d')
        ORDER BY
            date;
    </select>

    <select id="getSettleDataList" parameterType="com.ssb.approve.model.dto.RecruitmentDataListDTO"
            resultType="com.ssb.approve.model.vo.RecruitmentDataListVO">
        SELECT
            cc.id contactId,
            cc.name contactName,
            cc.phone phone,
            cp.id projectId,
            cp.title,
            ccu.id customerId,
            ccu.`name` customerName,
            csd.actual_settle_time settleTime,
            cu.id followUserId,
            cu.name followUserName,
            cp.source
        FROM
            crm_follow_up cfu
                INNER JOIN (select MAX(node), actual_settle_time, follow_id from crm_settle_details where `status` = 1 AND actual_settle_time BETWEEN #{timeStart} and #{timeEnd} GROUP BY follow_id) csd on csd.follow_id = cfu.id
                INNER JOIN crm_user cu ON cu.id = cfu.create_by
                INNER JOIN crm_contact cc on cc.id = cfu.contact_id
                INNER JOIN crm_project cp on cfu.project_id = cp.id
                INNER JOIN crm_customer ccu on ccu.id = cp.customer_id
        WHERE
            <if test="type == 1">
                cfu.create_by = #{userId}
            </if>
            <if test="type == 2">
                cfu.create_by in (
                <foreach collection="userIdList" item="userId" separator=",">
                    #{userId}
                </foreach>
                )
            </if>
            AND cu.del_flag = 0
            AND cfu.del_flag = 0
            AND cfu.current_status = 10
            AND cfu.finish = 1
            <if test="userName != null">
                AND cc.name like CONCAT('%', #{userName},'%')
            </if>
            <if test="phone != null">
                AND cc.phone like CONCAT('%', #{phone},'%')
            </if>
            <if test="title != null">
                AND cp.title like CONCAT('%', #{title},'%')
            </if>
            <if test="customerName != null">
                AND ccu.name like CONCAT('%', #{customerName},'%')
            </if>
            <if test="followUser != null">
                AND cu.name like CONCAT('%', #{followUser},'%')
            </if>
        ORDER BY csd.actual_settle_time desc
        limit #{pageBegin}, #{pageSize}
    </select>

    <select id="getSettleDataCount" parameterType="com.ssb.approve.model.dto.RecruitmentDataListDTO"
            resultType="java.lang.Integer">
        SELECT
            count(*)
        FROM
            crm_follow_up cfu
                INNER JOIN (select MAX(node), actual_settle_time, follow_id from crm_settle_details where `status` = 1 AND actual_settle_time BETWEEN #{timeStart} and #{timeEnd} GROUP BY follow_id) csd on csd.follow_id = cfu.id
                INNER JOIN crm_user cu ON cu.id = cfu.create_by
                INNER JOIN crm_contact cc on cc.id = cfu.contact_id
                INNER JOIN crm_project cp on cfu.project_id = cp.id
                INNER JOIN crm_customer ccu on ccu.id = cp.customer_id
        WHERE
            <if test="type == 1">
                cfu.create_by = #{userId}
            </if>
            <if test="type == 2">
                cfu.create_by in (
                <foreach collection="userIdList" item="userId" separator=",">
                    #{userId}
                </foreach>
                )
            </if>
            AND cu.del_flag = 0
            AND cfu.del_flag = 0
            AND cfu.current_status = 10
            AND cfu.finish = 1
            <if test="userName != null">
                AND cc.name like CONCAT('%', #{userName},'%')
            </if>
            <if test="phone != null">
                AND cc.phone like CONCAT('%', #{phone},'%')
            </if>
            <if test="title != null">
                AND cp.title like CONCAT('%', #{title},'%')
            </if>
            <if test="customerName != null">
                AND ccu.name like CONCAT('%', #{customerName},'%')
            </if>
            <if test="followUser != null">
                AND cu.name like CONCAT('%', #{followUser},'%')
            </if>
    </select>

    <select id="getSettleCountByInterviewTime" resultType="java.lang.Integer" parameterType="com.ssb.approve.model.dto.ConversionRateDataDTO">
        SELECT
            count(*) as count
        FROM
            crm_follow_up cfu
            INNER JOIN (select follow_id from crm_settle_details where `status` = 1 AND actual_settle_time BETWEEN #{timeStart} and #{timeEnd} GROUP BY follow_id) csd on csd.follow_id = cfu.id
            inner join crm_interview_record cir on cir.follow_id = cfu.id
        WHERE
            <if test="type == 1">
                cfu.create_by = #{userId}
            </if>
            <if test="type == 2">
                cfu.create_by in (
                <foreach collection="userIdList" item="userId" separator=",">
                    #{userId}
                </foreach>
                )
            </if>
          AND cfu.current_status = 10
          AND cfu.finish = 1
          AND cir.behavior_time between #{timeStart} and #{timeEnd}
    </select>

    <select id="getShareProjectFollowUpCount" resultType="java.lang.Integer">
        SELECT
            count(*)
        FROM
            crm_follow_up cfu
                INNER JOIN crm_project cp ON cfu.project_id = cp.id
        WHERE
            ( cfu.project_id = #{projectId} OR cp.share_project_id = #{projectId} )
            <if test="type == 1">
                AND current_status IN (-1, 0, 3, 5)
            </if>
            <if test="type == 2">
                AND current_status IN (6, 8)
            </if>
            <if test="type == 3">
                AND current_status = 7
            </if>
    </select>

    <select id="getDataStatisticsInviteCount" resultType="java.lang.Integer">
        SELECT
            count(*)
        FROM
            crm_follow_up cfu
                INNER JOIN crm_interview_record cir ON cfu.id = cir.follow_id
        WHERE
            cir.create_by IN (
                <foreach collection="dto.userIdList" item="userId" separator=",">
                    #{userId}
                </foreach>
                )
            <if test="dto.timeType != 0">
                <choose>
                    <when test="sourceType == 1">
                        AND cir.create_time between #{dto.timeStart} and #{dto.timeEnd}
                    </when>
                    <when test="sourceType == 2">
                        AND cir.create_time between #{dto.compareTimeStart} and #{dto.compareTimeEnd}
                    </when>
                </choose>
            </if>
    </select>
    <select id="getEnterpriseDataStatisticsInviteCount" resultType="java.lang.Integer">
        SELECT
            count(*)
        FROM
            crm_follow_up cfu
            INNER JOIN crm_project cp ON cfu.project_id = cp.id
            INNER JOIN crm_customer cc ON cp.customer_id = cc.id
            INNER JOIN crm_interview_record cir ON cfu.id = cir.follow_id
        WHERE cc.customer_enterprise_id = #{dto.enterpriseId}
            <if test="dto.timeType != 0">
                AND cir.create_time between #{dto.timeStart} and #{dto.timeEnd}
            </if>
    </select>

    <select id="getEnterpriseSettleCount" resultType="java.lang.Integer">
        SELECT
         count(*)
        FROM
            crm_follow_up cfu
            inner join crm_interview_record cir on cir.follow_id = cfu.id
            INNER JOIN crm_project cp ON cfu.project_id = cp.id
            INNER JOIN crm_customer cc ON cp.customer_id = cc.id
        WHERE
            cc.customer_enterprise_id = #{dto.enterpriseId}
            AND cfu.current_status = 10
            AND cfu.finish = 1
            <if test="dto.timeType != 0">
                AND cir.behavior_time between #{dto.timeStart} and #{dto.timeEnd}
            </if>
    </select>

    <select id="listWaitingForInterviewWithTimeRange" resultType="java.lang.Integer">
        SELECT cir.id
        FROM crm_follow_up cfu
        INNER JOIN crm_interview_record cir ON cfu.id = cir.follow_id
        WHERE cfu.del_flag = 0
        AND cir.status IN (0, 2)
        AND cir.create_time between #{startTime} and #{endTime}
        AND cfu.project_id IN
            (
            SELECT id FROM crm_project WHERE id IN
            <foreach collection="projectIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
            OR share_project_id IN
            <foreach collection="projectIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
            )
    </select>

    <select id="listWaitingForAgreedInterviewWithTimeRange" resultType="java.lang.Integer">
        SELECT cir.id
        FROM crm_follow_up cfu
        INNER JOIN crm_interview_record cir ON cfu.id = cir.follow_id
        WHERE cfu.del_flag = 0
          AND cir.status IN (3, 5)
          AND cir.create_time between #{startTime} and #{endTime}
          AND cfu.project_id IN
            (
            SELECT id FROM crm_project WHERE id IN
            <foreach collection="projectIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
            OR share_project_id IN
            <foreach collection="projectIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
            )
    </select>

    <select id="listWaitingForEntryWithTimeRange" resultType="java.lang.Integer">
        SELECT cer.id
        FROM crm_follow_up cfu
        INNER JOIN crm_entry_record cer ON cfu.id = cer.follow_id
        WHERE
            cfu.del_flag = 0
            AND cer.STATUS IN (1)
            AND cer.create_time between #{startTime} and #{endTime}
            AND cfu.project_id IN
                (
                SELECT id FROM crm_project WHERE id IN
                <foreach collection="projectIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
                OR share_project_id IN
                <foreach collection="projectIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
                )
    </select>

    <select id="getSettleCountByUserIdAndTimeRange" resultType="java.lang.Integer">
        select
            count(*)
        from
            crm_follow_up cfu
            INNER JOIN  (select DISTINCT follow_id from crm_settle_details where actual_settle_time BETWEEN #{startTime} and #{endTime}) csd on csd.follow_id = cfu.id
        where cfu.current_status = 10 and create_by = #{userId}
    </select>

    <select id="listSettlementWithTimeRange" resultType="java.lang.Integer">
        SELECT csd.id
        FROM crm_follow_up cfu
        JOIN crm_settle_details csd ON csd.follow_id = cfu.id AND csd.del_flag = 0 AND csd.actual_settle_time between #{startTime} and #{endTime}
        WHERE
            cfu.del_flag = 0
          AND cfu.current_status = 10
          AND cfu.project_id IN
              (
                SELECT id FROM crm_project WHERE id IN
                <foreach collection="projectIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
                OR share_project_id IN
                <foreach collection="projectIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
             )
    </select>
</mapper>