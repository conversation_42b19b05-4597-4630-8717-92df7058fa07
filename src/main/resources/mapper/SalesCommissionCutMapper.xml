<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ssb.approve.dao.SalesCommissionCutDAO">

    <insert id="insertSalesCommissionCut" parameterType="com.ssb.approve.entity.SalesCommissionCut">
        insert into crm_sales_commission_cut (enterprise_id, strategy_id, level, commission, create_by, create_time, update_by, update_time) values
        <foreach collection="list" separator="," item="item">
            (#{item.enterpriseId}, #{item.strategyId}, #{item.level}, #{item.commission}, #{item.createBy}, now(), #{item.createBy}, now())
        </foreach>
    </insert>

    <update id="deleteSalesCommissionCut">
        update crm_sales_commission_cut set del_flag = 1, update_by = #{userId}, update_time = now() where strategy_id = #{id} and del_flag = 0
    </update>

    <select id="getSalesCommissionCutList" parameterType="java.lang.Integer" resultType="com.ssb.approve.entity.SalesCommissionCut">
        select level, commission from crm_sales_commission_cut where del_flag = 0 and strategy_id = #{id}
    </select>
</mapper>