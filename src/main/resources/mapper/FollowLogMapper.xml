<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ssb.approve.dao.FollowLogDAO">

    <!-- 保存跟进操作记录 -->
    <insert id="save" parameterType="com.ssb.approve.entity.FollowLog" useGeneratedKeys="true" keyColumn="id" keyProperty="id">
        INSERT INTO crm_follow_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            enterprise_id,
            follow_id,
            <if test="settleLogId != null">
                settle_log_id,
            </if>
            `type`,
            `status`,
            <if test="behaviorTime != null">
                behavior_time,
            </if>
            create_by,
            create_time,
            <if test="remark != null">
                remark,
            </if>
            <if test="failure != null">
                failure,
            </if>
        </trim>

        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            #{enterpriseId},
            #{followId},
            <if test="settleLogId != null">
                #{settleLogId},
            </if>
            #{type},
            #{status},
            <if test="behaviorTime != null">
                #{behaviorTime},
            </if>
            #{createBy},
            #{createTime},
            <if test="remark != null">
                #{remark},
            </if>
            <if test="failure != null">
                #{failure},
            </if>
        </trim>
    </insert>

</mapper>