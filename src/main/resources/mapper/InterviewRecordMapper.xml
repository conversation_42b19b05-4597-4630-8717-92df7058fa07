<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ssb.approve.dao.InterviewRecordDAO">

    <resultMap id="resultMap" type="com.ssb.approve.entity.InterviewRecord">
        <result column="enterprise_id" property="enterpriseId"/>
        <result column="follow_id" property="followId"/>
        <result column="behavior_time" property="behaviorTime"/>
        <result column="estimate_time" property="estimateTime"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="auth_type" property="authType"/>
    </resultMap>

    <resultMap id="voResultMap" type="com.ssb.approve.model.vo.InterviewRecordVO">
        <result column="enterprise_id" property="enterpriseId"/>
        <result column="follow_id" property="followId"/>
        <result column="behavior_time" property="behaviorTime"/>
        <result column="estimate_time" property="estimateTime"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="interview_type" property="interviewType"/>
        <result column="review_time" property="reviewTime"/>
        <result column="review_user_id" property="reviewUserId"/>
    </resultMap>

    <!-- 保存面试记录 -->
    <insert id="save" parameterType = "com.ssb.approve.entity.InterviewRecord" useGeneratedKeys="true" keyColumn="id" keyProperty="id">
        INSERT INTO crm_interview_record (enterprise_id, follow_id, node, auth_type, behavior_time, create_by, create_time, update_by, update_time, remark, failure)
        VALUES (#{enterpriseId}, #{followId}, #{node}, #{authType}, #{behaviorTime}, #{createBy}, #{createTime}, #{updateBy}, #{updateTime}, #{remark}, #{failure})
    </insert>

    <!-- 获取面试记录 通过id -->
    <select id="getInterviewRecordById" resultMap="resultMap">
        SELECT id, enterprise_id, follow_id, node, status, auth, behavior_time, estimate_time, create_time, create_by, auth_type
        from crm_interview_record
        where id = #{id}
    </select>

    <!-- 更新面试记录表 -->
    <update id="updateInterviewRecord">
        UPDATE crm_interview_record
        SET
            `status` = #{status},
            <if test="node != null">
                node = #{node},
            </if>
            <if test="behaviorTime != null">
                behavior_time = #{behaviorTime},
            </if>
            <if test="reviewTime != null">
                review_time = #{reviewTime},
            </if>
            <if test="reviewUserId != null">
                review_user_id = #{reviewUserId},
            </if>
            estimate_time = #{estimateTime},
            update_by = #{updateBy},
            update_time = #{updateTime},
            remark = #{remark},
            failure = #{failure}
        WHERE id = #{id}
    </update>

    <update id="updateInterviewRecordByFollowId">
        UPDATE crm_interview_record
        SET
            `status` = #{status},
            <if test="node != null">
                node = #{node},
            </if>
            <if test="behaviorTime != null">
                behavior_time = #{behaviorTime},
            </if>
            <if test="estimateTime != null">
                estimate_time = #{estimateTime},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
            <if test="failure != null">
                failure = #{failure},
            </if>
            update_by = #{updateBy},
            update_time = #{updateTime}
        WHERE follow_id = #{followId}
    </update>

    <!-- 获取面试确认列表 -->
    <select id="getInterviewAuthList" resultMap="voResultMap">
        select cir.id, cir.behavior_time, cp.title, cp.interview_type, cc.id contactId, cir.follow_id, cp.id projectId, cc.name contactName,
            cc.phone contactPhone, cu.name userName, cir.node, cir.status, cir.auth, cp.interview_upper_limit interviewUpperLimit, cc.type,
            cir.failure,
            (select name from crm_user where id = cir.review_user_id) reviewUser,
            cir.review_time reviewTime,
            cir.remark
        from crm_interview_record cir
        left join crm_follow_up cfu on cfu.id = cir.`follow_id`
        left join crm_project cp on cfu.`project_id` = cp.id
        left join crm_contact cc on cc.id = cfu.`contact_id`
        left join crm_user cu on cu.id = cir.create_by
        <where>
            (
                (cir.enterprise_id = #{enterpriseId} and cp.share_project_id is null)
             or
                (cp.share_project_id in (select id from crm_project where enterprise_id = #{enterpriseId} and share_status = 1))
            )
                AND cp.signing_user_id = #{userId}
            <if test="authStatus != null ">
                AND cir.auth = #{authStatus}
            </if>
            <!-- 待面试 -->
            <if test="interviewStatus == 1">
                and cir.status in (0, 2)
            </if>
            <!-- 未通过 -->
            <if test="interviewStatus == 2">
                and cir.status = 3
            </if>
            <!-- 未参加 -->
            <if test="interviewStatus == 3">
                and cir.status = 1
            </if>
            <!-- 已通过-->
            <if test="interviewStatus == 4">
                and cir.status = 5
            </if>
            <if test="null != title">
                and cp.title LIKE CONCAT('%', #{title},'%')
            </if>
            <if test="null != jobSeeker">
                and (cc.name LIKE CONCAT('%', #{jobSeeker},'%') or cc.phone LIKE CONCAT('%', #{jobSeeker},'%'))
            </if>
            <if test="null != beginTime and null != endTime">
                and cir.behavior_time &gt;= #{beginTime} and cir.behavior_time &lt;= #{endTime}
            </if>
            <if test="createBy != null">
                and cfu.create_by = #{createBy}
            </if>
            <if test="null != beginReviewTime and null != endReviewTime">
                and cir.review_time &gt;= #{beginReviewTime} and cir.review_time &lt;= #{endReviewTime}
            </if>
        </where>
        ORDER BY cir.behavior_time asc
        limit #{pageBegin}, #{pageSize}
    </select>

    <!-- 获取面试确认数量 -->
    <select id="getInterviewAuthCount" resultType="int">
        select count(cir.id)
        from crm_interview_record cir
        left join crm_follow_up cfu on cfu.id = cir.`follow_id`
        left join crm_project cp on cfu.`project_id` = cp.id
        left join crm_contact cc on cc.id = cfu.`contact_id`
        left join crm_user cu on cu.id = cir.create_by
        <where>
            (
                (cir.enterprise_id = #{enterpriseId} and cp.share_project_id is null)
             or
                (cp.share_project_id in (select id from crm_project where enterprise_id = #{enterpriseId} and share_status = 1))
            )
                AND cp.signing_user_id = #{userId}
            <if test="authStatus != null ">
                AND cir.auth = #{authStatus}
            </if>
            <!-- 待面试 -->
            <if test="interviewStatus == 1">
                and cir.status in (0, 2)
            </if>
            <!-- 未通过 -->
            <if test="interviewStatus == 2">
                and cir.status = 1
            </if>
            <!-- 未通过 -->
            <if test="interviewStatus == 3">
                and cir.status = 3
            </if>
            <!-- 已通过-->
            <if test="interviewStatus == 4">
                and cir.status = 5
            </if>
            <if test="null != title">
                and cp.title LIKE CONCAT('%', #{title},'%')
            </if>
            <if test="null != jobSeeker">
                and (cc.name LIKE CONCAT('%', #{jobSeeker},'%') or cc.phone LIKE CONCAT('%', #{jobSeeker},'%'))
            </if>
            <if test="null != beginTime and null != endTime">
                and cir.behavior_time &gt;= #{beginTime} and cir.behavior_time &lt;= #{endTime}
            </if>
            <if test="createBy != null">
                and cfu.create_by = #{createBy}
            </if>
            <if test="null != beginReviewTime and null != endReviewTime">
                and cir.review_time &gt;= #{beginReviewTime} and cir.review_time &lt;= #{endReviewTime}
            </if>
        </where>
    </select>

    <!-- 更新面试审核状态 -->
    <update id="updateInterviewAuth">
        UPDATE crm_interview_record
        SET `auth` = #{auth}, update_by = #{updateBy}, update_time = #{updateTime}
        where id = #{id}
    </update>

    <!-- 获取待面试数量 -->
    <select id="getWaitInterviewCount" resultType="int">
        SELECT count(cfu.id)
        FROM crm_follow_up cfu
        LEFT JOIN crm_interview_record cir ON cir.`follow_id` = cfu.id
        WHERE cfu.project_id = #{projectId} AND cfu.del_flag = 0 AND cir.id is not null AND cir.status IN (0, 2, 4)
    </select>

    <!-- 获取面试记录 -->
    <select id="getInterviewRecordByCreateBy" resultMap="resultMap">
        SELECT id, enterprise_id, follow_id, node, status, auth, behavior_time, estimate_time, remark, failure, create_by, create_time, update_by, update_time
        FROM crm_interview_record
        WHERE enterprise_id = #{enterpriseId} and follow_id = #{followId} and create_by = #{createBy}
    </select>

    <!-- 离职 更新面试记录 对接人 -->
    <update id="quitUpdateInterviewRecordCreateBy">
        UPDATE crm_interview_record
        SET create_by = #{toUserId}, update_time = #{updateTime}
        WHERE enterprise_id = #{enterpriseId} AND create_by = #{fromUserId}
        AND follow_id in
        <foreach collection="followIds" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </update>

    <!-- 获取面试每日上线数 -->
    <select id="getInterviewDailyLimit" resultType="string">
        SELECT DATE_FORMAT(cir.behavior_time, "%Y-%m-%d")
        from crm_interview_record cir
        LEFT JOIN crm_follow_up cfu on cir.follow_id = cfu.id
        where cfu.project_id = #{projectId} and cfu.del_flag = 0 and cir.status != 1
        and cir.behavior_time &gt;= #{beginTime} and  cir.behavior_time &lt;= #{endTime}
        GROUP BY DATE_FORMAT(cir.behavior_time, "%Y-%m-%d") having count(cir.behavior_time) &gt;= #{days}
    </select>

    <!-- 获取面试数量  时间范围内 -->
    <select id="getInterviewCountByProjectId" resultType="int">
        SELECT count(cir.id)
        from crm_interview_record cir
        LEFT JOIN crm_follow_up cfu on cir.follow_id = cfu.id
        where cfu.project_id = #{projectId} and cfu.del_flag = 0 and cir.status != 1
        and cir.behavior_time >= #{beginTime} and  cir.behavior_time &lt;= #{endTime}
    </select>

    <!-- 获取面试记录 通过 followUpId -->
    <select id="getInterviewRecordByFollowUpId" resultMap="resultMap">
        SELECT id, enterprise_id, follow_id, node, status, auth, behavior_time, estimate_time, create_time, create_by, remark, failure
        from crm_interview_record
        where follow_id = #{followUpId}
    </select>

    <!-- 获取个人数据 邀约量 -->
    <select id="getIndividualInviteData" resultMap="voResultMap">
        select cir.id, cir.follow_id, cir.create_time, cir.behavior_time, cp.title,  cc.`name` contactName, cc.phone contactPhone, cu.`name` userName,
        cir.`status`
        from crm_interview_record cir
        left join crm_follow_up cfu on cfu.id = cir.`follow_id`
        left join crm_project cp on cfu.`project_id` = cp.id
        left join crm_contact cc on cc.id = cfu.`contact_id`
        left join crm_user cu on cu.id = cir.create_by
        <where>
            <if test="createBySet != null and createBySet.size > 0">
                and cir.create_by in
                <foreach collection="createBySet" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="createBySet == null or createBySet.size == 0">
                and cir.create_by = #{userId}
            </if>
            <if test="searchType != null and searchType == 'inviteTime'">
                and cir.create_time &gt;= #{beginTime} and cir.create_time &lt;= #{endTime}
            </if>
            <if test="searchType != null and searchType == 'interviewTime'">
                and cir.behavior_time &gt;= #{beginTime} and cir.behavior_time &lt;= #{endTime}
            </if>
            <!-- 面试结果 0/不限  1/待面试  2/未参加  3/未通过  4/已通过 -->
            <if test="interviewResult == 1">
                and cir.status in (0, 2, 4)
            </if>
            <if test="interviewResult == 2">
                and cir.status = 1
            </if>
            <if test="interviewResult == 3">
                and cir.status = 3
            </if>
            <if test="interviewResult == 4">
                and cir.status = 5
            </if>
        </where>
        ORDER BY cir.create_time desc
        limit #{pageBegin}, #{pageSize}
    </select>

    <!-- 获取个人数据 邀约量 数量-->
    <select id="getIndividualInviteDataCount" resultType="int">
        select count(cir.id)
        from crm_interview_record cir
        left join crm_follow_up cfu on cfu.id = cir.`follow_id`
        left join crm_project cp on cfu.`project_id` = cp.id
        left join crm_contact cc on cc.id = cfu.`contact_id`
        left join crm_user cu on cu.id = cir.create_by
        <where>
            <if test="createBySet != null and createBySet.size > 0">
                and cir.create_by in
                <foreach collection="createBySet" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="createBySet == null or createBySet.size == 0">
                and cir.create_by = #{userId}
            </if>
            <if test="searchType != null and searchType == 'inviteTime'">
                and cir.create_time &gt;= #{beginTime} and cir.create_time &lt;= #{endTime}
            </if>
            <if test="searchType != null and searchType == 'interviewTime'">
                and cir.behavior_time &gt;= #{beginTime} and cir.behavior_time &lt;= #{endTime}
            </if>
            <!-- 面试结果 0/不限  1/待面试  2/未参加  3/未通过  4/已通过 -->
            <if test="interviewResult == 1">
                and cir.status in (0, 2, 4)
            </if>
            <if test="interviewResult == 2">
                and cir.status = 1
            </if>
            <if test="interviewResult == 3">
                and cir.status = 3
            </if>
            <if test="interviewResult == 4">
                and cir.status = 5
            </if>
        </where>
    </select>

    <!-- 个人数据 面试量 -->
    <select id="getIndividualInterviewData" resultMap="voResultMap">
        select cir.id, cir.follow_id, cir.create_time, cir.behavior_time, cp.title,  cc.`name` contactName, cc.phone contactPhone, cu.`name` userName,
        cir.`status`
        from crm_interview_record cir
        left join crm_follow_up cfu on cfu.id = cir.`follow_id`
        left join crm_project cp on cfu.`project_id` = cp.id
        left join crm_contact cc on cc.id = cfu.`contact_id`
        left join crm_user cu on cu.id = cir.create_by
        <where>
            <if test="createBySet != null and createBySet.size > 0">
                and cir.create_by in
                <foreach collection="createBySet" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="createBySet == null or createBySet.size == 0">
                and cir.create_by = #{userId}
            </if>
            <if test="searchType != null and searchType == 'inviteTime'">
                and cir.create_time &gt;= #{beginTime} and cir.create_time &lt;= #{endTime}
            </if>
            <if test="searchType != null and searchType == 'interviewTime'">
                and cir.behavior_time &gt;= #{beginTime} and cir.behavior_time &lt;= #{endTime}
            </if>
            <!-- 面试结果 0/不限  3/未通过  4/已通过 -->
            <if test="interviewResult == 0">
                and cir.status in (3, 5)
            </if>
            <if test="interviewResult == 3">
                and cir.status = 3
            </if>
            <if test="interviewResult == 4">
                and cir.status = 5
            </if>
        </where>
        ORDER BY cir.behavior_time desc
        limit #{pageBegin}, #{pageSize}
    </select>

    <!-- 个人数据 面试量 数量-->
    <select id="getIndividualInterviewDataCount" resultType="int">
        select count(cir.id)
        from crm_interview_record cir
        left join crm_follow_up cfu on cfu.id = cir.`follow_id`
        left join crm_project cp on cfu.`project_id` = cp.id
        left join crm_contact cc on cc.id = cfu.`contact_id`
        left join crm_user cu on cu.id = cir.create_by
        <where>
            <if test="createBySet != null and createBySet.size > 0">
                and cir.create_by in
                <foreach collection="createBySet" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="createBySet == null or createBySet.size == 0">
                and cir.create_by = #{userId}
            </if>
            <if test="searchType != null and searchType == 'inviteTime'">
                and cir.create_time &gt;= #{beginTime} and cir.create_time &lt;= #{endTime}
            </if>
            <if test="searchType != null and searchType == 'interviewTime'">
                and cir.behavior_time &gt;= #{beginTime} and cir.behavior_time &lt;= #{endTime}
            </if>
            <!-- 面试结果 0/不限  3/未通过  4/已通过 -->
            <if test="interviewResult == 0">
                and cir.status in (3, 5)
            </if>
            <if test="interviewResult == 3">
                and cir.status = 3
            </if>
            <if test="interviewResult == 4">
                and cir.status = 5
            </if>
        </where>
    </select>

    <!-- 个人数据 offer量 -->
    <select id="getIndividualOfferData" resultMap="voResultMap">
        select cir.id, cir.follow_id, cir.create_time, cir.behavior_time, cp.title,  cc.`name` contactName, cc.phone contactPhone, cu.`name` userName,
        cir.`status`
        from crm_interview_record cir
        left join crm_follow_up cfu on cfu.id = cir.`follow_id`
        left join crm_project cp on cfu.`project_id` = cp.id
        left join crm_contact cc on cc.id = cfu.`contact_id`
        left join crm_user cu on cu.id = cir.create_by
        <where>
            cir.`status` = 5
            <if test="createBySet != null and createBySet.size > 0">
                and cir.create_by in
                <foreach collection="createBySet" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="createBySet == null or createBySet.size == 0">
                and cir.create_by = #{userId}
            </if>
            <if test="searchType != null and searchType == 'inviteTime'">
                and cir.create_time &gt;= #{beginTime} and cir.create_time &lt;= #{endTime}
            </if>
            <if test="searchType != null and searchType == 'interviewTime'">
                and cir.behavior_time &gt;= #{beginTime} and cir.behavior_time &lt;= #{endTime}
            </if>
        </where>
        ORDER BY cir.behavior_time desc
        limit #{pageBegin}, #{pageSize}
    </select>

    <!-- 个人数据 offer量 -->
    <select id="getIndividualOfferDataCount" resultType="int">
        select count(cir.id)
        from crm_interview_record cir
        left join crm_follow_up cfu on cfu.id = cir.`follow_id`
        left join crm_project cp on cfu.`project_id` = cp.id
        left join crm_contact cc on cc.id = cfu.`contact_id`
        left join crm_user cu on cu.id = cir.create_by
        <where>
            cir.behavior_time &gt;= #{beginTime} and cir.behavior_time &lt;= #{endTime} and cir.`status` = 5
            <if test="createBySet != null and createBySet.size > 0">
                and cir.create_by in
                <foreach collection="createBySet" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="createBySet == null or createBySet.size == 0">
                and cir.create_by = #{userId}
            </if>
            <if test="searchType != null and searchType == 'inviteTime'">
                and cir.create_time &gt;= #{beginTime} and cir.create_time &lt;= #{endTime}
            </if>
            <if test="searchType != null and searchType == 'interviewTime'">
                and cir.behavior_time &gt;= #{beginTime} and cir.behavior_time &lt;= #{endTime}
            </if>
        </where>
    </select>

    <select id="getRevokeInterviewList" resultType="com.ssb.approve.model.vo.FollowRevokeVO">
        select
            cir.id,
            cir.behavior_time behaviorTime,
            cc.id contactId,
            cp.title,
            cc.name contactName,
            cc.phone contactPhone,
            (case when cir.status = 1 then '未参加' when cir.status = 3 then '未通过' when cir.status = 5 then '已通过' end) result,
            cu.name userName,
            cc.type,
            cfu.id followId,
            cp.id projectId
        from crm_interview_record cir
        left join crm_follow_up cfu on cfu.id = cir.`follow_id`
        left join crm_project cp on cfu.`project_id` = cp.id
        left join crm_contact cc on cc.id = cfu.`contact_id`
        left join crm_user cu on cu.id = cfu.create_by
        left join crm_entry_record cer on cer.follow_id = cfu.id
        <where>
            (
                cir.enterprise_id = #{enterpriseId} and cp.share_project_id is null
                or
                (cp.share_project_id in (select id from crm_project where enterprise_id = #{enterpriseId} and share_status = 1))
            )
            and (cir.status in (1, 3) or (cir.status = 5 and cer.status in (0, 2)))
            <if test="null != range and range == 1">
                AND cp.master = #{userId}
            </if>
            <!-- 普通检索 -->
            <if test="null != searchType and searchType != ''">
                <if test="searchType == 'contactName'">
                    and cc.name LIKE CONCAT('%', #{search},'%')
                </if>
                <if test="searchType == 'contactPhone'">
                    and cc.phone LIKE CONCAT('%', #{search},'%')
                </if>
            </if>
            <if test="null != title">
                and cp.title LIKE CONCAT('%', #{title},'%')
            </if>
            <if test="null != beginTime and null != endTime">
                and cir.behavior_time &gt;= #{beginTime} and cir.behavior_time &lt;= #{endTime}
            </if>
        </where>
        ORDER BY cir.behavior_time desc
        limit #{pageBegin}, #{pageSize}
    </select>

    <select id="getRevokeInterviewListCount" resultType="java.lang.Integer">
        select
            count(*)
        from crm_interview_record cir
        left join crm_follow_up cfu on cfu.id = cir.`follow_id`
        left join crm_project cp on cfu.`project_id` = cp.id
        left join crm_contact cc on cc.id = cfu.`contact_id`
        left join crm_user cu on cu.id = cfu.create_by
        left join crm_entry_record cer on cer.follow_id = cfu.id
        <where>
            (
                cir.enterprise_id = #{enterpriseId} and cp.share_project_id is null
                or
                (cp.share_project_id in (select id from crm_project where enterprise_id = #{enterpriseId} and share_status = 1))
            )
            and (cir.status in (1, 3) or (cir.status = 5 and cer.status in (0, 2)))
            <if test="null != range and range == 1">
                AND cp.master = #{userId}
            </if>
            <!-- 普通检索 -->
            <if test="null != searchType and searchType != ''">
                <if test="searchType == 'contactName'">
                    and cc.name LIKE CONCAT('%', #{search},'%')
                </if>
                <if test="searchType == 'contactPhone'">
                    and cc.phone LIKE CONCAT('%', #{search},'%')
                </if>
            </if>
            <if test="null != title">
                and cp.title LIKE CONCAT('%', #{title},'%')
            </if>
            <if test="null != beginTime and null != endTime">
                and cir.behavior_time &gt;= #{beginTime} and cir.behavior_time &lt;= #{endTime}
            </if>
        </where>
        ORDER BY cir.behavior_time
    </select>

    <update id="updateRevokeInterviewRecord" parameterType="com.ssb.approve.entity.InterviewRecord">
        UPDATE crm_interview_record
        SET
        `status` = #{status},
        auth = #{auth},
        node = #{node},
        estimate_time = null,
        remark = null,
        failure = null,
        update_by = #{updateBy},
        update_time = #{updateTime}
        WHERE id = #{id}
    </update>

    <select id="getEnterpriseProjectInterviewAuthList" resultMap="voResultMap">
        select cir.id, cir.behavior_time, cp.title, cp.interview_type, cc.id contactId, cir.follow_id, cp.id projectId, cc.name contactName,
            cc.phone contactPhone, cu.name userName, cir.node, cir.status, cir.auth, cp.interview_upper_limit interviewUpperLimit, cc.type,
            case when cir.status = 6 then '猎企放弃项目，跟进中断' else cir.remark end remark, cir.review_user_id, cir.review_time,
            (select name from crm_user where id = cir.review_user_id) as reviewUserName
        from crm_interview_record cir
        left join crm_follow_up cfu on cfu.id = cir.`follow_id`
        left join crm_project cp on cfu.`project_id` = cp.id
        left join crm_contact cc on cc.id = cfu.`contact_id`
        left join crm_user cu on cu.id = cir.update_by and cu.enterprise_id = #{enterpriseId}
        <where>
            cfu.project_id = #{projectId}
            <if test="contactName != null ">
                and cc.name LIKE CONCAT('%', #{contactName},'%')
            </if>
            <if test="followUpUser != null ">
                and cu.name LIKE CONCAT('%', #{followUpUser},'%')
            </if>
            <!-- 待面试 -->
            <if test="interviewStatus == 1">
                and cir.status in (0, 2)
            </if>
            <!-- 未通过 -->
            <if test="interviewStatus == 2">
                and cir.status = 3
            </if>
            <!-- 未参加 -->
            <if test="interviewStatus == 3">
                and cir.status = 1
            </if>
            <!-- 已通过/发offer -->
            <if test="interviewStatus == 4">
                and cir.status = 5
            </if>
            <!-- 已中断 -->
            <if test="interviewStatus == 5">
                and cir.status = 6
            </if>
            <if test="null != beginTime and null != endTime">
                <if test="timeType != null and timeType == 1">
                    and cir.behavior_time &gt;= #{beginTime} and cir.behavior_time &lt;= #{endTime}
                </if>
                <if test="timeType != null and timeType == 2">
                    and cir.review_time &gt;= #{beginTime} and cir.review_time &lt;= #{endTime}
                </if>
            </if>
            <if test="null != authStatus">
                and cir.auth = #{authStatus}
            </if>
        </where>
        ORDER BY cir.behavior_time
        limit #{pageBegin}, #{pageSize}
    </select>

    <select id="getEnterpriseProjectInterviewAuthListCount" resultType="java.lang.Integer">
        select IFNULL(count(cir.id), 0)
        from crm_interview_record cir
        left join crm_follow_up cfu on cfu.id = cir.`follow_id`
        left join crm_project cp on cfu.`project_id` = cp.id
        left join crm_contact cc on cc.id = cfu.`contact_id`
        left join crm_user cu on cu.id = cir.update_by and cu.enterprise_id = #{enterpriseId}
        <where>
            cfu.project_id = #{projectId}
            <if test="contactName != null ">
                and cc.name LIKE CONCAT('%', #{contactName},'%')
            </if>
            <if test="followUpUser != null ">
                and cu.name LIKE CONCAT('%', #{followUpUser},'%')
            </if>
            <!-- 待面试 -->
            <if test="interviewStatus == 1">
                and cir.status in (0, 2)
            </if>
            <!-- 未通过 -->
            <if test="interviewStatus == 2">
                and cir.status = 3
            </if>
            <!-- 未参加 -->
            <if test="interviewStatus == 3">
                and cir.status = 1
            </if>
            <!-- 已通过/发offer -->
            <if test="interviewStatus == 4">
                and cir.status = 5
            </if>
            <!-- 已中断 -->
            <if test="interviewStatus == 5">
                and cir.status = 6
            </if>
            <if test="null != beginTime and null != endTime">
                <if test="timeType != null and timeType == 1">
                    and cir.behavior_time &gt;= #{beginTime} and cir.behavior_time &lt;= #{endTime}
                </if>
                <if test="timeType != null and timeType == 2">
                    and cir.review_time &gt;= #{beginTime} and cir.review_time &lt;= #{endTime}
                </if>
            </if>
            <if test="null != authStatus">
                and cir.auth = #{authStatus}
            </if>
        </where>
    </select>

    <select id="getInvitationForInterviewData" parameterType="com.ssb.approve.model.dto.RecruitmentDataDTO"
            resultType="com.ssb.approve.model.vo.StatisticalChartDataVO">
        SELECT
            count(*) as count,
	        DATE_FORMAT(cir.create_time, '%m-%d') AS date
        FROM
            crm_follow_up cfu
                INNER JOIN crm_interview_record cir ON cfu.id = cir.follow_id
        WHERE
            <if test="type == 1">
                cir.create_by = #{userId}
            </if>
            <if test="type == 2">
                cir.create_by in (
                <foreach collection="userIdList" item="userId" separator=",">
                    #{userId}
                </foreach>
                )
            </if>
          AND cir.create_time BETWEEN #{timeStart} and #{timeEnd}
        GROUP BY
            DATE_FORMAT(cir.create_time, '%m-%d')
        ORDER BY
            date;
    </select>

    <select id="getInvitationForInterviewCount" resultType="java.lang.Integer" parameterType="com.ssb.approve.model.dto.ConversionRateDataDTO">
        SELECT
            count(*) as count
        FROM
            crm_follow_up cfu
                INNER JOIN crm_interview_record cir ON cfu.id = cir.follow_id
        WHERE
            <if test="type == 1">
                cir.create_by = #{userId}
            </if>
            <if test="type == 2">
                cir.create_by in (
                <foreach collection="userIdList" item="userId" separator=",">
                    #{userId}
                </foreach>
                )
            </if>
        AND cir.create_time between #{timeStart} and #{timeEnd}
    </select>

    <select id="getInterviewData" parameterType="com.ssb.approve.model.dto.RecruitmentDataDTO"
            resultType="com.ssb.approve.model.vo.StatisticalChartDataVO">
        SELECT
            count(*) as count,
	        DATE_FORMAT(behavior_time, '%m-%d') AS date
        FROM
            crm_interview_record
        WHERE
            <if test="type == 1">
                create_by = #{userId}
            </if>
            <if test="type == 2">
                create_by in (
                <foreach collection="userIdList" item="userId" separator=",">
                    #{userId}
                </foreach>
                )
            </if>
          AND STATUS in (3,5)
          AND behavior_time BETWEEN #{timeStart} and #{timeEnd}
        GROUP BY
            DATE_FORMAT(behavior_time, '%m-%d')
        ORDER BY
            date;
    </select>

    <select id="getInterviewCount" parameterType="com.ssb.approve.model.dto.ConversionRateDataDTO"
            resultType="java.lang.Integer">
        SELECT
            count(*) as count
        FROM
            crm_interview_record
        WHERE
            <if test="type == 1">
                create_by = #{userId}
            </if>
            <if test="type == 2">
                create_by in (
                <foreach collection="userIdList" item="userId" separator=",">
                    #{userId}
                </foreach>
                )
            </if>
          AND STATUS in (3, 5)
          AND behavior_time BETWEEN #{timeStart} and #{timeEnd}
    </select>

    <select id="getOfferCount" parameterType="com.ssb.approve.model.dto.ConversionRateDataDTO"
            resultType="java.lang.Integer">
        SELECT
            count(*) as count
        FROM
            crm_interview_record
        WHERE
            <if test="type == 1">
                create_by = #{userId}
            </if>
            <if test="type == 2">
                create_by in (
                <foreach collection="userIdList" item="userId" separator=",">
                    #{userId}
                </foreach>
                )
            </if>
          AND STATUS = 5
          AND behavior_time BETWEEN #{timeStart} and #{timeEnd}
    </select>

    <select id="getInvitationForInterviewDataList" parameterType="com.ssb.approve.model.dto.RecruitmentDataListDTO"
            resultType="com.ssb.approve.model.vo.RecruitmentDataListVO">
        SELECT
            cc.id contactId,
            cc.name contactName,
            cc.phone phone,
            cp.id projectId,
            cp.title,
            ccu.id customerId,
            ccu.`name` customerName,
            cir.behavior_time interviewTime,
            cir.create_time invitationForInterviewTime,
            cir.status interviewStatus,
            cu.id followUserId,
            cu.name followUserName,
            cp.source
        FROM
            crm_follow_up cfu
                INNER JOIN crm_interview_record cir ON cfu.id = cir.follow_id
                INNER JOIN crm_user cu ON cu.id = cfu.create_by
                INNER JOIN crm_contact cc on cc.id = cfu.contact_id
                INNER JOIN crm_project cp on cfu.project_id = cp.id
                INNER JOIN crm_customer ccu on ccu.id = cp.customer_id
        WHERE
            <if test="type == 1">
                cfu.create_by = #{userId}
            </if>
            <if test="type == 2">
                cfu.create_by in (
                <foreach collection="userIdList" item="userId" separator=",">
                    #{userId}
                </foreach>
                )
            </if>
            AND cu.del_flag = 0
            AND cfu.del_flag = 0
            <if test="interviewStatus != null">
                <choose>
                    <when test="interviewStatus == 0">
                        AND cir.status != 6
                    </when>
                    <when test="interviewStatus == 1">
                        AND cir.status in (0, 2, 4)
                    </when>
                    <when test="interviewStatus == 2">
                        AND cir.status = 1
                    </when>
                    <when test="interviewStatus == 3">
                        AND cir.status = 3
                    </when>
                    <when test="interviewStatus == 4">
                        AND cir.status = 5
                    </when>
                </choose>
            </if>
            <if test="userName != null">
                AND cc.name like CONCAT('%', #{userName},'%')
            </if>
            <if test="phone != null">
                AND cc.phone like CONCAT('%', #{phone},'%')
            </if>
            <if test="title != null">
                AND cp.title like CONCAT('%', #{title},'%')
            </if>
            <if test="customerName != null">
                AND ccu.name like CONCAT('%', #{customerName},'%')
            </if>
            <if test="followUser != null">
                AND cu.name like CONCAT('%', #{followUser},'%')
            </if>
            <if test="timeStart != null and timeEnd != null">
                AND cir.behavior_time between #{timeStart} and #{timeEnd}
            </if>
            <if test="inviteTimeStart != null and inviteTimeEnd != null">
                AND cir.create_time between #{inviteTimeStart} and #{inviteTimeEnd}
            </if>
        ORDER BY cir.behavior_time desc
        limit #{pageBegin}, #{pageSize}
    </select>

    <select id="getInvitationForInterviewDataCount" parameterType="com.ssb.approve.model.dto.RecruitmentDataListDTO"
            resultType="java.lang.Integer">
        SELECT
            count(*)
        FROM
            crm_follow_up cfu
                INNER JOIN crm_interview_record cir ON cfu.id = cir.follow_id
                INNER JOIN crm_user cu ON cu.id = cfu.create_by
                INNER JOIN crm_contact cc on cc.id = cfu.contact_id
                INNER JOIN crm_project cp on cfu.project_id = cp.id
                INNER JOIN crm_customer ccu on ccu.id = cp.customer_id
        WHERE
            <if test="type == 1">
                cfu.create_by = #{userId}
            </if>
            <if test="type == 2">
                cfu.create_by in (
                <foreach collection="userIdList" item="userId" separator=",">
                    #{userId}
                </foreach>
                )
            </if>
            AND cu.del_flag = 0
            AND cfu.del_flag = 0
            <if test="interviewStatus != null">
                <choose>
                    <when test="interviewStatus == 0">
                        AND cir.status != 6
                    </when>
                    <when test="interviewStatus == 1">
                        AND cir.status in (0, 2, 4)
                    </when>
                    <when test="interviewStatus == 2">
                        AND cir.status = 1
                    </when>
                    <when test="interviewStatus == 3">
                        AND cir.status = 3
                    </when>
                    <when test="interviewStatus == 4">
                        AND cir.status = 5
                    </when>
                </choose>
            </if>
            <if test="userName != null">
                AND cc.name like CONCAT('%', #{userName},'%')
            </if>
            <if test="phone != null">
                AND cc.phone like CONCAT('%', #{phone},'%')
            </if>
            <if test="title != null">
                AND cp.title like CONCAT('%', #{title},'%')
            </if>
            <if test="customerName != null">
                AND ccu.name like CONCAT('%', #{customerName},'%')
            </if>
            <if test="followUser != null">
                AND cu.name like CONCAT('%', #{followUser},'%')
            </if>
            <if test="timeStart != null and timeEnd != null">
                AND cir.behavior_time between #{timeStart} and #{timeEnd}
            </if>
            <if test="inviteTimeStart != null and inviteTimeEnd != null">
                AND cir.create_time between #{inviteTimeStart} and #{inviteTimeEnd}
            </if>
    </select>

    <select id="getInterviewDataList" parameterType="com.ssb.approve.model.dto.RecruitmentDataListDTO"
            resultType="com.ssb.approve.model.vo.RecruitmentDataListVO">
        SELECT
            cc.id contactId,
            cc.name contactName,
            cc.phone phone,
            cp.id projectId,
            cp.title,
            ccu.id customerId,
            ccu.`name` customerName,
            cir.behavior_time interviewTime,
            cir.create_time invitationForInterviewTime,
            cir.status interviewStatus,
            cu.id followUserId,
            cu.name followUserName,
            cp.source
        FROM
            crm_follow_up cfu
                INNER JOIN crm_interview_record cir ON cfu.id = cir.follow_id
                INNER JOIN crm_user cu ON cu.id = cfu.create_by
                INNER JOIN crm_contact cc on cc.id = cfu.contact_id
                INNER JOIN crm_project cp on cfu.project_id = cp.id
                INNER JOIN crm_customer ccu on ccu.id = cp.customer_id
        WHERE
            <if test="type == 1">
                cfu.create_by = #{userId}
            </if>
            <if test="type == 2">
                cfu.create_by in (
                <foreach collection="userIdList" item="userId" separator=",">
                    #{userId}
                </foreach>
                )
            </if>
            AND cu.del_flag = 0
            AND cfu.del_flag = 0
            <if test="interviewStatus != null">
                <choose>
                    <when test="interviewStatus == 0">
                        AND cir.status in (3, 5)
                    </when>
                    <when test="interviewStatus == 1">
                        AND cir.status = 3
                    </when>
                    <when test="interviewStatus == 2">
                        AND cir.status = 5
                    </when>
                </choose>
            </if>
            <if test="userName != null">
                AND cc.name like CONCAT('%', #{userName},'%')
            </if>
            <if test="phone != null">
                AND cc.phone like CONCAT('%', #{phone},'%')
            </if>
            <if test="title != null">
                AND cp.title like CONCAT('%', #{title},'%')
            </if>
            <if test="customerName != null">
                AND ccu.name like CONCAT('%', #{customerName},'%')
            </if>
            <if test="followUser != null">
                AND cu.name like CONCAT('%', #{followUser},'%')
            </if>
            <if test="timeStart != null and timeEnd != null">
                AND cir.behavior_time between #{timeStart} and #{timeEnd}
            </if>
            <if test="inviteTimeStart != null and inviteTimeEnd != null">
                AND cir.create_time between #{inviteTimeStart} and #{inviteTimeEnd}
            </if>
        ORDER BY cir.behavior_time desc
        limit #{pageBegin}, #{pageSize}
    </select>

    <select id="getInterviewDataCount" parameterType="com.ssb.approve.model.dto.RecruitmentDataListDTO"
            resultType="java.lang.Integer">
        SELECT
            count(*)
        FROM
            crm_follow_up cfu
                INNER JOIN crm_interview_record cir ON cfu.id = cir.follow_id
                INNER JOIN crm_user cu ON cu.id = cfu.create_by
                INNER JOIN crm_contact cc on cc.id = cfu.contact_id
                INNER JOIN crm_project cp on cfu.project_id = cp.id
                INNER JOIN crm_customer ccu on ccu.id = cp.customer_id
        WHERE
            <if test="type == 1">
                cfu.create_by = #{userId}
            </if>
            <if test="type == 2">
                cfu.create_by in (
                <foreach collection="userIdList" item="userId" separator=",">
                    #{userId}
                </foreach>
                )
            </if>
            AND cu.del_flag = 0
            AND cfu.del_flag = 0
            <if test="interviewStatus != null">
                <choose>
                    <when test="interviewStatus == 0">
                        AND cir.status in (3, 5)
                    </when>
                    <when test="interviewStatus == 1">
                        AND cir.status = 3
                    </when>
                    <when test="interviewStatus == 2">
                        AND cir.status = 5
                    </when>
                </choose>
            </if>
            <if test="userName != null">
                AND cc.name like CONCAT('%', #{userName},'%')
            </if>
            <if test="phone != null">
                AND cc.phone like CONCAT('%', #{phone},'%')
            </if>
            <if test="title != null">
                AND cp.title like CONCAT('%', #{title},'%')
            </if>
            <if test="customerName != null">
                AND ccu.name like CONCAT('%', #{customerName},'%')
            </if>
            <if test="followUser != null">
                AND cu.name like CONCAT('%', #{followUser},'%')
            </if>
            <if test="timeStart != null and timeEnd != null">
                AND cir.behavior_time between #{timeStart} and #{timeEnd}
            </if>
            <if test="inviteTimeStart != null and inviteTimeEnd != null">
                AND cir.create_time between #{inviteTimeStart} and #{inviteTimeEnd}
            </if>
    </select>

    <select id="getOfferDataList" parameterType="com.ssb.approve.model.dto.RecruitmentDataListDTO"
            resultType="com.ssb.approve.model.vo.RecruitmentDataListVO">
        SELECT
            cc.id contactId,
            cc.name contactName,
            cc.phone phone,
            cp.id projectId,
            cp.title,
            ccu.id customerId,
            ccu.`name` customerName,
            cir.behavior_time interviewTime,
            cir.create_time invitationForInterviewTime,
            cu.id followUserId,
            cu.name followUserName,
            cp.source
        FROM
            crm_follow_up cfu
                INNER JOIN crm_interview_record cir ON cfu.id = cir.follow_id
                INNER JOIN crm_user cu ON cu.id = cfu.create_by
                INNER JOIN crm_contact cc on cc.id = cfu.contact_id
                INNER JOIN crm_project cp on cfu.project_id = cp.id
                INNER JOIN crm_customer ccu on ccu.id = cp.customer_id
        WHERE
            <if test="type == 1">
                cfu.create_by = #{userId}
            </if>
            <if test="type == 2">
                cfu.create_by in (
                <foreach collection="userIdList" item="userId" separator=",">
                    #{userId}
                </foreach>
                )
            </if>
            AND cu.del_flag = 0
            AND cfu.del_flag = 0
            AND cir.status = 5
            <if test="userName != null">
                AND cc.name like CONCAT('%', #{userName},'%')
            </if>
            <if test="phone != null">
                AND cc.phone like CONCAT('%', #{phone},'%')
            </if>
            <if test="title != null">
                AND cp.title like CONCAT('%', #{title},'%')
            </if>
            <if test="customerName != null">
                AND ccu.name like CONCAT('%', #{customerName},'%')
            </if>
            <if test="followUser != null">
                AND cu.name like CONCAT('%', #{followUser},'%')
            </if>
            <if test="timeStart != null and timeEnd != null">
                AND cir.behavior_time between #{timeStart} and #{timeEnd}
            </if>
            <if test="inviteTimeStart != null and inviteTimeEnd != null">
                AND cir.create_time between #{inviteTimeStart} and #{inviteTimeEnd}
            </if>
        ORDER BY cir.behavior_time desc
        limit #{pageBegin}, #{pageSize}
    </select>

    <select id="getOfferDataCount" parameterType="com.ssb.approve.model.dto.RecruitmentDataListDTO"
            resultType="java.lang.Integer">
        SELECT
            count(*)
        FROM
            crm_follow_up cfu
                INNER JOIN crm_interview_record cir ON cfu.id = cir.follow_id
                INNER JOIN crm_user cu ON cu.id = cfu.create_by
                INNER JOIN crm_contact cc on cc.id = cfu.contact_id
                INNER JOIN crm_project cp on cfu.project_id = cp.id
                INNER JOIN crm_customer ccu on ccu.id = cp.customer_id
        WHERE
            <if test="type == 1">
                cfu.create_by = #{userId}
            </if>
            <if test="type == 2">
                cfu.create_by in (
                <foreach collection="userIdList" item="userId" separator=",">
                    #{userId}
                </foreach>
                )
            </if>
            AND cu.del_flag = 0
            AND cfu.del_flag = 0
            AND cir.status = 5
            <if test="userName != null">
                AND cc.name like CONCAT('%', #{userName},'%')
            </if>
            <if test="phone != null">
                AND cc.phone like CONCAT('%', #{phone},'%')
            </if>
            <if test="title != null">
                AND cp.title like CONCAT('%', #{title},'%')
            </if>
            <if test="customerName != null">
                AND ccu.name like CONCAT('%', #{customerName},'%')
            </if>
            <if test="followUser != null">
                AND cu.name like CONCAT('%', #{followUser},'%')
            </if>
            <if test="timeStart != null and timeEnd != null">
                AND cir.behavior_time between #{timeStart} and #{timeEnd}
            </if>
            <if test="inviteTimeStart != null and inviteTimeEnd != null">
                AND cir.create_time between #{inviteTimeStart} and #{inviteTimeEnd}
            </if>
    </select>

    <select id="getDataStatisticsInterviewCount" resultType="java.lang.Integer">
        SELECT
            count(*)
        FROM
            crm_interview_record
        WHERE
            create_by IN (
                <foreach collection="dto.userIdList" item="userId" separator=",">
                    #{userId}
                </foreach>
                )
            <if test="dto.timeType != 0">
                <choose>
                    <when test="sourceType == 1">
                        AND behavior_time between #{dto.timeStart} and #{dto.timeEnd}
                    </when>
                    <when test="sourceType == 2">
                        AND behavior_time between #{dto.compareTimeStart} and #{dto.compareTimeEnd}
                    </when>
                </choose>
            </if>
            AND status in (3,5)
    </select>
    <select id="getEnterpriseDataStatisticsInterviewCount" resultType="java.lang.Integer">
        SELECT
            count(*)
        FROM
            crm_interview_record cir
            INNER JOIN crm_follow_up cfu ON cfu.id = cir.follow_id
            INNER JOIN crm_project cp ON cfu.project_id = cp.id
            INNER JOIN crm_customer cc ON cp.customer_id = cc.id
        WHERE cc.customer_enterprise_id =  #{dto.enterpriseId}
            <if test="dto.timeType != 0">
                AND cir.behavior_time between #{dto.timeStart} and #{dto.timeEnd}
            </if>
            AND cir.status in (3,5)
    </select>

    <select id="getWaitingForInterviewList" resultType="com.ssb.approve.model.vo.InterviewRecordVO">
        select t1.id, t2.project_id projectId, t2.contact_id contactId, t2.update_by updateBy, t1.behavior_time behaviorTime,
               (select title from crm_project where id = t2.project_id) as title,
               (select name from crm_user where id = t2.contact_id) as contactName,
               (select name from crm_user where id = t2.update_by) as userName
        from crm_interview_record t1
        left join crm_follow_up t2 on t1.follow_id = t2.id
        where
            t1.id in
            <foreach collection="ids" item="ite" separator="," close=")" open="(">
                #{item}
            </foreach>
            <if test="null != maxId and maxId != 0">
                and t1.id <![CDATA[ < ]]> #{maxId}
            </if>
        order by t1.id desc
        limit 20
    </select>

    <select id="getWaitingForAgreedInterviewList" resultType="com.ssb.approve.model.vo.InterviewRecordVO">
        select t1.id, t2.project_id projectId, t2.contact_id contactId, t2.update_by updateBy, t1.behavior_time behaviorTime,
        (select title from crm_project where id = t2.project_id) as title,
        (select name from crm_user where id = t2.contact_id) as contactName,
        (select name from crm_user where id = t2.update_by) as userName,
        cir.status
        from crm_interview_record t1
        left join crm_follow_up t2 on t1.follow_id = t2.id
        where
        t1.id in
        <foreach collection="ids" item="ite" separator="," close=")" open="(">
            #{item}
        </foreach>
        <if test="null != maxId and maxId != 0">
            and t1.id <![CDATA[ < ]]> #{maxId}
        </if>
        order by t1.id desc
        limit 20
    </select>

</mapper>