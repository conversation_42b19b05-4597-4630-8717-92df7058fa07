<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ssb.approve.dao.DataStatisticDAO">

    <resultMap id="voResultMap" type="com.ssb.approve.model.vo.TeamFollowUpDataVO">

    </resultMap>

    <!-- 获取团队 统计数据 -->
    <select id="getTeamFollowUpData" resultMap="voResultMap">
        select *
        from (
                SELECT cu.id userId, cu.phone, cu.dept_id, cu.`name`, cu.`level`, cu.`status`, cd.`name` deptName,
                (SELECT count(id) FROM crm_interview_record where create_time &gt;= #{beginTime} and create_time &lt;= #{endTime} and create_by = cu.id) inviteCount,
                (SELECT count(id) FROM crm_interview_record where behavior_time &gt;= #{beginTime} and behavior_time &lt;= #{endTime} and `status` in (3, 5) and create_by = cu.id) interviewCount,
                (SELECT count(id) FROM crm_interview_record where behavior_time &gt;= #{beginTime} and behavior_time &lt;= #{endTime} and `status` = 5 and create_by = cu.id) offerCount,
                (SELECT count(id) FROM crm_entry_record where behavior_time &gt;= #{beginTime} and behavior_time &lt;= #{endTime} and `status` = 1 and create_by = cu.id) entryCount,
                (
                SELECT count(a.id) FROM (
                SELECT cfu.id, max(csd.actual_settle_time) settle_time, cfu.create_by
                FROM crm_follow_up cfu
                left JOIN crm_settle_details csd on csd.follow_id = cfu.id and csd.del_flag = 0
                where cfu.current_status = 10
                GROUP BY cfu.id
                ) a where a.settle_time &gt;= #{beginTime} and a.settle_time &lt;= #{endTime} and a.create_by = cu.id
                )  settleCount,
                (SELECT IFNULL( sum(call_count), 0)  from crm_user_everyday_workload where user_id = cu.id and statistics_date &gt;= #{beginTime} and statistics_date &lt;= #{endTime}) callCount,
                (SELECT IFNULL(sum(wechat_count), 0)  from crm_user_everyday_workload where user_id = cu.id and statistics_date &gt;= #{beginTime} and statistics_date &lt;= #{endTime}) wechatCount,
                (SELECT IFNULL(count(*), 0) from crm_contact where create_by = cu.id and create_time &gt;= #{beginTime} and create_time &lt;= #{endTime}) resumeCount,
                (SELECT IFNULL( sum(contract_count),0) from crm_user_everyday_workload where user_id = cu.id and statistics_date &gt;= #{beginTime} and statistics_date &lt;= #{endTime}) contractCount,
                (SELECT IFNULL( SUM(CASE WHEN money = 0 THEN integral ELSE money END), 0) from crm_commission_gave_log where commission_user = cu.id and del_flag = 0 and commission_time >= #{beginTime} and commission_time &lt;= #{endTime}) integral,
                cu.create_time
                FROM crm_user cu
                LEFT JOIN crm_dept cd on cd.id = cu.dept_id
                where
                <!-- 选择团队 -->
                <if test="deptIds != null and deptIds.size > 0">
                    cu.dept_id in
                    <foreach collection="deptIds" open="(" close=")" separator="," item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="deptIds == null or deptIds.size == 0">
                    cu.id = #{userId}
                </if>
                <if test="userStatus != null">
                   and cu.status = #{userStatus}
                </if>

                and cu.create_time &lt;= #{endTime}
                and
                case
                    when cu.status = 1
                then
                    cu.update_time &gt;= #{beginTime}
                else
                    cu.update_time
                end
        ) a
        <!-- 排序 -->
        <choose>
            <when test="sortFiled != null and sortType != null">
                <!-- 邀约量 -->
                <if test="sortFiled == 'inviteCount'">
                    <if test="sortType == 0">
                        ORDER BY a.inviteCount ASC
                    </if>
                    <if test="sortType == 1">
                        ORDER BY a.inviteCount desc
                    </if>
                </if>
                <!-- 到面量 -->
                <if test="sortFiled == 'interviewCount'">
                    <if test="sortType == 0">
                        ORDER BY a.interviewCount ASC
                    </if>
                    <if test="sortType == 1">
                        ORDER BY a.interviewCount desc
                    </if>
                </if>
                <!-- offer量 -->
                <if test="sortFiled == 'offerCount'">
                    <if test="sortType == 0">
                        ORDER BY a.offerCount ASC
                    </if>
                    <if test="sortType == 1">
                        ORDER BY a.offerCount desc
                    </if>
                </if>
                <!-- 入职量 -->
                <if test="sortFiled == 'entryCount'">
                    <if test="sortType == 0">
                        ORDER BY a.entryCount ASC
                    </if>
                    <if test="sortType == 1">
                        ORDER BY a.entryCount desc
                    </if>
                </if>
                <!-- 结算量 -->
                <if test="sortFiled == 'settleCount'">
                    <if test="sortType == 0">
                        ORDER BY a.settleCount ASC
                    </if>
                    <if test="sortType == 1">
                        ORDER BY a.settleCount desc
                    </if>
                </if>
                <!-- 电话量 -->
                <if test="sortFiled == 'callCount'">
                    <if test="sortType == 0">
                        ORDER BY a.callCount ASC
                    </if>
                    <if test="sortType == 1">
                        ORDER BY a.callCount desc
                    </if>
                </if>
                <!-- 微信量 -->
                <if test="sortFiled == 'wechatCount'">
                    <if test="sortType == 0">
                        ORDER BY a.wechatCount ASC
                    </if>
                    <if test="sortType == 1">
                        ORDER BY a.wechatCount desc
                    </if>
                </if>
                <!-- 简历量 -->
                <if test="sortFiled == 'resumeCount'">
                    <if test="sortType == 0">
                        ORDER BY a.resumeCount ASC
                    </if>
                    <if test="sortType == 1">
                        ORDER BY a.resumeCount desc
                    </if>
                </if>
                <!-- 转介绍量 -->
                <if test="sortFiled == 'introduceCount'">
                    <if test="sortType == 0">
                        ORDER BY a.introduceCount ASC
                    </if>
                    <if test="sortType == 1">
                        ORDER BY a.introduceCount desc
                    </if>
                </if>
                <!-- 积分数量 -->
                <if test="sortFiled == 'integral'">
                    <if test="sortType == 0">
                        ORDER BY a.integral ASC
                    </if>
                    <if test="sortType == 1">
                        ORDER BY a.integral desc
                    </if>
                </if>
                <!-- 提成金额 -->
                <if test="sortFiled == 'commission'">
                    <if test="sortType == 0">
                        ORDER BY a.commission ASC
                    </if>
                    <if test="sortType == 1">
                        ORDER BY a.commission desc
                    </if>
                </if>
            </when>
            <otherwise>
                ORDER BY a.create_time desc
            </otherwise>
        </choose>
        limit #{pageBegin}, #{pageSize}

    </select>

    <!-- 获取团队跟进  数据 数量 -->
    <select id="getTeamFollowUpDataCount" resultType="integer">
        SELECT count(cu.id)
        FROM crm_user cu
        LEFT JOIN crm_dept cd on cd.id = cu.dept_id
        where
        <!-- 选择团队 -->
        <if test="deptIds != null and deptIds.size > 0">
            cu.dept_id in
            <foreach collection="deptIds" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="deptIds == null or deptIds.size == 0">
            cu.id = #{userId}
        </if>
        <if test="userStatus != null">
            and cu.status = #{userStatus}
        </if>

        and cu.create_time &lt;= #{endTime} and
        case
            when cu.status = 1 then
                cu.update_time &gt;= #{beginTime}
            else
                cu.update_time
        end
    </select>

    <!-- 获取团队跟进数据 总数 -->
    <select id="getTeamFollowUpDataTotal" resultMap="voResultMap">

        select sum(uf.inviteCount) inviteCount,  sum(uf.interviewCount) interviewCount, sum(uf.offerCount) offerCount,
        sum(uf.entryCount) entryCount, sum(uf.settleCount) settleCount, sum(uf.callCount) callCount, sum(uf.wechatCount) wechatCount,
        sum(uf.resumeCount) resumeCount, sum(uf.contractCount) contractCount, sum(uf.integral) integral
        from (
            SELECT
            (SELECT count(id) FROM crm_interview_record where create_time &gt;= #{beginTime} and create_time &lt;= #{endTime} and create_by = cu.id) inviteCount,
            (SELECT count(id) FROM crm_interview_record where behavior_time &gt;= #{beginTime} and behavior_time &lt;= #{endTime} and `status` in (3, 5) and create_by = cu.id) interviewCount,
            (SELECT count(id) FROM crm_interview_record where behavior_time &gt;= #{beginTime} and behavior_time &lt;= #{endTime} and `status` = 5 and create_by = cu.id) offerCount,
            (SELECT count(id) FROM crm_entry_record where behavior_time &gt;= #{beginTime} and behavior_time &lt;= #{endTime} and `status` = 1 and create_by = cu.id) entryCount,
            (
                SELECT count(a.id) FROM (
                    SELECT cfu.id, max(csd.actual_settle_time) settle_time, cfu.create_by
                    FROM crm_follow_up cfu
                    left JOIN crm_settle_details csd on csd.follow_id = cfu.id and csd.del_flag = 0
                    where cfu.current_status = 10
                    GROUP BY cfu.id
                ) a where a.settle_time &gt;= #{beginTime} and a.settle_time &lt;= #{endTime} and a.create_by = cu.id
            )  settleCount,
            (SELECT IFNULL( sum(call_count), 0)  from crm_user_everyday_workload where user_id = cu.id and statistics_date &gt;= #{beginTime} and statistics_date &lt;= #{endTime}) callCount,
            (SELECT IFNULL(sum(wechat_count), 0)  from crm_user_everyday_workload where user_id = cu.id and statistics_date &gt;= #{beginTime} and statistics_date &lt;= #{endTime}) wechatCount,
            (SELECT IFNULL(count(*), 0) from crm_contact where create_by = cu.id and create_time &gt;= #{beginTime} and create_time &lt;= #{endTime}) resumeCount,
            (SELECT IFNULL( sum(contract_count),0) from crm_user_everyday_workload where user_id = cu.id and statistics_date &gt;= #{beginTime} and statistics_date &lt;= #{endTime}) contractCount,
            (SELECT IFNULL( SUM(CASE WHEN money = 0 THEN integral ELSE money END), 0) from crm_commission_gave_log where commission_user = cu.id and del_flag = 0 and commission_time >= #{beginTime} and commission_time &lt;= #{endTime}) integral
            FROM crm_user cu
            LEFT JOIN crm_dept cd on cd.id = cu.dept_id
            where
            <!-- 选择团队 -->
            <if test="deptIds != null and deptIds.size > 0">
                cu.dept_id in
                <foreach collection="deptIds" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="deptIds == null or deptIds.size == 0">
                cu.id = #{userId}
            </if>
            <if test="userStatus != null">
                and cu.status = #{userStatus}
            </if>
            and cu.create_time &lt;= #{endTime} and
            case
                when cu.status = 1 then
                    cu.update_time &gt;= #{beginTime}
                else
                    cu.update_time
            end
        ) uf

    </select>


    <!-- 获取个人团队数据 总数 -->
    <select id="getIndividualDataTotal" resultMap="voResultMap">

        select sum(uf.inviteCount) inviteCount,  sum(uf.interviewCount) interviewCount, sum(uf.offerCount) offerCount,
        sum(uf.entryCount) entryCount, sum(uf.settleCount) settleCount, sum(uf.callCount) callCount, sum(uf.wechatCount) wechatCount,
        sum(uf.resumeCount) resumeCount, sum(uf.contractCount) contractCount, sum(uf.integral) integral
        from (
            SELECT
            <choose>
                <!-- 查询入口  0/邀约量  1/到面量  2/offer量 -->
                <when test="queryType == 0 and searchType == 'inviteTime'">
                    (SELECT count(id) FROM crm_interview_record where create_time &gt;= #{beginTime} and create_time &lt;= #{endTime} and create_by = cu.id
                        <!-- 面试结果 0/不限  1/待面试  2/未参加  3/未通过  4/已通过 -->
                        <if test="interviewResult == 1">
                            and status in (0, 2, 4)
                        </if>
                        <if test="interviewResult == 2">
                            and status = 1
                        </if>
                        <if test="interviewResult == 3">
                            and status = 3
                        </if>
                        <if test="interviewResult == 4">
                            and status = 5
                        </if>
                    ) inviteCount,
                    (SELECT count(id) FROM crm_interview_record where behavior_time &gt;= #{beginTime} and behavior_time &lt;= #{endTime} and `status` in (3, 5) and create_by = cu.id) interviewCount,
                    (SELECT count(id) FROM crm_interview_record where behavior_time &gt;= #{beginTime} and behavior_time &lt;= #{endTime} and `status` = 5 and create_by = cu.id) offerCount,
                </when>
                <when test="queryType == 0 and searchType == 'interviewTime'">
                    (SELECT count(id) FROM crm_interview_record where create_time &gt;= #{beginTime} and create_time &lt;= #{endTime} and create_by = cu.id
                        <!-- 面试结果 0/不限  1/待面试  2/未参加  3/未通过  4/已通过 -->
                        <if test="interviewResult == 1">
                            and status in (0, 2, 4)
                        </if>
                        <if test="interviewResult == 2">
                            and status = 1
                        </if>
                        <if test="interviewResult == 3">
                            and status = 3
                        </if>
                        <if test="interviewResult == 4">
                            and status = 5
                        </if>
                    ) inviteCount,
                    (SELECT count(id) FROM crm_interview_record where behavior_time &gt;= #{beginTime} and behavior_time &lt;= #{endTime} and `status` in (3, 5) and create_by = cu.id) interviewCount,
                    (SELECT count(id) FROM crm_interview_record where behavior_time &gt;= #{beginTime} and behavior_time &lt;= #{endTime} and `status` = 5 and create_by = cu.id) offerCount,
                </when>
                <when test="queryType == 1 and searchType == 'inviteTime'">
                    (SELECT count(id) FROM crm_interview_record where create_time &gt;= #{beginTime} and create_time &lt;= #{endTime} and create_by = cu.id) inviteCount,
                    (SELECT count(id) FROM crm_interview_record where behavior_time &gt;= #{beginTime} and behavior_time &lt;= #{endTime} and create_by = cu.id
                        <!-- 面试结果 0/不限  3/未通过  4/已通过 -->
                        <if test="interviewResult == 3">
                            and status = 3
                        </if>
                        <if test="interviewResult == 4">
                            and status = 5
                        </if>
                    ) interviewCount,
                    (SELECT count(id) FROM crm_interview_record where behavior_time &gt;= #{beginTime} and behavior_time &lt;= #{endTime} and `status` = 5 and create_by = cu.id) offerCount,
                </when>
                <when test="queryType == 1 and searchType == 'interviewTime'">
                    (SELECT count(id) FROM crm_interview_record where behavior_time &gt;= #{beginTime} and behavior_time &lt;= #{endTime} and create_by = cu.id) inviteCount,
                    (SELECT count(id) FROM crm_interview_record where behavior_time &gt;= #{beginTime} and behavior_time &lt;= #{endTime} and create_by = cu.id
                        <!-- 面试结果 0/不限  1/待面试  2/未参加  3/未通过  4/已通过 -->
                        <if test="interviewResult == 3">
                            and status = 3
                        </if>
                        <if test="interviewResult == 4">
                            and status = 5
                        </if>
                    ) interviewCount,
                    (SELECT count(id) FROM crm_interview_record where behavior_time &gt;= #{beginTime} and behavior_time &lt;= #{endTime} and `status` = 5 and create_by = cu.id) offerCount,
                </when>
                <when test="queryType == 2 and searchType == 'inviteTime'">
                    (SELECT count(id) FROM crm_interview_record where create_time &gt;= #{beginTime} and create_time &lt;= #{endTime} and create_by = cu.id) inviteCount,
                    (SELECT count(id) FROM crm_interview_record where behavior_time &gt;= #{beginTime} and behavior_time &lt;= #{endTime} and `status` in (3, 5) and create_by = cu.id) interviewCount,
                    (SELECT count(id) FROM crm_interview_record where behavior_time &gt;= #{beginTime} and behavior_time &lt;= #{endTime} and `status` = 5 and create_by = cu.id) offerCount,
                </when>
                <when test="queryType == 2 and searchType == 'interviewTime'">
                    (SELECT count(id) FROM crm_interview_record where behavior_time &gt;= #{beginTime} and behavior_time &lt;= #{endTime} and create_by = cu.id) inviteCount,
                    (SELECT count(id) FROM crm_interview_record where behavior_time &gt;= #{beginTime} and behavior_time &lt;= #{endTime} and `status` in (3, 5) and create_by = cu.id) interviewCount,
                    (SELECT count(id) FROM crm_interview_record where behavior_time &gt;= #{beginTime} and behavior_time &lt;= #{endTime} and `status` = 5 and create_by = cu.id) offerCount,
                </when>
                <otherwise>
                    (SELECT count(id) FROM crm_interview_record where create_time &gt;= #{beginTime} and create_time &lt;= #{endTime} and create_by = cu.id) inviteCount,
                    (SELECT count(id) FROM crm_interview_record where behavior_time &gt;= #{beginTime} and behavior_time &lt;= #{endTime} and `status` in (3, 5) and create_by = cu.id) interviewCount,
                    (SELECT count(id) FROM crm_interview_record where behavior_time &gt;= #{beginTime} and behavior_time &lt;= #{endTime} and `status` = 5 and create_by = cu.id) offerCount,
                </otherwise>
            </choose>
            (SELECT count(id) FROM crm_entry_record where behavior_time &gt;= #{beginTime} and behavior_time &lt;= #{endTime} and `status` = 1 and create_by = cu.id) entryCount,
            (
                SELECT count(a.id) FROM (
                SELECT cfu.id, max(csd.actual_settle_time) settle_time, cfu.create_by
                FROM crm_follow_up cfu
                left JOIN crm_settle_details csd on csd.follow_id = cfu.id
                where cfu.current_status = 10 and csd.del_flag = 0
                GROUP BY cfu.id
                ) a where a.settle_time &gt;= #{beginTime} and a.settle_time &lt;= #{endTime} and a.create_by = cu.id
            )  settleCount,
            (SELECT IFNULL( sum(call_count), 0)  from crm_user_everyday_workload where user_id = cu.id and statistics_date &gt;= #{beginTime} and statistics_date &lt;= #{endTime}) callCount,
            (SELECT IFNULL(sum(wechat_count), 0)  from crm_user_everyday_workload where user_id = cu.id and statistics_date &gt;= #{beginTime} and statistics_date &lt;= #{endTime}) wechatCount,
            (SELECT IFNULL(count(*), 0) from crm_contact where create_by = cu.id and create_time &gt;= #{beginTime} and create_time &lt;= #{endTime}) resumeCount,
            (SELECT IFNULL( sum(contract_count),0) from crm_user_everyday_workload where user_id = cu.id and statistics_date &gt;= #{beginTime} and statistics_date &lt;= #{endTime}) contractCount,
            (SELECT IFNULL( SUM(CASE WHEN money = 0 THEN integral ELSE money END), 0) from crm_commission_gave_log where commission_user = cu.id and del_flag = 0 and commission_time >= #{beginTime} and commission_time &lt;= #{endTime}) integral
        FROM crm_user cu
        where
        <!-- 选择团队 -->
        <if test="createBySet != null and createBySet.size > 0">
            cu.id in
            <foreach collection="createBySet" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="createBySet == null or createBySet.size == 0">
            cu.id = #{userId}
        </if>
        ) uf

    </select>

    <select id="getForInterviewCount" resultType="int">
        SELECT COUNT(cir.id)
        FROM crm_interview_record cir
        LEFT JOIN crm_follow_up cfu ON cfu.id = cir.`follow_id`
        LEFT JOIN crm_project cp ON cfu.`project_id` = cp.id
        WHERE cp.suspend = 0 AND cp.stop = 0 AND cp.master =#{uid}
        AND cir.status IN (0,2,4)
        <if test="null != beginTime and null != endTime">
            and cir.behavior_time &gt;= #{beginTime} and cir.behavior_time &lt;= #{endTime}
        </if>
    </select>

    <select id="getAwaitEntyCount" resultType="int">
        SELECT COUNT(cer.id)
        FROM crm_entry_record cer
        LEFT JOIN crm_follow_up cfu ON cfu.id = cer.follow_id
        LEFT JOIN crm_project cp ON cp.id = cfu.`project_id`
        WHERE  NOT cer.estimate_time IS NULL and cp.suspend = 0 AND cp.stop = 0 AND cp.master =#{uid}
        AND cfu.del_flag = 0 AND cer.status in (0,2)
        AND cer.enterprise_id = #{eid}
        <if test="null != beginTime and null != endTime">
            and cer.estimate_time &gt;= #{beginTime} and cer.estimate_time &lt;= #{endTime}
        </if>
    </select>

    <select id="getForTheCount" resultType="int">
        SELECT COUNT(1) AS total FROM (
        SELECT csd.id
        FROM crm_settle_details csd
        LEFT JOIN crm_follow_up cfu ON cfu.id = csd.follow_id
        LEFT JOIN crm_project cp ON cp.id = cfu.`project_id`
        WHERE cp.suspend = 0 AND cp.stop = 0 AND cp.master =#{uid}
        AND csd.tmp_status = 0
        AND csd.status = 0
        AND csd.del_flag = 0
        <if test="null != beginTime and null != endTime">
            and csd.actual_settle_time &gt;= #{beginTime} and csd.actual_settle_time &lt;= #{endTime}
        </if>
        GROUP BY csd.follow_id) temp
    </select>

    <select id="getNodeCount" resultType="int">
        SELECT COUNT(csd.id)
        FROM crm_settle_details csd
        LEFT JOIN crm_follow_up cfu ON cfu.id = csd.follow_id
        LEFT JOIN crm_project cp ON cp.id = cfu.`project_id`
        WHERE cp.suspend = 0 AND cp.stop = 0 AND cp.master =#{uid}
        AND csd.tmp_status = 0
        AND csd.status = 0
        AND csd.del_flag = 0
        <if test="null != beginTime and null != endTime">
            and csd.actual_settle_time &gt;= #{beginTime} and csd.actual_settle_time &lt;= #{endTime}
        </if>
    </select>

    <select id="getForInterview" resultType="com.ssb.approve.model.WorkWaiting" >
        SELECT cir.id,cir.`follow_id` followId,cir.enterprise_id enterpriseId,cir.behavior_time TIME, cp.title, cc.name contactName ,
        ccu.name enterpriseName, "待面试" matter
        FROM crm_interview_record cir
        LEFT JOIN crm_follow_up cfu ON cfu.id = cir.`follow_id`
        LEFT JOIN crm_project cp ON cfu.`project_id` = cp.id
        LEFT JOIN crm_contact cc ON cc.id = cfu.`contact_id`
        LEFT JOIN crm_customer ccu ON ccu.id = cp.customer_id
        WHERE cp.suspend = 0 AND cp.stop = 0 AND cp.master =#{uid}
        AND cfu.del_flag = 0
        AND cir.status IN (0,2,4)
        <if test="null != beginTime and null != endTime">
            and cir.behavior_time &gt;= #{beginTime} and cir.behavior_time &lt;= #{endTime}
        </if>
        ORDER BY cir.behavior_time DESC
        LIMIT #{offset},#{pageSize}
    </select>

    <select id="getAwaitEnty" resultType="com.ssb.approve.model.WorkWaiting" >
        SELECT cer.id,cer.`follow_id` followId,cer.enterprise_id enterpriseId,cer.estimate_time TIME, cp.title, cc.name contactName,
        ccu.name enterpriseName, "待入职" matter
        FROM crm_entry_record cer
        LEFT JOIN crm_follow_up cfu ON cfu.id = cer.follow_id
        LEFT JOIN crm_contact cc ON cfu.contact_id = cc.id
        LEFT JOIN crm_project cp ON cp.id = cfu.`project_id`
        LEFT JOIN crm_customer ccu ON ccu.id = cp.customer_id
        WHERE NOT cer.estimate_time IS NULL and cp.suspend = 0 AND cp.stop = 0 AND cp.master =#{uid}
        AND cfu.del_flag = 0 AND cer.status in (0,2)
        AND cer.enterprise_id = #{eid}
        <if test="null != beginTime and null != endTime">
            and cer.estimate_time &gt;= #{beginTime} and cer.estimate_time &lt;= #{endTime}
        </if>
        ORDER BY cer.estimate_time DESC
        LIMIT #{offset},#{pageSize}
    </select>

    <select id="getNode" resultType="com.ssb.approve.model.WorkWaiting" >
        SELECT csd.id,csd.`follow_id` followId,csd.enterprise_id enterpriseId,csd.actual_settle_time TIME, cp.title, cc.name contactName,
        ccu.name enterpriseName, csd.node matter
        FROM crm_settle_details csd
        LEFT JOIN crm_follow_up cfu ON cfu.id = csd.follow_id
        LEFT JOIN crm_contact cc ON cfu.contact_id = cc.id
        LEFT JOIN crm_project cp ON cp.id = cfu.`project_id`
        LEFT JOIN crm_customer ccu ON ccu.id = cp.customer_id
        WHERE cp.suspend = 0 AND cp.stop = 0 AND cp.master = #{uid}
        AND csd.tmp_status = 0 and csd.enterprise_id = #{eid}
        AND csd.status = 0
        AND cfu.del_flag = 0
        AND csd.del_flag = 0
        <if test="null != beginTime and null != endTime">
            and csd.actual_settle_time &gt;= #{beginTime} and csd.actual_settle_time &lt;= #{endTime}
        </if>
        ORDER BY csd.actual_settle_time DESC
        LIMIT #{offset},#{pageSize}
    </select>

    <select id="getCheckSlip" resultType="com.ssb.approve.entity.CheckSlip" >
        SELECT ccu.name enterpriseName, ccu.id eid, cp.id pid, cp.title projectName,
               csd.actual_settle_time settleTime, ccu.account_balance accountBalance, ccu.payment,
               (case when cp.source = 3 then SUM(csl.payment) / (select rate from crm_project_share where project_id = cp.share_project_id and del_flag = 0 limit 1) * 100 else SUM(csl.payment) end) as salary
        FROM crm_settle_details csd
        LEFT JOIN crm_follow_up cfu ON cfu.id = csd.follow_id
        LEFT JOIN crm_project cp ON cp.id = cfu.`project_id`
        LEFT JOIN crm_customer ccu ON ccu.id = cp.customer_id
        LEFT JOIN crm_settle_log csl ON csl.settle_id = csd.id AND csl.del_flag = 0
        left join crm_contract cco on cco.id = cp.contract_id
        WHERE
            ccu.enterprise_id = #{enterpriseId}
        AND cfu.del_flag = 0
        AND csd.del_flag = 0
        AND csd.status = 1
        <if test="null != range and range == 1">
            AND cp.master in
            <foreach collection="userIds" item="userId" separator="," open="(" close=")">
                #{userId}
            </foreach>
        </if>
        <if test="null != search and search != ''">
            and ccu.name LIKE CONCAT('%', #{search},'%')
        </if>
        <if test="null != beginTime and null != endTime">
            and csd.actual_settle_time &gt;= #{beginTime} and DATE_FORMAT(csd.actual_settle_time,'%Y-%m-%d') &lt;= #{endTime}
        </if>
        <if test="projectStatus != null">
            <choose>
                <when test="projectStatus == 0">
                    and cp.suspend != 1 and cp.stop != 1
                </when>
                <when test="projectStatus == 1">
                    and cp.suspend = 1 and cp.stop != 1
                </when>
                <when test="projectStatus == 2">
                    and cp.stop = 1
                </when>
            </choose>
        </if>
        <if test="enterpriseStatus != null">
            and ccu.status = #{enterpriseStatus}
        </if>
        <if test="paymentType != null">
            and cco.payment_type = #{paymentType}
        </if>
        <if test="arrearsStatus != null">
            <if test="arrearsStatus == 1">
                and ccu.account_balance &lt; 0
            </if>
            <if test="arrearsStatus == 2">
                and ccu.account_balance &gt;= 0
            </if>
        </if>
        GROUP BY cp.id
        ORDER BY csd.actual_settle_time DESC
        LIMIT #{pageBegin},#{pageSize}
    </select>


    <select id="getCheckSlipCount" resultType="int" >
        SELECT COUNT(1) AS total FROM (
        SELECT ccu.id eid
        FROM crm_settle_details csd
        LEFT JOIN crm_follow_up cfu ON cfu.id = csd.follow_id
        LEFT JOIN crm_project cp ON cp.id = cfu.`project_id`
        LEFT JOIN crm_customer ccu ON ccu.id = cp.customer_id
        LEFT JOIN crm_settle_log csl ON csl.settle_id = csd.id AND csl.del_flag = 0
        left join crm_contract cco on cco.id = cp.contract_id
        WHERE
            ccu.enterprise_id = #{enterpriseId}
        AND cfu.del_flag = 0
        AND csd.del_flag = 0
        AND csd.status = 1
        <if test="null != range and range == 1">
            AND cp.master in
            <foreach collection="userIds" item="userId" separator="," open="(" close=")">
                #{userId}
            </foreach>
        </if>
        <if test="null != search and search != ''">
            and ccu.name LIKE CONCAT('%', #{search},'%')
        </if>
        <if test="null != beginTime and null != endTime">
                and csd.actual_settle_time &gt;= #{beginTime} and DATE_FORMAT(csd.actual_settle_time,'%Y-%m-%d') &lt;= #{endTime}
        </if>
        <if test="projectStatus != null">
            <choose>
                <when test="projectStatus == 0">
                    and cp.suspend != 1 and cp.stop != 1
                </when>
                <when test="projectStatus == 1">
                    and cp.suspend = 1 and cp.stop != 1
                </when>
                <when test="projectStatus == 2">
                    and cp.stop = 1
                </when>
            </choose>
        </if>
        <if test="enterpriseStatus != null">
            and ccu.status = #{enterpriseStatus}
        </if>
        <if test="paymentType != null">
            and cco.payment_type = #{paymentType}
        </if>
        <if test="arrearsStatus != null">
            <if test="arrearsStatus == 1">
                and ccu.account_balance &lt; 0
            </if>
            <if test="arrearsStatus == 2">
                and ccu.account_balance &gt;= 0
            </if>
        </if>
        GROUP BY cp.id)temp
    </select>

    <select id="getSumCheckSlip" resultType="java.math.BigDecimal">
        SELECT ROUND(IFNULL(sum(temp.salary), 0), 2) from (
        SELECT
               (case when cp.source = 3 then SUM(csl.payment) / (select rate from crm_project_share where project_id = cp.share_project_id and del_flag = 0 limit 1) * 100 else SUM(csl.payment) end) as salary
        FROM crm_settle_details csd
        LEFT JOIN crm_follow_up cfu ON cfu.id = csd.follow_id
        LEFT JOIN crm_project cp ON cp.id = cfu.`project_id`
        LEFT JOIN crm_customer ccu ON ccu.id = cp.customer_id
        LEFT JOIN crm_settle_log csl ON csl.settle_id = csd.id AND csl.del_flag = 0
        left join crm_contract cco on cco.id = cp.contract_id
        WHERE
            ccu.enterprise_id = #{enterpriseId}
        AND cfu.del_flag = 0
        AND csd.del_flag = 0
        AND csd.status = 1
        <if test="null != range and range == 1">
            AND cp.master in
            <foreach collection="userIds" item="userId" separator="," open="(" close=")">
                #{userId}
            </foreach>
        </if>
        <if test="null != search and search != ''">
            and ccu.name LIKE CONCAT('%', #{search},'%')
        </if>
        <if test="null != beginTime and null != endTime">
            and csd.actual_settle_time &gt;= #{beginTime} and DATE_FORMAT(csd.actual_settle_time,'%Y-%m-%d') &lt;= #{endTime}
        </if>
        <if test="projectStatus != null">
            <choose>
                <when test="projectStatus == 0">
                    and cp.suspend != 1 and cp.stop != 1
                </when>
                <when test="projectStatus == 1">
                    and cp.suspend = 1 and cp.stop != 1
                </when>
                <when test="projectStatus == 2">
                    and cp.stop = 1
                </when>
            </choose>
        </if>
        <if test="enterpriseStatus != null">
            and ccu.status = #{enterpriseStatus}
        </if>
        <if test="paymentType != null">
            and cco.payment_type = #{paymentType}
        </if>
        <if test="arrearsStatus != null">
            <if test="arrearsStatus == 1">
                and ccu.account_balance &lt; 0
            </if>
            <if test="arrearsStatus == 2">
                and ccu.account_balance &gt;= 0
            </if>
        </if>
        GROUP BY cp.id
        )temp
    </select>

    <select id="getCheckSlipDetail" resultType="com.ssb.approve.entity.CheckSlip" >
            SELECT ccu.name enterpriseName, ccu.id eid, cp.id pid, cp.title projectName, cc.name contactName, cc.phone contactPhone,csd.entry_time entryTime,
            csd.actual_settle_time settleTime, csd.node node,
            (case when cp.source = 3 then csl.payment / (select rate from crm_project_share where project_id = cp.share_project_id and del_flag = 0 limit 1) * 100 else csl.payment end) as salary
            FROM crm_settle_details csd
            LEFT JOIN crm_follow_up cfu ON cfu.id = csd.follow_id
            LEFT JOIN crm_contact cc ON cfu.contact_id = cc.id
            LEFT JOIN crm_project cp ON cp.id = cfu.`project_id`
            LEFT JOIN crm_customer ccu ON ccu.id = cp.customer_id
            LEFT JOIN crm_settle_log csl ON csl.settle_id = csd.id AND csl.del_flag = 0
            left join crm_contract cco on cco.id = cp.contract_id
            WHERE
                ccu.id =#{eid}
            AND cfu.del_flag = 0
            AND csd.del_flag = 0
            AND csd.status = 1
            <if test="null != range and range == 1">
                AND cp.master in
                <foreach collection="userIds" item="userId" separator="," open="(" close=")">
                    #{userId}
                </foreach>
            </if>
            <if test="null != search and search != ''">
                and ccu.name LIKE CONCAT('%', #{search},'%')
            </if>
            <if test="null != beginTime and null != endTime">
                and csd.actual_settle_time &gt;= #{beginTime} and DATE_FORMAT(csd.actual_settle_time,'%Y-%m-%d') &lt;= #{endTime}
            </if>
            <if test="projectStatus != null">
                <choose>
                    <when test="projectStatus == 0">
                        and cp.suspend != 1 and cp.stop != 1
                    </when>
                    <when test="projectStatus == 1">
                        and cp.suspend = 1 and cp.stop != 1
                    </when>
                    <when test="projectStatus == 2">
                        and cp.stop = 1
                    </when>
                </choose>
            </if>
            <if test="enterpriseStatus != null">
                and cc.status = #{enterpriseStatus}
            </if>
            <if test="paymentType != null">
                and cco.payment_type = #{paymentType}
            </if>
            <if test="arrearsStatus != null">
                <if test="arrearsStatus == 1">
                    and ccu.account_balance &lt; 0
                </if>
                <if test="arrearsStatus == 2">
                    and ccu.account_balance &gt;= 0
                </if>
            </if>
            ORDER BY csd.actual_settle_time DESC
            <if test="null != pageBegin">
                LIMIT #{pageBegin},#{pageSize}
            </if>
    </select>

    <select id="getCheckSlipDetailCount" resultType="int" >
        SELECT COUNT(cp.id )pid
        FROM crm_settle_details csd
        LEFT JOIN crm_follow_up cfu ON cfu.id = csd.follow_id
        LEFT JOIN crm_contact cc ON cfu.contact_id = cc.id
        LEFT JOIN crm_project cp ON cp.id = cfu.`project_id`
        LEFT JOIN crm_customer ccu ON ccu.id = cp.customer_id
        WHERE
            ccu.id =#{eid}
        AND cfu.del_flag = 0
        AND csd.del_flag = 0
        AND csd.status = 1
        <if test="null != range and range == 1">
            AND cp.master in
            <foreach collection="userIds" item="userId" separator="," open="(" close=")">
                #{userId}
            </foreach>
        </if>
        <if test="null != search and search != ''">
            and ccu.name LIKE CONCAT('%', #{search},'%')
        </if>
        <if test="null != beginTime and null != endTime">
            and csd.actual_settle_time &gt;= #{beginTime} and DATE_FORMAT(csd.actual_settle_time,'%Y-%m-%d') &lt;= #{endTime}
        </if>
        <if test="projectStatus != null">
            <choose>
                <when test="projectStatus == 0">
                    and cp.suspend != 1 and cp.stop != 1
                </when>
                <when test="projectStatus == 1">
                    and cp.suspend = 1 and cp.stop != 1
                </when>
                <when test="projectStatus == 2">
                    and cp.stop = 1
                </when>
            </choose>
        </if>
        <if test="enterpriseStatus != null">
            and cc.status = #{enterpriseStatus}
        </if>
    </select>

</mapper>