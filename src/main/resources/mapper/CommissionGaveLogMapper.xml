<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ssb.approve.dao.CommissionGaveLogDAO">

    <insert id="save">
        insert into crm_commission_gave_log (enterprise_id, project_id, follow_id, settle_id, classify_id, industry_first_id, industry_second_id, position_first_id, position_second_id, follow_commission_id, commission_level_id, commission_user, `type`, commission_time, money, integral, create_by, create_time, remark)
        VALUES (#{enterpriseId}, #{projectId}, #{followId}, #{settleId}, #{classifyId}, #{industryFirstId}, #{industrySecondId}, #{positionFirstId}, #{positionSecondId}, #{followCommissionId}, #{commissionLevelId}, #{commissionUser}, #{type}, #{commissionTime}, #{money}, #{integral}, #{createBy}, now(), #{remark})

    </insert>

    <!-- 批量保存信息 -->
    <insert id="saveBatch">
        insert into crm_commission_gave_log (enterprise_id, project_id, follow_id, settle_id, classify_id, industry_first_id, industry_second_id, position_first_id, position_second_id, follow_commission_id, commission_level_id, commission_user, `type`, commission_time, money, integral, create_by, create_time, remark)
        VALUES
        <foreach collection ="commissionGaveLogs" item="cgl" separator =",">
            (#{cgl.enterpriseId}, #{cgl.projectId}, #{cgl.followId}, #{cgl.settleId}, #{cgl.classifyId}, #{cgl.industryFirstId}, #{cgl.industrySecondId}, #{cgl.positionFirstId}, #{cgl.positionSecondId}, #{cgl.followCommissionId}, #{cgl.commissionLevelId}, #{cgl.commissionUser}, #{cgl.type}, #{cgl.commissionTime}, #{cgl.money}, #{cgl.integral}, #{cgl.createBy}, now(), #{cgl.remark})
        </foreach >
    </insert>

    <!-- 月金额 -->
    <select id="getMonthAmount" resultType="decimal">
        SELECT sum(money)
        FROM crm_commission_gave_log
        WHERE project_id = #{projectId} and `type` = #{type} and commission_user = #{commissionUser}
        and DATE_FORMAT(commission_time, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-%m')
        and del_flag = 0
    </select>

    <update id="deleteCommissionGaveLogBySettleId">
        update crm_commission_gave_log set del_flag = 1 where settle_id = #{settleId}
    </update>

    <select id="getPerformanceByUserIdList" resultType="java.math.BigDecimal">
        select IFNULL(sum(integral), 0)
        from crm_commission_gave_log
        where
              <choose>
                  <when test="type == 1">
                    commission_user = #{id}
                  </when>
                  <otherwise>
                    commission_user in
                      <foreach collection="userIdList" item="userId" separator="," open="(" close=")">
                        #{userId}
                      </foreach>
                  </otherwise>
              </choose>
         and del_flag = 0
         and create_time > '2024-05-01 00:00:00'
    </select>

    <select id="getShareProjectPerformance" resultType="java.math.BigDecimal">
        select IFNULL(sum(ccgl.integral), 0)
        from crm_commission_gave_log ccgl
        left join crm_project cp on cp.id = ccgl.project_id
        where cp.share_project_id in
                  (
                      SELECT id
                      FROM crm_project
                      where share_status = 1 AND master in
                        <foreach collection="userIdList" item="userId" separator="," open="(" close=")">
                            #{userId}
                        </foreach>
                  )
         and ccgl.del_flag = 0
         and ccgl.create_time > '2024-05-01 00:00:00'
    </select>

    <select id="getTheCharts" resultType="com.ssb.approve.model.vo.TheChartsVO" parameterType="com.ssb.approve.model.dto.TheChartsDTO">
        select
            *
        from
            (
                SELECT
                    cu.id,
                    cu.head,
                    cu.NAME userName,
                    cu.dept_id deptId,
                    cd.name deptName,
                    case when cu.type = 0 then IFNULL(SUM(ccgl.integral), 0) when cu.type = 1 then IFNULL(SUM(ccgl.money), 0) end money,
                    (select create_time from `crm_commission_gave_log` where `commission_user` = cu.id and commission_time > '2024-05-01 00:00:00' ORDER BY `commission_time` desc limit 1) lastTime
                FROM
                    crm_commission_gave_log ccgl
                        INNER JOIN crm_user cu ON cu.id = ccgl.commission_user
                        INNER JOIN crm_dept cd on cd.id = cu.`dept_id`
                WHERE
                    ccgl.enterprise_id = #{enterpriseId}
                  and cu.del_flag = 0
                  and ccgl.del_flag = 0
                  <if test="userName != null">
                      and cu.name like CONCAT('%', #{userName},'%')
                  </if>
                  and DATE_FORMAT(ccgl.commission_time, '%Y-%m') = DATE_FORMAT(CURDATE(), '%Y-%m')
                  and ccgl.commission_time > '2024-05-01 00:00:00'
                GROUP BY
                    ccgl.commission_user
            ) res
        where
            res.money > 0
        ORDER BY
            res.money DESC
            <if test="type == 1">
                limit #{homePageSize}
            </if>
            <if test="type == 2">
                limit #{pageBegin}, #{pageSize}
            </if>
    </select>

    <select id="getTheChartsCount" parameterType="com.ssb.approve.model.dto.TheChartsDTO" resultType="java.lang.Integer">
        select
            count(*)
        from
            (
                SELECT
                    cu.id,
                    cu.head,
                    cu.NAME userName,
                    cu.dept_id deptId,
                    cd.name deptName,
                    IFNULL(SUM(ccgl.integral), 0) money
                FROM
                    crm_commission_gave_log ccgl
                        INNER JOIN crm_user cu ON cu.id = ccgl.commission_user
                        INNER JOIN crm_dept cd on cd.id = cu.`dept_id`
                WHERE
                    ccgl.enterprise_id = #{enterpriseId}
                  and cu.del_flag = 0
                  and ccgl.del_flag = 0
                  <if test="userName != null">
                      and cu.name like CONCAT('%', #{userName},'%')
                  </if>
                  and DATE_FORMAT(ccgl.commission_time, '%Y-%m') = DATE_FORMAT(CURDATE(), '%Y-%m')
                  and ccgl.commission_time > '2024-05-01 00:00:00'
                GROUP BY
                    ccgl.commission_user
            ) res
        where
            res.money > 0
    </select>

    <select id="getDataStatisticsPerformance" resultType="java.math.BigDecimal">
        SELECT
            IFNULL(
                SUM(CASE
                    WHEN ccgl.money = 0 THEN ccgl.integral
                    ELSE ccgl.money
                END)
                    , 0 ) AS total_amount
        FROM
            crm_commission_gave_log ccgl
        inner join crm_user cu on ccgl.commission_user = cu.id
        WHERE
            ccgl.commission_user IN (
                <foreach collection="dto.userIdList" item="userId" separator=",">
                    #{userId}
                </foreach>
                )
            AND ccgl.del_flag = 0
            <if test="dto.timeType != 0">
                <choose>
                    <when test="sourceType == 1">
                        AND ccgl.commission_time between #{dto.timeStart} and #{dto.timeEnd}
                    </when>
                    <when test="sourceType == 2">
                        AND ccgl.commission_time between #{dto.compareTimeStart} and #{dto.compareTimeEnd}
                    </when>
                </choose>
            </if>
    </select>

</mapper>