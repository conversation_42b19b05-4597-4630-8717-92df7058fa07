<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ssb.approve.dao.QuitRecordDAO">

    <resultMap id="quitRecordResultMap" type="com.ssb.approve.entity.QuitRecord">
        <result column="enterprise_id" property="enterpriseId"></result>
        <result column="from_user_id" property="fromUserId"></result>
        <result column="to_user_id" property="toUserId"></result>
        <result column="resume_change" property="resumeChange"></result>
        <result column="create_by" property="createBy"></result>
        <result column="create_time" property="createTime"></result>
        <result column="update_time" property="updateTime"></result>
    </resultMap>

    <!-- 获取离职记录 -->
    <select id="getQuitRecordByFromUserId" resultMap="quitRecordResultMap">
        select id, enterprise_id, from_user_id, to_user_id, resume_change, status, create_by, create_time, update_time, remark
        from crm_quit_record
        where from_user_id = #{fromUserId} and del_flag = 0
        ORDER BY create_time DESC
        limit 1
    </select>

    <!-- 保存离职跟进记录 -->
    <insert id="batchSaveFollowUpQuitRecord">
        INSERT INTO crm_follow_up_quit_record (quit_id, follow_id, enterprise_id, contact_id, project_id, `status`, current_status, finish, create_by, create_time, update_by, update_time, del_flag, remark, operation_time, operation_by)
        VALUES
        <foreach collection="followUpQuitRecords" item="item" index="index" separator=",">
            (
                #{item.quitId},
                #{item.followId},
                #{item.enterpriseId},
                #{item.contactId},
                #{item.projectId},
                #{item.status},
                #{item.currentStatus},
                #{item.finish},
                #{item.createBy},
                #{item.createTime},
                #{item.updateBy},
                #{item.updateTime},
                #{item.delFlag},
                #{item.remark},
                #{item.operationTime},
                #{item.operationBy}
            )
        </foreach>
    </insert>

    <!-- 保存离职  面试记录 -->
    <insert id="batchSaveInterviewQuitRecord">
        INSERT INTO crm_interview_quit_record (quit_id, enterprise_id, follow_id, node, `status`, auth, behavior_time, estimate_time, remark, failure, create_by, create_time, update_by, update_time, operation_time, operation_by)
        VALUES
        <foreach collection="interviewQuitRecords" item="item" index="index" separator=",">
            (
                #{item.quitId},
                #{item.enterpriseId},
                #{item.followId},
                #{item.node},
                #{item.status},
                #{item.auth},
                #{item.behaviorTime},
                #{item.estimateTime},
                #{item.remark},
                #{item.failure},
                #{item.createBy},
                #{item.createTime},
                #{item.updateBy},
                #{item.updateTime},
                #{item.operationTime},
                #{item.operationBy}
            )
        </foreach>
    </insert>

    <!-- 保存离职 入职记录 -->
    <insert id="batchSaveEntryQuitRecord">
        INSERT INTO crm_entry_quit_record (quit_id, enterprise_id, follow_id, `status`, auth, estimate_time, behavior_time, remark, refuse, create_by, create_time, update_by, update_time, operation_time, operation_by)
        VALUES
        <foreach collection="entryQuitRecords" item="item" index="index" separator=",">
            (
                #{item.quitId},
                #{item.enterpriseId},
                #{item.followId},
                #{item.status},
                #{item.auth},
                #{item.estimateTime},
                #{item.behaviorTime},
                #{item.remark},
                #{item.refuse},
                #{item.createBy},
                #{item.createTime},
                #{item.updateBy},
                #{item.updateTime},
                #{item.operationTime},
                #{item.operationBy}
            )
        </foreach>
    </insert>

    <!-- 保存离职 结算明细 -->
    <insert id="batchSaveSettleQuitDetail">
        INSERT INTO crm_settle_quit_details(quit_id, enterprise_id, follow_id, node, entry_time, salary, `date`, `time`, settle_time, `status`, create_by, create_time, update_by, update_time, del_flag, operation_time, operation_by)
        VALUES
        <foreach collection="settleQuitDetails" item="item" index="index" separator=",">
            (
                #{item.quitId},
                #{item.enterpriseId},
                #{item.followId},
                #{item.node},
                #{item.entryTime},
                #{item.salary},
                #{item.date},
                #{item.time},
                #{item.settleTime},
                #{item.status},
                #{item.createBy},
                #{item.createTime},
                #{item.updateBy},
                #{item.updateTime},
                #{item.delFlag},
                #{item.operationTime},
                #{item.operationBy}
            )
        </foreach>
    </insert>

    <!-- 保存离职 结算记录 -->
    <insert id="batchSaveSettleQuitLog">
        INSERT INTO crm_settle_quit_log(quit_id, enterprise_id, follow_id, settle_id, behavior_time, performance, payment, create_by, create_time, del_flag, remark, operation_time, operation_by)
        VALUES
        <foreach collection="settleQuitLogs" item="item" index="index" separator=",">
            (
                #{item.quitId},
                #{item.enterpriseId},
                #{item.followId},
                #{item.settleId},
                #{item.behaviorTime},
                #{item.performance},
                #{item.payment},
                #{item.createBy},
                #{item.createTime},
                #{item.delFlag},
                #{item.remark},
                #{item.operationTime},
                #{item.operationBy},
            )
        </foreach>
    </insert>

    <!-- 更新离职状态 完成态 -->
    <update id="updateQuitRecord" parameterType="com.ssb.approve.entity.QuitRecord">
        update crm_quit_record
        set status = #{status}, update_time = #{updateTime}
        WHERE id = #{id}
    </update>
</mapper>