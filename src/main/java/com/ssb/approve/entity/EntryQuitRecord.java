package com.ssb.approve.entity;

import lombok.Data;

import java.sql.Timestamp;

/**
 * @ClassName: EntryQuitRecord
 * @Description: 入职离职记录
 * @Author: YZK
 * @Date: 2020年02月20日09:04:43
 **/
@Data
public class EntryQuitRecord {

    private Integer id; // id

    private Integer quitId; //离职记录id

    private Integer enterpriseId; // 企业id

    private Integer followId; // 跟进id

    private Integer status; // 状态 0 默认 1 已入职 2 更改入职时间  3 拒绝入职

    private Integer auth; // 审核状态 0 默认  1 审核

    private Timestamp estimateTime; // 估计入职时间 （发offer确定时间  不可变）

    private Timestamp behaviorTime; // 行为时间

    private String remark; // 备注

    private String refuse; // 拒绝理由

    private Integer createBy; // 创建人

    private Timestamp createTime; // 创建时间

    private Integer updateBy; // 更新人

    private Timestamp updateTime; // 更新时间

    private Timestamp operationTime; // 操作时间

    private Integer operationBy; // 操作人
}

