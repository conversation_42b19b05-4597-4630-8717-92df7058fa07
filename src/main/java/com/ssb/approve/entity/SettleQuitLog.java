package com.ssb.approve.entity;

import lombok.Data;

import java.math.BigDecimal;
import java.sql.Timestamp;

/**
 * @ClassName: SettleQuitLog
 * @Description: 结算离职记录
 * @Author: YZK
 * @Date: 2020年02月20日09:24:04
 **/
@Data
public class SettleQuitLog {

    private Integer id; // id

    private Integer quitId; // 离职id

    private Integer enterpriseId; //企业id

    private Integer followId; //跟进id

    private Integer settleId; // 结算明细id

    private Timestamp behaviorTime; // 行为时间

    private BigDecimal performance; // 业绩

    private BigDecimal payment; // 扣费

    private Integer createBy; // 创建人

    private Timestamp createTime; // 创建时间

    private Integer delFlag; // 删除标识  0 正常 1 删除

    private String remark; // 备注

    private Timestamp operationTime; // 操作时间

    private Integer operationBy; // 操作人

}
