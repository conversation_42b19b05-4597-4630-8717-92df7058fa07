package com.ssb.approve.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.sql.Timestamp;

/**
 * @ClassName: SettleDetails
 * @Description: 结算明细记录
 * @Author: YZK
 * @Date: 2019年12月27日11:11:35
 **/
@Data
public class SettleDetails {

    private Integer id; //id

    private Integer enterpriseId; //企业id

    private Integer followId; //跟进id

    private Integer node; //结算节点

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp entryTime; //入职时间

    private BigDecimal salary; //结算金额

    private Integer date; //天数

    private String time; //时间 数据库存的是  time 类型  string类型 处理更方便  所以 偷个懒吧

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp settleTime; //预估结算时间

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp actualSettleTime; //实际结算时间

    private Integer status; //状态0 默认 1 结算  2 离职

    private Integer tmpStatus; // 临时状态  状态0 默认 1 结算  2 离职

    private Integer authType; //审核类型 0猎头端审核 1企业端审核

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Timestamp leaveTime; //行为时间   离职时间

    private String leaveCause; //离职原因

    private Integer createBy; //创建人

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp createTime; //创建时间

    private Integer updateBy; //更新人

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp updateTime; //更新时间

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp reviewTime; // 审核时间

    private Integer reviewUserId; // 审核人

    private String reviewUserName;

    private Integer delFlag; // 删除状态 0 正常 1 删除

    // 私有属性 结算明细
    private SettleLog settleLog;

}

