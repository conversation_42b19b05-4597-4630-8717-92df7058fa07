package com.ssb.approve.entity;

import lombok.Data;

import java.math.BigDecimal;
import java.sql.Timestamp;

/**
 * @ClassName: SettleQuitDetails
 * @Description: 结算离职明细
 * @Author: YZK
 * @Date: 2020年02月20日09:24:04
 **/
@Data
public class SettleQuitDetails {

    private Integer id; // id

    private Integer quitId; // 离职记录id

    private Integer enterpriseId; //企业id

    private Integer followId; //跟进id

    private Integer node; // 节点

    private Timestamp entryTime; // 入职时间

    private BigDecimal salary; // 结算金额

    private Integer date; // 天数

    private String time; // 时间

    private Timestamp settleTime; // 预估结算时间

    private Integer status; // 状态0 默认 1 结算  2 离职

    private Integer createBy; // 创建人

    private Timestamp createTime; // 创建人

    private Integer updateBy; // 更新人

    private Timestamp updateTime; //更新时间

    private Integer delFlag; //删除标识 0  正常 1 删除

    private Timestamp operationTime; // 操作时间

    private Integer operationBy; // 操作人

}
