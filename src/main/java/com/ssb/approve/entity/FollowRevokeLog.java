package com.ssb.approve.entity;

import lombok.Data;
import java.sql.Timestamp;

@Data
public class FollowRevokeLog {

    private Integer id;

    //企业id
    private Integer enterpriseId;

    //跟进id
    private Integer followId;

    //id 根据type对应不同的表
    private Integer targetId;

    //撤销类型  0/面试  1/入职  2/结算
    private Integer type;

    //撤销前跟进状态  -1意向 0 约面面试 1 约面失败  2 未参加  3 改约时间  4 未通过 5 下一轮面试  6 发offer
    // 7 已入职  8 更改入职时间  9 拒绝入职  10 结算  11 离职
    private Integer followStatus;

    //撤销前状态根据type对应
    //面试：状态0 默认 1 未参加 2 改约时间  3 未通过  4 下一轮面试  5 发offer
    //入职：状态0 默认 1 已入职 2 更改入职时间  3 拒绝入职
    //结算：状态0 默认 1 结算    2 离职
    private Integer statusBeforeRevocation;

    //撤销后状态根据type对应
    //面试：状态0 默认 1 未参加 2 改约时间  3 未通过  4 下一轮面试  5 发offer
    //入职：状态0 默认 1 已入职 2 更改入职时间  3 拒绝入职
    //结算：状态0 默认 1 结算    2 离职
    private Integer statusAfterRevocation;

    //撤销前节点（结算）
    private Integer node;

    //撤销人
    private Integer revokeBy;

    //撤销时间
    private Timestamp revokeTime;

    //撤销原因
    private String revokeRemark;
}
