package com.ssb.approve.entity;

import lombok.Data;

import java.sql.Timestamp;

/**
 * @ClassName: QuitRecord
 * @Description: 离职记录
 * @Author: YZK
 * @Date: 2020年02月20日08:59:09
 **/
@Data
public class QuitRecord {

    private Integer id; //id

    private Integer enterpriseId; //企业id

    private Integer fromUserId; //离职人

    private Integer toUserId; //交接人

    private String resumeChange; //简历释放记录

    private Integer status; //状态 0 默认 1 完成

    private Integer createBy; //创建人

    private Timestamp createTime; //创建时间

    private Timestamp updateTime; //更新时间

    private Integer delFlag; //删除状态 0 正常 1 删除

    private String remark; //备注

}
