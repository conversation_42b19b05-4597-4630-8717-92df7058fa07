package com.ssb.approve.entity;

import lombok.Data;

import java.math.BigDecimal;
import java.sql.Timestamp;

/**
 * @ClassName: RechargeLog
 * @Description: 充值记录
 * @Author: YZK
 * @Date: 2019年12月28日15:05:16
 **/
@Data
public class RechargeLog {

    private Integer id; //id

    private Integer enterpriseId; //企业id

    private Integer projectId; // 项目id

    private Integer customerId; // 客户id

    private Integer contractId; // 合同id

    private Integer type; // 付款类型 1/预付款 2/后付款

    private BigDecimal salary; //充值金额 （可为负数）

    private Integer createBy; //创建人

    private Timestamp createTime; //创建时间

    private String remark; //备注

    private Integer transactionType; //交易类型 1/充值/付款（根据type区分） 2/结算 3/撤销退款 4/离职扣款 5/其他
}
