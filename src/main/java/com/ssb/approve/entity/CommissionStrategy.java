package com.ssb.approve.entity;

import lombok.Data;

@Data
public class CommissionStrategy {

    private Integer id;
    private Integer enterpriseId;//企业id
    private Integer cityLevel;//城市发展水平  0超一线   1新一线   2二线
    private String title;//名称
    private Integer quota;//衡量指标
    private Integer newProjectTime;//新项目持续时间
    private Integer followUpCompletedCount;//新项目跟进完成数
    private Integer divisor;//项目积分除数
    private Integer type;//根据难度控制提成比例  0否 1是
    private Integer createBy;
    private java.sql.Timestamp createTime;
    private Integer updateBy;
    private java.sql.Timestamp updateTime;
    private Integer delFlag;
}
