package com.ssb.approve.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.sql.Timestamp;

/**
 * @ClassName: EntryRecord
 * @Description: 入职记录
 * @Author: YZK
 * @Date: 2019年12月25日14:24:01
 **/
@Data
public class EntryRecord {

    private Integer id; // id

    private Integer enterpriseId; // 企业id

    private Integer followId; // 跟进id

    private Integer status; // 状态 0 默认 1 已入职 2 更改入职时间  3 拒绝入职

    private Integer auth;// 审核状态 0 默认  1 审核

    private Integer authType; //审核类型 0猎头端审核 1企业端审核

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp estimateTime; // 估计入职时间 （发offer确定时间  不可变）

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp behaviorTime;// 行为时间

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp reviewTime; // 审核时间

    private Integer reviewUserId; // 审核人

    private String reviewUserName;

    private  String remark;// 备注

    private String refuse;// 拒绝理由

    private Integer createBy; //创建人

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp createTime;// 创建时间

    private Integer updateBy;// 更新人

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp updateTime;// 更新时间

}

