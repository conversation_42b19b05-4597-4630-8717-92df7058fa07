package com.ssb.approve.entity;

import lombok.Data;

import java.sql.Timestamp;

/**
 * @ClassName: EntryQuitRecord
 * @Description: 入职离职记录
 * @Author: YZK
 * @Date: 2020年02月20日09:04:43
 **/
@Data
public class FollowUpQuitRecord {

    private Integer id; // id

    private Integer quitId; // 离职id

    private Integer followId; // 跟进id

    private Integer enterpriseId; // 企业id

    private Integer contactId; // 求职用户id

    private Integer projectId; // 项目id

    private Integer status; // 状态 0 待约面 1 已约待面试  2 通过待入职 3 入职待结算

    private Integer currentStatus; // 当前状态  状态 -1意象 0 约面面试 1 约面失败  2 未参加  3 改约时间  4 未通过 5 下一轮面试  6 发offer  7 已入职  8 更改入职时间  9 拒绝入职  10 结算  11 离职

    private Integer finish; // 完成状态 0 进行中 1 已结束  （便于后续查询）

    private Integer createBy; // 创建人

    private Timestamp createTime; // 创建时间

    private Integer updateBy; // 更新人

    private Timestamp updateTime; // 更新时间

    private Integer delFlag; // 删除标识 0 正常 1 删除

    private String remark; // 备注

    private Timestamp operationTime; // 操作时间

    private Integer operationBy; // 操作人
}
