package com.ssb.approve.entity;

import lombok.Data;

import java.sql.Timestamp;

/**
 * @ClassName: FollowLog
 * @Description: 跟进记录
 * @Author: YZK
 * @Date: 2019年12月19日20:19:40
 **/
@Data
public class FollowLog {

    private Integer id;//id

    private Integer enterpriseId;//企业id

    private Integer followId;//跟进id

    private Integer settleLogId;//结算日志id    ( status == 12 || 13   为结算id   这里只是一个记录 )

    private Integer type;//类型 0 待约面 1 已约待面试 2 通过入职  3 入职待结算  4 结算  5 撤销

    private Integer status;//状态 -1意象 0 约面面试 1 约面失败  2 未参加  3 改约时间  4 未通过 5 下一轮面试  6 发offer  7 已入职  8 更改入职时间  9 拒绝入职  10 结算  11 离职   12 临时结算  13 临时离职

    private Timestamp behaviorTime;//行为时间

    private Integer createBy;//创建人

    private Timestamp createTime;//创建时间

    private String remark;//备注

    private String failure;//错误原因

    private Integer delFlag;//删除状态  0 正常 1 删除
}
