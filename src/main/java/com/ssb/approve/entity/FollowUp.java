package com.ssb.approve.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.sql.Timestamp;

/**
 * @ClassName: FollowUp
 * @Description: 项目跟进审核
 * @Author: YZK
 * @Date: 2019年12月19日19:49:39
 **/
@Data
public class FollowUp {

    private Integer id;//id

    private Integer enterpriseId;//企业id

    private Integer contactId;//求职用户id

    private Integer projectId;//项目id

    private Integer status;//状态 0 待约面 1 已约待面试  2 通过待入职 3 入职待结算

    private Integer currentStatus; //当前状态  状态 -1意象 0 约面面试 1 约面失败  2 未参加  3 改约时间  4 未通过 5 下一轮面试  6 发offer  7 已入职  8 更改入职时间  9 拒绝入职  10 结算  11 离职

    private Integer finish;//入职审核 0 未审核  1 审核通过

    private Integer createBy;//创建人

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp createTime;//创建时间

    private Integer updateBy;//更新人

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp updateTime;//更新时间

    private Integer delFlag;//删除状态 0 正常 1 删除

    private String remark;//备注

    // 项目类型 0猎头端录入，1企业端录入，2平台端录入
    private Integer projectSource;
}
