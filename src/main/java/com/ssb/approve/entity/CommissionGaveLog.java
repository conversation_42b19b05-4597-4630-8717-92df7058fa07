package com.ssb.approve.entity;

import lombok.Data;

import java.math.BigDecimal;
import java.sql.Timestamp;

/**
 * 提成发放记录
 *
 * <AUTHOR>
 * @time 2021年1月4日09:21:20
 */
@Data
public class CommissionGaveLog {

    private Integer id; // id

    private Integer enterpriseId; //企业id

    private Integer projectId; // 项目id

    private Integer followId; //跟进id

    private Integer classifyId; //行业id

    private Integer followCommissionId; //跟进提成规则id

    private Integer industryFirstId; //一级行业

    private Integer industrySecondId; // 二级行业

    private Integer positionFirstId;  //一级岗位

    private Integer positionSecondId;  //二级岗位

    private Integer settleId; //结算id

    private Integer commissionLevelId; // 提成级别id

    private Integer commissionUser; // 提成用户id

    private Integer type; // 提成类型   0/招聘提成   1/销售提成   2/领导提成

    private Timestamp commissionTime; // 提成时间 （跟进本该发送时间）

    private BigDecimal money; // 提成金额

    private BigDecimal integral; // 业绩

    private Integer createBy; // 创建人

    private Timestamp createTime; // 创建时间

    private Integer delFlag; // 删除状态 0 正常 1 删除

    private String remark; // 备注

}