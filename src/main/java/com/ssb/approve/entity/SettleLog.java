package com.ssb.approve.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.sql.Timestamp;

/**
 * @ClassName: SettleLog
 * @Description: 结算日志记录
 * @Author: YZK
 * @Date: 2019年12月27日20:30:42
 **/
@Data
public class SettleLog {

    private Integer id; //id

    private Integer enterpriseId; //企业id

    private Integer followId; //跟进id

    private Integer settleId; //结算明细id

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Timestamp behaviorTime; //行为时间

    private BigDecimal performance; //业绩

    private BigDecimal payment; //扣款

    private Integer createBy; //创建人

    private Timestamp createTime; //创建时间

    private Integer delFlag; //删除状态 0 正常 1 删除

    private String remark; // 备注
}
