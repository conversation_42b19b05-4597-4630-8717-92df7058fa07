package com.ssb.approve.entity;


import lombok.Data;

import java.sql.Timestamp;

@Data
public class CrmEnterpriseNoticeRecord {

  private Integer id;
  private Integer enterpriseId;
  private Integer projectId;
  private Integer contactId;
  private Integer source;
  private String name;
  private String content;
  private Integer type;
  private Integer status;
  private Integer read;
  private Integer targetId;
  private Integer targetType;
  private Integer route;
  private Integer noticeType;
  private Integer createBy;
  private Timestamp createTime;
  private Integer updateBy;
  private Timestamp updateTime;
  private Integer delFlag;

  public CrmEnterpriseNoticeRecord(){
  }

  public CrmEnterpriseNoticeRecord(Integer enterpriseId, Integer projectId, Integer contactId, Integer source, String name, String content, Integer type, Integer status, Integer read, Integer targetId, Integer targetType, Integer route, Integer noticeType, Integer createBy, Timestamp createTime, Integer updateBy, Timestamp updateTime) {
    this.enterpriseId = enterpriseId;
    this.projectId = projectId;
    this.contactId = contactId;
    this.source = source;
    this.name = name;
    this.content = content;
    this.type = type;
    this.status = status;
    this.read = read;
    this.targetId = targetId;
    this.targetType = targetType;
    this.route = route;
    this.noticeType = noticeType;
    this.createBy = createBy;
    this.createTime = createTime;
    this.updateBy = updateBy;
    this.updateTime = updateTime;
  }
}
