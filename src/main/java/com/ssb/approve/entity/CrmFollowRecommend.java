package com.ssb.approve.entity;

import lombok.Data;

import java.sql.Timestamp;

@Data
public class CrmFollowRecommend {

  private Integer id;
  private Integer contactId;
  private Integer resumeId;
  /**
   * 审核结果 1/审核中 2/通过 3/未通过
   */
  private Integer status;

  /**
   * 审核状态 0未审核，1已审核
   */
  private Integer auth;
  private Integer enterpriseId;
  private Integer customerEnterpriseId;
  private Integer projectId;
  private String remark;
  private Integer createBy;
  private Timestamp createTime;
  private Integer updateBy;
  private Timestamp updateTime;
  private Integer recommendBy;
  private Timestamp recommendTime;
  private Integer checkedBy;
  private Timestamp checkedTime;

}
