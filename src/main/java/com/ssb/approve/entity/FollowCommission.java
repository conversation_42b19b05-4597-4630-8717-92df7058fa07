package com.ssb.approve.entity;

import lombok.Data;

import java.sql.Timestamp;

/**
 * 跟进提成表
 *
 * <AUTHOR>
 * @time 2021年1月4日09:17:45
 */
@Data
public class FollowCommission {

    private Integer id; //if

    private Integer followId; //跟进id

    private Integer classifyId; // 行业

    private Integer industryFirstId; //一级行业
    private Integer industrySecondId; // 二级行业
    private Integer positionFirstId;  //一级岗位
    private Integer positionSecondId;  //二级岗位

    private Integer strategyId; //提成规则id

    private Integer difficulty; // 级别 难易程度

    private String rateContent; //占比

    private Timestamp createTime; // 创建时间

    private Integer errState; // 异常状态   0 正常

    private Timestamp contractStartTime;//合同开始时间

    private Timestamp contractEndTime;//合同开始时间

    private String settlementContent;//项目结算节点

    private Timestamp firstContractStartTime;//最早项目的合同开始时间

    private Timestamp firstContractEndTime;//最早项目的合同开始时间
}