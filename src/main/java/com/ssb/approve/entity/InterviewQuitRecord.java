package com.ssb.approve.entity;

import lombok.Data;

import java.sql.Timestamp;

/**
 * @ClassName: InterviewQuitRecord
 * @Description: 面试离职记录
 * @Author: YZK
 * @Date: 2020年02月20日09:04:43
 **/
@Data
public class InterviewQuitRecord {

    private Integer id; // id

    private Integer quitId; // 离职记录id

    private Integer enterpriseId; // 企业id

    private Integer followId; //跟进id

    private Integer node; // 轮次 第几轮

    private Integer status; // 状态0 默认  1 未参加 2 改约时间  3 未通过  4 下一轮面试  5 发offer

    private Integer auth; // 审核状态 0 默认 1 审核

    private Timestamp behaviorTime; // 行为时间（面试时间）

    private Timestamp estimateTime; // 发offer 预估时间

    private String remark; // 备注

    private String failure; // 失败原因

    private Integer createBy; // 创建人

    private Timestamp createTime; //创建时间

    private Integer updateBy; //更新人

    private Timestamp updateTime; //更新时间

    private Timestamp operationTime; //操作时间

    private Integer operationBy; //操作人

}
