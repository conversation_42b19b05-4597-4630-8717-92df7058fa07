package com.ssb.approve.entity;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class CrmFund {

    /**
     * id
     */
    private Integer id;
    /**
     * 企业id
     */
    private Integer enterpriseId;
    /**
     * 项目id
     */
    private Integer projectId;
    /**
     * 结算id
     */
    private Integer settleId;
    /**
     * 金额（充值为正，提现为负）
     */
    private BigDecimal salary;
    /**
     * 余额
     */
    private BigDecimal balance;
    /**
     * 类型  1/提现  2/项目交付  3/审核失败退款
     */
    private Integer type;
    /**
     * 备注
     */
    private String remark;
    /**
     * 交易状态 1/审核中  2/已完成  3/审核拒绝
     */
    private Integer transactionStatus;
    /**
     * 创建人
     */
    private Integer createBy;
    /**
     * 创建时间
     */
    private java.sql.Timestamp createTime;
    /**
     * 更新人（操作人，-1时表示上啥班云平台）
     */
    private Integer updateBy;
    /**
     * 更新时间（交易时间）
     */
    private java.sql.Timestamp updateTime;


}
