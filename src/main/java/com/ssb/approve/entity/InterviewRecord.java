package com.ssb.approve.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.sql.Timestamp;

/**
 * @ClassName: InterviewRecord
 * @Description: 面试记录
 * @Author: YZK
 * @Date: 2019年12月23日14:50:04
 **/
@Data
public class InterviewRecord {

    private Integer id; //id

    private Integer enterpriseId; //企业id

    private Integer followId; //跟进id

    private Integer node; //轮次 （第几轮）

    private Integer status; //状态0 默认  1 未参加 2 改约时间  3 未通过  4 下一轮面试  5 发offer  （注意 下一轮的时候  更新 node + 1 当前状态 清空 更新对应数据）  6已中断

    private Integer auth; //审核状态 0 默认 1 审核

    private Integer authType; //审核类型 0猎头端审核 1企业端审核

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp behaviorTime; //行为时间

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp estimateTime; // 发offer 预估时间

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp reviewTime; // 审核时间

    private Integer reviewUserId; // 审核人

    private String reviewUserName;

    private String remark;  //备注

    private String failure; //失败原因

    private Integer createBy; //创建人

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp createTime; //创建时间

    private Integer updateBy; //更新人

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp updateTime; //更新时间
}
