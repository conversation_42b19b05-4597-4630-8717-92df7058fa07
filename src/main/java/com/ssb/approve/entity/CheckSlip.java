package com.ssb.approve.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;

/**
 * @Description: 核对单
 * @Author: zhangzeng
 * @date: $2021/7/269
 */
@Data
public class CheckSlip {

    private String enterpriseName;

    private int eid;

    private String projectName;

    private Integer pid;

    private BigDecimal salary;

    private Integer accountBalance;

    private Integer payment;

    private String contactName;

    private String contactPhone;

    private Integer node;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private Timestamp entryTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Timestamp settleTime;


    public String getEnterpriseName() {
        if (enterpriseName == null || enterpriseName == ""){
            return enterpriseName = "-";
        }else {
            return enterpriseName;
        }
    }

    public BigDecimal getSalary() {
        if (salary == null) {
            return null;
        }
        return salary.setScale(2, RoundingMode.HALF_UP);
    }
}

