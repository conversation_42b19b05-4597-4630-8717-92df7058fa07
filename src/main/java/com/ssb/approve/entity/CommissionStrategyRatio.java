package com.ssb.approve.entity;

import lombok.Data;

import java.sql.Timestamp;

/**
 * 策略提成比例
 */
@Data
public class CommissionStrategyRatio {

    private Integer id; //id

    private Integer strategyId; // 策略id

    private Integer type; // 提成类型   0/招聘提成   1/销售提成

    private Integer commissionLevelId; // 提成级别id

    private String high; // 提成比例-高

    private String baseHigh; // 提成比例-中高

    private String base; // 提成比例-基础

    private String baseLow; // 提成比例-中低

    private String low; // 提成比例-低

    private Integer createBy; // 创建人

    private Timestamp createTime; // 创建时间

    private Integer updateBy; // 更新人

    private Timestamp updateTime; // 更新时间

    private Integer delFlag; // 删除标识 0 正常  1 删除

}