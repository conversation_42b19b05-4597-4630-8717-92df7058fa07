package com.ssb.approve.entity;

import lombok.Data;

@Data
public class CommissionStrategyDifficulty {

    private Integer id;
    private Integer strategyId;//策略id
    private Integer classifyId;//行业id（已废弃）
    private String high;//难度-高
    private String base;//难度-一般
    private String low;//难度-低
    private Integer createBy;
    private java.sql.Timestamp createTime;
    private Integer updateBy;
    private java.sql.Timestamp updateTime;
    private Integer delFlag;

    private Integer industryFirstId;
    private Integer industrySecondId;
    private Integer positionFirstId;
    private Integer positionSecondId;
}
