package com.ssb.approve.service;

import com.ssb.approve.entity.FollowRevokeLog;
import com.ssb.approve.entity.FollowUp;
import com.ssb.approve.model.QueryFollowRevokeModel;

import java.util.Map;

/**
 * @Author: WangShiyong
 * @Date: 2021/10/15 2:18 PM
 * @Description:撤销
 */
public interface FollowRevokeService {

    /**
     * 数据校对-面试列表
     * @param model
     * @return
     */
    Map getRevokeInterviewList(QueryFollowRevokeModel model);

    /**
     * 数据校对-入职列表
     * @param model
     * @return
     */
    Map getRevokeEntryList(QueryFollowRevokeModel model);

    /**
     * 数据校对-结算列表
     * @param model
     * @return
     */
    Map getRevokeSettleList(QueryFollowRevokeModel model);

    /**
     * 撤销
     * @param followRevoke
     * @return
     */
    Map revoke(FollowRevokeLog followRevoke);

}
