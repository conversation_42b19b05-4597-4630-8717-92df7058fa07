package com.ssb.approve.service;

import com.alibaba.fastjson.JSONObject;

/**
 * @ClassName: FollowCommissionService
 * @Description: 跟进提成 service
 * @Author: YZK
 * @Date: 2021年1月4日11:29:50
 **/
public interface FollowCommissionService {

    /**
     * 保存跟进 提成关系
     *
     * @param followId  跟进id
     * @param projectId 项目id
     */
    void saveFollowCommission(Integer followId, Integer projectId, JSONObject jsonObjectResult);

    /**
     * 保存兼职跟进 提成关系
     * @param followId
     * @param jsonObjectResult
     * @param userJsonObject
     */
    void savePartTimeJobFollowCommission(Integer followId, JSONObject jsonObjectResult, JSONObject userJsonObject);

}
