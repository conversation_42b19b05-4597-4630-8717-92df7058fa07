package com.ssb.approve.service;

import com.ssb.approve.model.FollowUpModel;
import com.ssb.approve.model.QueryFollowUpModel;

import java.util.Map;

/**
 * @ClassName: SettleDetailsService
 * @Description: 结算明细记录 service
 * @Author: YZK
 * @Date: 2019年12月27日16:15:42
 **/
public interface SettleDetailsService {

    /**
     * 获取入职待结算列表
     *
     * @param queryFollowUpModel
     * @return
     */
    Map getSettleDetailsList(QueryFollowUpModel queryFollowUpModel);

    /**
     * 获取结算确认列表
     *
     * @param queryFollowUpModel
     * @return
     */
    Map getSettleAuthList(QueryFollowUpModel queryFollowUpModel);

    /**
     * 结算确认 -- 结算、离职操作
     *
     * @param followUpModel
     * @return
     */
    Map updateSettleAuth(FollowUpModel followUpModel);

    /**
     * 更新临时结算状态（猎头操作）
     *
     * @param followUpModel
     * @return
     */
    Map updateSettleDetails(FollowUpModel followUpModel);

    /**
     * 获取结算确认列表
     *
     * @param queryFollowUpModel
     * @return
     */
    Map getEnterpriseProjectSettleAuthList(QueryFollowUpModel queryFollowUpModel);

    /**
     * 结算确认 -- 结算、离职操作
     *
     * @param followUpModel
     * @return
     */
    Map updateEnterpriseSettleAuth(FollowUpModel followUpModel);
}
