package com.ssb.approve.service;

import com.ssb.approve.entity.CrmFollowRecommend;
import com.ssb.approve.model.FollowUpModel;
import com.ssb.approve.model.QueryAppResumeFollowUpList;
import com.ssb.approve.model.QueryFollowUpModel;
import com.ssb.approve.model.dto.FollowRecommendDTO;
import com.ssb.approve.model.dto.FollowRecommendListDTO;

import java.util.Map;

/**
 * @ClassName: FollowUpService
 * @Description: 跟进记录Service
 * @Author: YZK
 * @Date: 2019年12月20日09:29:36
 **/
public interface FollowUpService {

    /**
     * 检测求职用户是否正在关联项目
     *
     * @param contactId
     * @param projectId
     * @return
     */
    Map checkFollowUp(Integer contactId, Integer projectId);

    /**
     * 保存推荐
     * @param dto
     * @return
     */
    Map saveFollowRecommend(FollowRecommendDTO dto);

    /**
     * 推荐列表
     * @param dto
     * @return
     */
    Map getFollowRecommendList(FollowRecommendListDTO dto);

    /**
     * 保存跟进记录
     *
     * @param followUpModel
     * @return
     */
    Map save(FollowUpModel followUpModel);

    /**
     * 获取我的意象列表 tab数量
     *
     * @param queryFollowUpModel
     * @return
     */
    Map getImageCount(QueryFollowUpModel queryFollowUpModel);

    /**
     * 获取审核数量
     *
     * @param queryFollowUpModel
     * @return
     */
    Map getAuthCount(QueryFollowUpModel queryFollowUpModel);
    /**
     * 获取待约面
     *
     * @param queryFollowUpModel
     * @return
     */
    Map getAppointmentList(QueryFollowUpModel queryFollowUpModel);

    /**
     * 更新约面状态
     *
     * @param followUpModel
     * @return
     */
    Map updateAppointment(FollowUpModel followUpModel);

    /**
     * 获取历史跟进列表
     *
     * @param queryFollowUpModel
     * @return
     */
    Map getHistoryList(QueryFollowUpModel queryFollowUpModel);

    /**
     * 检测用户未完结跟进记录
     *
     * @param queryFollowUpModel
     * @return
     */
    Map checkQuitFollowUp(QueryFollowUpModel queryFollowUpModel);

    /**
     * 获取项目跟进详情
     *
     * @param queryFollowUpModel
     * @return
     */
    Map getProjectFollowUpDetails(QueryFollowUpModel queryFollowUpModel);

    /**
     * app简历跟进结果列表
     * @param queryAppResumeFollowUpList
     * @return
     */
    Map getAppResumeFollowUpList(QueryAppResumeFollowUpList queryAppResumeFollowUpList);

    /**
     * app简历跟进结果图表
     * @param queryAppResumeFollowUpList
     * @return
     */
    Map getAppResumeFollowUpChart(QueryAppResumeFollowUpList queryAppResumeFollowUpList);

    Map getCooperationProjectFollowUpList(QueryFollowUpModel queryFollowUpModel);

    Map stopFollowUp(Integer userId, Integer projectId);

    Map getShareProjectFollowUpCount(Integer projectId);

    Object getFollowRecommendListByEnterprise(FollowRecommendListDTO dto);

    Object auditFollowRecommendById(CrmFollowRecommend dto);

    Object getRecommendInfoListByEnterprise(FollowRecommendListDTO dto);

    Map<String, Object> waitingForInterviewList(FollowUpModel model);

    Map<String, Object> waitingForAgreedInterview(FollowUpModel model);

    Map<String, Object> waitingForEntry(FollowUpModel model);

    Map<String, Object> settlement(FollowUpModel model);

    Map getFollowUpRecByProjectId(FollowRecommendListDTO dto);
}
