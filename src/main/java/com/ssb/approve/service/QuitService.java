package com.ssb.approve.service;

import java.util.Map;

/**
 * @ClassName: QuitService
 * @Description: 离职Service
 * @Author: YZK
 * @Date: 2020年02月20日09:44:03
 **/
public interface QuitService {

    /**
     * 更新离职招聘跟进
     * 替换对接人
     *
     * @param fromUserId
     * @param toUserId
     * @return
     */
    Map updateQuitFollowUp(Integer eid, Integer fromUserId, Integer toUserId, Integer uid);

}
