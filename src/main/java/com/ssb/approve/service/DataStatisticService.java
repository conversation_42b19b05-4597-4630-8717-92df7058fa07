package com.ssb.approve.service;

import com.ssb.approve.model.*;
import com.ssb.approve.model.dto.*;

import java.text.ParseException;
import java.util.List;
import java.util.Map;

/**
 * @ClassName: DataStatisticService
 * @Description: 数据统计 跟进 Service
 * @Author: YZK
 * @Date: 2020年9月2日10:13:04
 **/
public interface DataStatisticService {

    /**
     * 团队项目跟进数据
     *
     * @param queryFollowUpModel
     * @return
     */
    Map getTeamFollowUpData(QueryFollowUpModel queryFollowUpModel);

    /**
     * 个人跟进数据
     *
     * @param queryFollowUpModel
     * @return
     */
    Map getIndividualData(QueryFollowUpModel queryFollowUpModel);

    /**
     * 个人数据 邀约量
     *
     * @param queryFollowUpModel
     * @return
     */
    Map getIndividualInviteData(QueryFollowUpModel queryFollowUpModel);

    /**
     * 个人数据 面试量
     *
     * @param queryFollowUpModel
     * @return
     */
    Map getIndividualInterviewData(QueryFollowUpModel queryFollowUpModel);

    /**
     * 个人数据 offer量
     *
     * @param queryFollowUpModel
     * @return
     */
    Map getIndividualOfferData(QueryFollowUpModel queryFollowUpModel);

    /**
     * 个人数据 入职量
     *
     * @param queryFollowUpModel
     * @return
     */
    Map getIndividualEntryData(QueryFollowUpModel queryFollowUpModel);

    /**
     * 个人数据 结算量
     *
     * @param queryFollowUpModel
     * @return
     */
    Map getIndividualSettleData(QueryFollowUpModel queryFollowUpModel);

    /**
     * 获取下级用户id
     * @param userId
     * @param enterpriseId
     * @return
     */
    List<Integer> getUserIds(Integer userId, Integer enterpriseId);

    /**
     * 销售工作台
     * @param mk
     * @return
     */
    Map getVertrieb(MarketWorkModel mk);

    /**
     * 核对单
     * @param cs
     * @return
     */
    Map getCheckSlip(CheckSlipModel cs);

    /**
     * 核对单明细
     * @param cs
     * @return
     */
    Map getCheckSlipDetail(CheckSlipModel cs);

    /**
     * 核对单明细导出
     */
    String exportExpectedCheckSlip(CheckSlipModel cs) throws ParseException;

    /**
     * 已撤销列表
     * @param model
     * @return
     */
    Map getRevokeList(QueryFollowRevokeStatisticsModel model);

    /**
     * 卡片
     * @param userId
     * @param enterpriseId
     * @return
     */
    Map card(Integer userId, Integer enterpriseId);

    /**
     * 排行榜
     * @param deptId
     * @param userId
     * @param enterpriseId
     * @return
     */
    Map getTheCharts(TheChartsDTO dto);

    Map getDailyData(DailyDataDTO dto);

    Map getRecruitmentData(RecruitmentDataDTO dto);

    Map getInvitationForInterviewDataList(RecruitmentDataListDTO dto);

    Map getInterviewDataList(RecruitmentDataListDTO dto);

    Map getOfferDataList(RecruitmentDataListDTO dto);

    Map getEntryDataList(RecruitmentDataListDTO dto);

    Map getSettleDataList(RecruitmentDataListDTO dto);

    Map getConversionRateDataList(ConversionRateDataDTO dto);

    Map getBasicData(BasicDataDTO dto);

    Map getEnterpriseBasicData(BasicDataDTO dto);

    Map getPerformanceTarget(PerformanceTargetDTO dto);
}
