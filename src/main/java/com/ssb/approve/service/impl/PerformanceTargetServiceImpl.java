package com.ssb.approve.service.impl;

import com.ssb.approve.dao.PerformanceTargetDAO;
import com.ssb.approve.entity.PerformanceTarget;
import com.ssb.approve.service.PerformanceTargetService;
import com.ssb.approve.utils.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Calendar;
import java.util.Date;
import java.util.Map;

@Service
public class PerformanceTargetServiceImpl implements PerformanceTargetService {

    @Autowired
    private PerformanceTargetDAO performanceTargetDAO;

    @Override
    public Map save(PerformanceTarget performanceTarget) {
        // 参数校验
        if (performanceTarget.getPerformance() == null) {
            return MapUtils.create("no", 0, "msg", "业绩不能为空");
        }
        
        // 检查业绩是否超过6位数
        if (performanceTarget.getPerformance().compareTo(new BigDecimal("999999")) > 0) {
            return MapUtils.create("no", 0, "msg", "仅支持六位数字");
        }

        // 查询是否已存在数据
        PerformanceTarget existingTarget = performanceTargetDAO.getByUserIdAndYearAndMonth(
            performanceTarget.getUserId(),
            performanceTarget.getYear(),
            performanceTarget.getMonth()
        );

        if (existingTarget != null) {
            // 如果存在则更新
            existingTarget.setPerformance(performanceTarget.getPerformance());
            existingTarget.setUpdateBy(performanceTarget.getUserId());
            existingTarget.setUpdateTime(new Date());
            performanceTargetDAO.update(existingTarget);
        } else {
            // 如果不存在则新增
            performanceTarget.setCreateBy(performanceTarget.getUserId());
            performanceTarget.setCreateTime(new Date());
            performanceTarget.setUpdateBy(performanceTarget.getUserId());
            performanceTarget.setUpdateTime(new Date());
            performanceTargetDAO.save(performanceTarget);
        }
        
        return MapUtils.create("no", 1);
    }
} 