package com.ssb.approve.service.impl;

import com.ssb.approve.dao.NoticeRecordDAO;
import com.ssb.approve.entity.CrmNoticeRecord;
import com.ssb.approve.service.NoticeRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class NoticeRecordServiceImpl implements NoticeRecordService {

    @Autowired
    private NoticeRecordDAO noticeRecordDAO;

    @Override
    public void updateNoticeRecord(CrmNoticeRecord record) {
        noticeRecordDAO.updateRecord(record);
    }

    @Override
    public void updateProjectNoticeRecord(CrmNoticeRecord record){
        noticeRecordDAO.updateProjectNoticeRecord(record);
    }

    @Override
    public void noticeCancelShareProject(List<CrmNoticeRecord> crmNoticeRecord) {
        noticeRecordDAO.batchInsert(crmNoticeRecord);
    }
}
