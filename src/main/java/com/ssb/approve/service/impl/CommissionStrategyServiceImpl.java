package com.ssb.approve.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.ssb.approve.dao.CommissionStrategyDAO;
import com.ssb.approve.dao.CommissionStrategyDifficultyDAO;
import com.ssb.approve.dao.CommissionStrategyRatioDAO;
import com.ssb.approve.dao.SalesCommissionCutDAO;
import com.ssb.approve.entity.CommissionStrategy;
import com.ssb.approve.entity.CommissionStrategyDifficulty;
import com.ssb.approve.entity.SalesCommissionCut;
import com.ssb.approve.model.CommissionStrategyModel;
import com.ssb.approve.model.CommissionStrategyRatioModel;
import com.ssb.approve.model.PositionIndustryModel;
import com.ssb.approve.model.QueryCommissionStrategyModel;
import com.ssb.approve.model.vo.CommissionStrategyDifficultyVO;
import com.ssb.approve.model.vo.CommissionStrategyVO;
import com.ssb.approve.service.CommissionStrategyService;
import com.ssb.approve.service.client.ProjectService;
import com.ssb.approve.utils.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: WangShiyong
 * @Date: 2020/12/22 9:22 AM
 * @Description:
 */
@Service
public class CommissionStrategyServiceImpl implements CommissionStrategyService {
//
//    @Autowired
//    private CommissionStrategyDAO commissionStrategyDAO;
//
//    @Autowired
//    private CommissionStrategyRatioDAO commissionStrategyRatioDAO;
//
//    @Autowired
//    private CommissionStrategyDifficultyDAO commissionStrategyDifficultyDAO;
//
//    @Autowired
//    private SalesCommissionCutDAO salesCommissionCutDAO;
//
//    @Autowired
//    private ProjectService projectService;
//
//    /**
//     * 提成策略列表
//     *
//     * @param query
//     * @return
//     */
//    @Override
//    public Map getStrategyList(QueryCommissionStrategyModel query) {
//        Integer pageBegin = (query.getPageNum() - 1) * query.getPageSize();
//        query.setPageBegin(pageBegin);
//
//        List<CommissionStrategyVO> list = commissionStrategyDAO.getStrategyList(query);
//
//        Integer count = commissionStrategyDAO.getStrategyListCount(query);
//
//        return MapUtils.create("no", 1, "detail", list, "count", count);
//    }
//
//    /**
//     * 新建提成策略
//     *
//     * @param commissionStrategy
//     * @return
//     */
//    @Override
//    @Transactional
//    public Map insertStrategy(CommissionStrategy commissionStrategy) {
//
//        //标题是否存在
//        Integer id = commissionStrategyDAO.judgeStrategyTitle(commissionStrategy.getTitle(), commissionStrategy.getEnterpriseId());
//        if(id != null){
//            return MapUtils.create("no", 0, "msg", "标题已存在");
//        }
//
//        //默认未开启难度关联提成比例
//        commissionStrategy.setType(0);
//        commissionStrategyDAO.insertStrategy(commissionStrategy);
//
//        //新增空的难度提成策略
//        List<CommissionStrategyDifficulty> commissionStrategyDifficultyList = Lists.newArrayList();
//        CommissionStrategyDifficulty difficulty = null;
//
//        Object result = projectService.getIndustryPosition();
//        JSONObject jsonResult = JSONObject.parseObject(JSON.toJSON(result).toString());
//        JSONArray jsonResultArray = null;
//        JSONObject jsonResultObject = null;
//        if (jsonResult.getInteger("no") != null && jsonResult.getInteger("no").equals(200)) {
//            jsonResultArray = jsonResult.getJSONArray("detail");
//            if (jsonResultArray != null) {
//                for (int i = 0; i < jsonResultArray.size(); i++) {
//
//                    jsonResultObject = jsonResultArray.getJSONObject(i);
//                    difficulty = new CommissionStrategyDifficulty();
//                    difficulty.setStrategyId(commissionStrategy.getId());
//                    difficulty.setCreateBy(commissionStrategy.getCreateBy());
//                    difficulty.setIndustryFirstId(jsonResultObject.getInteger("industryFirstId"));
//                    difficulty.setIndustrySecondId(jsonResultObject.getInteger("industrySecondId"));
//                    difficulty.setPositionFirstId(jsonResultObject.getInteger("positionFirstId"));
//                    difficulty.setPositionSecondId(jsonResultObject.getInteger("positionSecondId"));
//
//                    //字段已废弃 0占位
//                    difficulty.setClassifyId(0);
//
//                    commissionStrategyDifficultyList.add(difficulty);
//                }
//            }
//        }
//
//        if(commissionStrategyDifficultyList != null && commissionStrategyDifficultyList.size() > 0){
//            commissionStrategyDifficultyDAO.batchInsertDifficulty(commissionStrategyDifficultyList);
//        }
//
//
//        return MapUtils.create("no", 1, "msg", "新建成功");
//    }
//
//    /**
//     * 更新策略名称
//     *
//     * @param commissionStrategyModel
//     * @return
//     */
//    @Override
//    public Map updateStrategy(CommissionStrategyModel commissionStrategyModel) {
//
//        if(commissionStrategyModel.getTitle() != null){
//            //标题是否存在
//            Integer id = commissionStrategyDAO.judgeStrategyTitle(commissionStrategyModel.getTitle(), commissionStrategyModel.getEnterpriseId());
//            //标题不存在且不是当前策略标题
//            if(id != null && id != commissionStrategyModel.getId()){
//                return MapUtils.create("no", 0, "msg", "标题已存在");
//            }
//        }
//
//        commissionStrategyDAO.updateStrategy(commissionStrategyModel);
//        return MapUtils.create("no", 1, "msg", "更新成功");
//    }
//
//    /**
//     * 管理提成级别列表
//     *
//     * @return
//     */
//    @Override
//    public Map getCommissionLevelList(Integer enterpriseId) {
//        return MapUtils.create("no", 1, "detail", commissionStrategyDAO.getCommissionLevelList(enterpriseId));
//    }
//
//    /**
//     * 包含猎头与销售
//     *
//     * @param enterpriseId
//     * @return
//     */
//    @Override
//    public Map getCommissionLevelListV2(Integer enterpriseId) {
//
//        return MapUtils.create("no", 1, "detail", commissionStrategyDAO.getCommissionLevelListV2(enterpriseId));
//    }
//
//    /**
//     * 新增/修改提成策略
//     *
//     * @param list
//     * @param userId
//     * @return
//     */
//    @Override
//    @Transactional
//    public Map saveStrategyRatio(List<CommissionStrategyRatioModel> list, Integer userId) {
//
//        for(CommissionStrategyRatioModel model:list){
//            //比例要求在0-100内范围内  可以取两位小数
//            //校验是否是0.00 - 1之间的四位小数
//            if(!judgeStrategyRatio(model.getHigh()) || !judgeStrategyRatio(model.getBase()) || !judgeStrategyRatio(model.getLow())){
//                return MapUtils.create("no", 0, "msg", "比例异常");
//            }
//        }
//
//        commissionStrategyRatioDAO.deleteStrategyRatio(list.get(0).getStrategyId(), userId);
//        commissionStrategyRatioDAO.batchInsert(list, userId);
//        return MapUtils.create("no", 1, "msg", "修改成功");
//    }
//
//    public boolean judgeStrategyRatio(String num){
//        if(num == null || num.isEmpty()){
//            return true;
//        }
//        //0%-100%以内
//        Double a = Double.parseDouble(num);
//        if(0 > a || a > 1){
//            return false;
//        }
//
//        //两位小数
//        if(num.indexOf(".")>0){
//            String[] b = num.split("\\.");
//            if(b[1].length()>4){
//                return false;
//            }
//        }
//        return true;
//    }
//
//    /**
//     * 新增/修改行业难度
//     *
//     * @param difficulty
//     * @return
//     */
//    @Override
//    @Transactional
//    public Map saveStrategyDifficulty(CommissionStrategyDifficulty difficulty) {
//
//        //新增
//        if(difficulty.getId() == null){
//            if(difficulty.getClassifyId() == null || difficulty.getStrategyId() == null){
//                return MapUtils.create("no", 0, "msg", "参数异常");
//            }
//
//            commissionStrategyDifficultyDAO.insertDifficulty(difficulty);
//        }else {
//            commissionStrategyDifficultyDAO.updateDifficulty(difficulty);
//        }
//        return MapUtils.create("no", 1, "msg", "修改成功");
//    }
//
//    /**
//     * 策略详情
//     *
//     * @param id
//     * @return
//     */
//    @Override
//    public Map getStrategyInfo(Integer id) {
//
//        CommissionStrategyVO strategyInfo = commissionStrategyDAO.getStrategyInfo(id);
//
//        Integer projectCount = commissionStrategyDAO.getProjectCountByIdAndStatus(id, 0);
//        Integer projectRecruitingCount = commissionStrategyDAO.getProjectCountByIdAndStatus(id, 1);
//        Integer suspendCount = commissionStrategyDAO.getProjectCountByIdAndStatus(id, 2);
//        Integer stopCount = commissionStrategyDAO.getProjectCountByIdAndStatus(id, 3);
//
//        //总数 在招  暂停  结束
//        strategyInfo.setProjectCount(projectCount);
//        strategyInfo.setProjectRecruitingCount(projectRecruitingCount);
//        strategyInfo.setSuspendCount(suspendCount);
//        strategyInfo.setStopCount(stopCount);
//
//        return MapUtils.create("no", 1, "detail", strategyInfo);
//    }
//
//    /**
//     * 删除策略
//     *
//     * @param id
//     * @param userId
//     * @return
//     */
//    @Override
//    public Map deleteStrategy(Integer id, Integer userId) {
//
//        //查询管理项目数量
//        Integer count = commissionStrategyDAO.getProjectCountByIdAndStatus(id, 0);
//        if(count > 0){
//            return MapUtils.create("no", 0, "msg", "有项目使用提成策略后，不能删除。");
//        }
//
//        commissionStrategyDAO.deleteStrategyById(id, userId);
//
//        return MapUtils.create("no", 1, "msg", "删除成功。");
//    }
//
//    /**
//     * 新增销售削减比例
//     *
//     * @param id
//     * @param userId
//     * @param commission
//     * @return
//     */
//    @Override
//    @Transactional
//    public Map saveSaleCommissionCut(Integer id, Integer userId, Integer enterpriseId, String commission) {
//
//        String[] rate = commission.split(",");
//        List<SalesCommissionCut> salesCommissionCutList = Lists.newArrayList();
//        //level  0/销售  1/主管   2/销售主管   3/经理
//        for (int i=0;i<rate.length;i++) {
//            SalesCommissionCut salesCommissionCut = new SalesCommissionCut();
//            salesCommissionCut.setEnterpriseId(enterpriseId);
//            salesCommissionCut.setCommission(Double.parseDouble(rate[i]));
//            salesCommissionCut.setLevel(i);
//            salesCommissionCut.setStrategyId(id);
//            salesCommissionCut.setCreateBy(userId);
//
//            salesCommissionCutList.add(salesCommissionCut);
//        }
//
//        if(!salesCommissionCutList.isEmpty()){
//            salesCommissionCutDAO.deleteSalesCommissionCut(id, userId);
//            salesCommissionCutDAO.insertSalesCommissionCut(salesCommissionCutList);
//            return MapUtils.create("no", 1, "msg", "修改成功");
//        }
//        return MapUtils.create("no", 0, "msg", "参数异常");
//    }
//
//    /**
//     * 行业岗位难度列表
//     *
//     * @param id
//     * @param userId
//     * @param industryId
//     * @param positionId
//     * @return
//     */
//    @Override
//    public Map getStrategyDifficultyList(Integer id, Integer userId, Integer industryId, Integer positionId) {
//
//        List<CommissionStrategyDifficultyVO> difficultyList = commissionStrategyDifficultyDAO.getDifficultyList(id, industryId, positionId);
//
//        //2.1.0发版时可能存在旧数据 行业岗位难度为空情况  特殊处理
//        //如果难度为空  重新新增难度
//        if(difficultyList.isEmpty() && industryId == null && positionId == null){
//            List<CommissionStrategyDifficulty> commissionStrategyDifficultyList = Lists.newArrayList();
//
//            Object result = projectService.getIndustryPosition();
//            JSONObject jsonResult = JSONObject.parseObject(JSON.toJSON(result).toString());
//            JSONArray jsonResultArray = null;
//            JSONObject jsonResultObject = null;
//            if (jsonResult.getInteger("no") != null && jsonResult.getInteger("no").equals(200)) {
//                jsonResultArray = jsonResult.getJSONArray("detail");
//                if (jsonResultArray != null) {
//                    for (int i = 0; i < jsonResultArray.size(); i++) {
//
//                        jsonResultObject = jsonResultArray.getJSONObject(i);
//                        CommissionStrategyDifficulty difficulty = new CommissionStrategyDifficulty();
//                        difficulty.setStrategyId(id);
//                        difficulty.setCreateBy(userId);
//                        difficulty.setClassifyId(0);
//                        difficulty.setIndustryFirstId(jsonResultObject.getInteger("industryFirstId"));
//                        difficulty.setIndustrySecondId(jsonResultObject.getInteger("industrySecondId"));
//                        difficulty.setPositionFirstId(jsonResultObject.getInteger("positionFirstId"));
//                        difficulty.setPositionSecondId(jsonResultObject.getInteger("positionSecondId"));
//
//                        commissionStrategyDifficultyList.add(difficulty);
//                    }
//                }
//            }
//
//            if(commissionStrategyDifficultyList != null && commissionStrategyDifficultyList.size() > 0){
//                commissionStrategyDifficultyDAO.batchInsertDifficulty(commissionStrategyDifficultyList);
//            }
//            //新增后重新查询 保证有行业岗位难度列表
//            difficultyList = commissionStrategyDifficultyDAO.getDifficultyList(id, industryId, positionId);
//
//            if(difficultyList.isEmpty()){
//                return MapUtils.create("no", 0, "msg", "请先设置岗位");
//            }
//        }
//
//        List<Map<String, Object>> detail = Lists.newArrayList();
//
//        //处理难度列表
//        //按一级行业分组
//        Map<String, List<CommissionStrategyDifficultyVO>> groupByFirstIndustryMap = difficultyList.stream()
//                .collect(Collectors.groupingBy(CommissionStrategyDifficultyVO::getIndustryFirstName));
//
//        //一级行业
//        for (Map.Entry firstIndustryEntry : groupByFirstIndustryMap.entrySet()) {
//
//            Map<String, Object> firstIndustryMap = new HashMap<>();
//            firstIndustryMap.put("firstIndustry", firstIndustryEntry.getKey());
//
//            //二级行业
//            List<CommissionStrategyDifficultyVO> secondIndustryList = (List<CommissionStrategyDifficultyVO>)firstIndustryEntry.getValue();
//            //按二级行业分组
//            Map<String, List<CommissionStrategyDifficultyVO>> groupBySecondIndustryMap = secondIndustryList.stream()
//                    .collect(Collectors.groupingBy(CommissionStrategyDifficultyVO::getIndustrySecondName));
//
//            //处理二级行业
//            List<Map<String, Object>> secondIndustryMapList = new ArrayList<>();
//            for (Map.Entry secondIndustryEntry : groupBySecondIndustryMap.entrySet()) {
//
//                Map<String, Object> secondIndustryMap = new HashMap<>();
//                secondIndustryMap.put("secondIndustry", secondIndustryEntry.getKey());
//
//                //一级岗位
//                List<CommissionStrategyDifficultyVO> firstPositionList = (List<CommissionStrategyDifficultyVO>)secondIndustryEntry.getValue();
//                //按一级岗位分组
//                Map<String, List<CommissionStrategyDifficultyVO>> groupByFirstPositionMap = firstPositionList.stream()
//                        .collect(Collectors.groupingBy(CommissionStrategyDifficultyVO::getPositionFirstName));
//
//                //处理一级岗位
//                List<Map<String, Object>> firstPositionMapList = new ArrayList<>();
//                for (Map.Entry firstPositionEntry : groupByFirstPositionMap.entrySet()) {
//
//                    Map<String, Object> firstPositionMap = new HashMap<>();
//                    firstPositionMap.put("firstPosition", firstPositionEntry.getKey());
//
//                    List<CommissionStrategyDifficultyVO> secondPositionList = (List<CommissionStrategyDifficultyVO>)firstPositionEntry.getValue();
//
//                    List<Map<String, Object>> secondPositionMapList = new ArrayList<>();
//                    for(CommissionStrategyDifficultyVO difficultyVO : secondPositionList){
//
//                        Map<String, Object> secondPositionMap = new HashMap<>();
//                        secondPositionMap.put("difficultyId", difficultyVO.getId());
//                        secondPositionMap.put("secondPosition", difficultyVO.getPositionSecondName());
//                        secondPositionMap.put("high", difficultyVO.getHigh());
//                        secondPositionMap.put("base", difficultyVO.getBase());
//                        secondPositionMap.put("low", difficultyVO.getLow());
//
//                        secondPositionMapList.add(secondPositionMap);
//                    }
//
//                    firstPositionMap.put("secondPositionList", secondPositionMapList);
//
//                    firstPositionMapList.add(firstPositionMap);
//                }
//                secondIndustryMap.put("firstPositionList", firstPositionMapList);
//                secondIndustryMapList.add(secondIndustryMap);
//            }
//            firstIndustryMap.put("secondIndustryList", secondIndustryMapList);
//            detail.add(firstIndustryMap);
//        }
//        return MapUtils.create("no", 1, "detail", detail);
//    }
//
//    /**
//     * 行业岗位修改 对应提成策略难度修改
//     *
//     * @param map
//     * @return
//     */
//    @Override
//    @Transactional
//    public Map updateStrategyDifficultyList(Map<String, Object> map) {
//
//        //用户ID
//        Integer userId = Integer.parseInt(map.get("userId")+"");
//        //一级行业id
//        Integer industryFirstId = Integer.parseInt(map.get("industryFirstId")+"");
//        Integer industryId = Integer.parseInt(map.get("industryId")+"");
//
//        //修改的行业岗位信息
//        List<PositionIndustryModel> positionIndustryList = JSON.parseArray(JSON.toJSONString(map.get("positionIndustryList")),PositionIndustryModel.class);
//
//        //已存在难度策略
//        List<PositionIndustryModel> allDifficultyList = commissionStrategyDifficultyDAO.getAllDifficultyList(industryId);
//
//        if (!positionIndustryList.isEmpty()) {
//            //新行业岗位关系
//            List<PositionIndustryModel> newList = diffList(positionIndustryList, allDifficultyList);
//            //删除的行业岗位关系
//            List<PositionIndustryModel> deleteList = diffList(allDifficultyList, positionIndustryList);
//
//            //全部策略id 装配
//            if (!newList.isEmpty()) {
//
//                List<PositionIndustryModel> newStrategyDifficultyList = Lists.newArrayList();
//
//                List<Integer> strategyIdList = commissionStrategyDAO.getAllStrategyId();
//                if (strategyIdList != null) {
//                    for (Integer strategyId : strategyIdList) {
//                        for (PositionIndustryModel model : newList) {
//                            PositionIndustryModel newStrategyDifficulty = new PositionIndustryModel();
//                            newStrategyDifficulty.setIndustryFirstId(industryFirstId);
//                            newStrategyDifficulty.setIndustryId(model.getIndustryId());
//                            newStrategyDifficulty.setPositionFirstId(model.getPositionFirstId());
//                            newStrategyDifficulty.setPositionId(model.getPositionId());
//                            newStrategyDifficulty.setStrategyId(strategyId);
//                            newStrategyDifficulty.setUpdateBy(userId);
//
//                            newStrategyDifficultyList.add(newStrategyDifficulty);
//                        }
//                    }
//                    commissionStrategyDifficultyDAO.batchInsertPositionIndustry(newStrategyDifficultyList);
//                }
//            }
//
//            if (!deleteList.isEmpty()) {
//                commissionStrategyDifficultyDAO.batchUpdatePositionIndustry(deleteList, userId);
//            }
//        } else {
//            if (!allDifficultyList.isEmpty()) {
//                commissionStrategyDifficultyDAO.batchUpdatePositionIndustry(allDifficultyList, userId);
//            }
//        }
//
//        return MapUtils.create("no", 200, "msg", "修改成功");
//    }
//
//    private List<PositionIndustryModel> diffList(List<PositionIndustryModel> firstArrayList, List<PositionIndustryModel> secondArrayList) {
//        List<PositionIndustryModel> resultList = firstArrayList.stream()
//                .filter(item -> !secondArrayList.stream().map(e -> e.getPositionId() + "&" + e.getIndustryId())
//                        .collect(Collectors.toList()).contains(item.getPositionId() + "&" + item.getIndustryId())
//                ).collect(Collectors.toList());
//
//        return resultList;
//    }
//
//
//
//
//
//
//
//
//
//
//
//
//
//
}
