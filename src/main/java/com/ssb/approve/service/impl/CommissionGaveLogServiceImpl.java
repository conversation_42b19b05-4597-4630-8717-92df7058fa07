package com.ssb.approve.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.ssb.approve.dao.*;
import com.ssb.approve.entity.*;
import com.ssb.approve.service.CommissionGaveLogService;
import com.ssb.approve.service.client.ProjectService;
import com.ssb.approve.service.client.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.List;

/**
 * @ClassName: CommissionGaveLogServiceImpl
 * @Description: 提成发放 impl
 * @Author: YZK
 * @Date: 2021年1月5日10:58:21
 **/
@Service
@Slf4j
public class CommissionGaveLogServiceImpl implements CommissionGaveLogService {

    private static final long YEAR_TIME_STAMP = 31536000; //一年时间戳 秒

    private static final long TIME_NODE = 1614528000000L; // 时间节点  2021-03-01 00:00:00

    @Autowired
    private SettleDetailsDAO settleDetailsDAO;

    @Autowired
    private FollowCommissionDAO followCommissionDAO;

    @Autowired
    private FollowUpDAO followUpDAO;

    @Autowired
    private CommissionGaveLogDAO commissionGaveLogDAO;

    @Autowired
    private UserService userService;

    @Autowired
    private ProjectService projectService;

    @Autowired
    private SalesCommissionCutDAO salesCommissionCutDAO;

    /**
     * 发放提成
     *
     * @param settleId  结算
     * @param payment   绩效
     */
    @Async
    @Override
    public void gaveCommission(Integer settleId, BigDecimal payment) {
        // 查询计算节点
        SettleDetails settleDetails = settleDetailsDAO.getSettleDetailsById(settleId);
        // 跟进信息
        FollowUp followUp = followUpDAO.getFollowUpById(settleDetails.getFollowId());
        //跟进提成
        FollowCommission followCommission = followCommissionDAO.getFollowCommission(settleDetails.getFollowId());
        // 存在提成规则  同时必须 正确情况下
        if (followCommission == null || followCommission.getErrState() != 0 ) {
            log.warn("gaveCommission  followCommission 跟进提成占比为空");
            return;
        }

        // 项目信息详情
        Object projectInfo = projectService.getProjectInfo(followUp.getProjectId());
        JSONObject jsonProjectResult = JSONObject.parseObject(JSON.toJSON(projectInfo).toString());
        // 查询异常 直接终止
        if (!(jsonProjectResult.getInteger("no") != null && jsonProjectResult.getInteger("no").equals(200))) {
            log.error("gaveCommission -- projectInfo  查询项目信息异常");
            return;
        }

        // 计算 猎头 提成
        gaveHeadhunterCommission(settleDetails, followUp, followCommission, jsonProjectResult, followUp.getCreateBy(), payment);

    }

    /**
     * 发放猎头提成
     * @param settleDetails         结算id
     * @param followUp              跟进信息
     * @param followCommission      跟进成信息
     * @param jsonProjectResult     项目信息
     * @param userId                猎头id
     * @param payment               绩效金额
     */
    public void gaveHeadhunterCommission(SettleDetails settleDetails, FollowUp followUp, FollowCommission followCommission, JSONObject jsonProjectResult, Integer userId, BigDecimal payment) {
        // 项目 处理
        JSONObject jsonProject = jsonProjectResult.getJSONObject("obj");
        //项目id
        Integer projectId = jsonProject.getInteger("id");
        // 处理自己的数据
        CommissionGaveLog commissionGaveLog = new CommissionGaveLog();
        commissionGaveLog.setType(0);
        commissionGaveLog.setCommissionTime(settleDetails.getActualSettleTime());
        commissionGaveLog.setEnterpriseId(followUp.getEnterpriseId());
        commissionGaveLog.setProjectId(projectId);
        commissionGaveLog.setFollowId(followUp.getId());
        commissionGaveLog.setSettleId(settleDetails.getId());
        commissionGaveLog.setClassifyId(followCommission.getClassifyId());
        commissionGaveLog.setIndustryFirstId(followCommission.getIndustryFirstId());
        commissionGaveLog.setIndustrySecondId(followCommission.getIndustrySecondId());
        commissionGaveLog.setPositionFirstId(followCommission.getPositionFirstId());
        commissionGaveLog.setPositionSecondId(followCommission.getPositionSecondId());
        commissionGaveLog.setFollowCommissionId(followCommission.getId());
        commissionGaveLog.setCommissionLevelId(2);
        commissionGaveLog.setCommissionUser(userId);
        commissionGaveLog.setIntegral(payment);
        commissionGaveLog.setMoney(BigDecimal.valueOf(0));
        commissionGaveLog.setCreateBy(settleDetails.getUpdateBy());
        commissionGaveLogDAO.save(commissionGaveLog);
    }

    @Async
    @Override
    public void gavePartTimeJobCommission(Integer settleId, BigDecimal payment) {
        // 查询计算节点
        SettleDetails settleDetails = settleDetailsDAO.getSettleDetailsById(settleId);
        // 跟进信息
        FollowUp followUp = followUpDAO.getFollowUpById(settleDetails.getFollowId());
        //跟进提成
        FollowCommission followCommission = followCommissionDAO.getFollowCommission(settleDetails.getFollowId());
        // 提成规则
        JSONObject rateContentObject = JSONObject.parseObject(followCommission.getRateContent());
        BigDecimal rate = rateContentObject.getJSONObject("headhunterMap").getBigDecimal("projectCommissionRatio").compareTo(BigDecimal.ZERO) > 0 ? rateContentObject.getJSONObject("headhunterMap").getBigDecimal("projectCommissionRatio"):rateContentObject.getJSONObject("headhunterMap").getBigDecimal("userCommissionRatio");
        // 金额转换
        BigDecimal baseMoney = payment;

        if (followCommission == null) {
            log.warn("gaveCommission  followCommission 跟进提成占比为空");
            return;
        }

        // 项目信息详情
        Object projectInfo = projectService.getProjectInfo(followUp.getProjectId());
        JSONObject jsonProjectResult = JSONObject.parseObject(JSON.toJSON(projectInfo).toString());
        // 查询异常 直接终止
        if (!(jsonProjectResult.getInteger("no") != null && jsonProjectResult.getInteger("no").equals(200))) {
            log.error("gaveCommission -- projectInfo  查询项目信息异常");
            return;
        }
        Object aboveOrganizationHeadhunterUsers = userService.getAboveOrganizationUsers(followUp.getCreateBy(), followUp.getEnterpriseId());
        JSONObject jsonHeadhunterResult = JSONObject.parseObject(JSON.toJSON(aboveOrganizationHeadhunterUsers).toString());
        if (!(jsonHeadhunterResult.getInteger("no") != null && jsonHeadhunterResult.getInteger("no").equals(200))) {
            log.error("gaveCommission -- aboveOrganizationHeadhunterUsers  跟进人 猎头 查询对应的 组织结构信息异常");
            return;
        }
        JSONObject jsonHeadhunterObject = jsonHeadhunterResult.getJSONObject("userDetail");

        CommissionGaveLog commissionGaveLog = new CommissionGaveLog();
        commissionGaveLog.setType(0);
        commissionGaveLog.setCommissionTime(settleDetails.getActualSettleTime());
        commissionGaveLog.setEnterpriseId(followUp.getEnterpriseId());
        commissionGaveLog.setProjectId(followUp.getProjectId());
        commissionGaveLog.setFollowId(followUp.getId());
        commissionGaveLog.setSettleId(settleDetails.getId());
        commissionGaveLog.setClassifyId(followCommission.getClassifyId());
        commissionGaveLog.setIndustryFirstId(followCommission.getIndustryFirstId());
        commissionGaveLog.setIndustrySecondId(followCommission.getIndustrySecondId());
        commissionGaveLog.setPositionFirstId(followCommission.getPositionFirstId());
        commissionGaveLog.setPositionSecondId(followCommission.getPositionSecondId());
        commissionGaveLog.setFollowCommissionId(followCommission.getId());
        commissionGaveLog.setCommissionLevelId(2);
        commissionGaveLog.setCommissionUser(followUp.getCreateBy());
        commissionGaveLog.setIntegral(payment);

        BigDecimal ownMoney = rate.multiply(baseMoney);
        commissionGaveLog.setMoney(ownMoney);
        commissionGaveLog.setCreateBy(settleDetails.getUpdateBy());
        commissionGaveLogDAO.save(commissionGaveLog);
    }
}