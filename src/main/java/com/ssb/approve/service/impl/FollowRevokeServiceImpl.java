package com.ssb.approve.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Sets;
import com.ssb.approve.common.constants.Enum.RechargeLogTransactionTypeEnum;
import com.ssb.approve.common.constants.FollowRevokeConstants;
import com.ssb.approve.common.kafka.KafkaProducer;
import com.ssb.approve.dao.*;
import com.ssb.approve.entity.*;
import com.ssb.approve.model.QueryFollowRevokeModel;
import com.ssb.approve.model.vo.FollowRevokeVO;
import com.ssb.approve.model.vo.FollowUpVO;
import com.ssb.approve.model.vo.MenuDetailVO;
import com.ssb.approve.service.FollowRevokeService;
import com.ssb.approve.service.client.ProjectService;
import com.ssb.approve.service.client.UserService;
import com.ssb.approve.utils.DateUtils;
import com.ssb.approve.utils.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Author: WangShiyong
 * @Date: 2021/10/15 2:20 PM
 * @Description:
 */
@Service
public class FollowRevokeServiceImpl implements FollowRevokeService {

    @Autowired
    private FollowUpDAO followUpDAO;

    @Autowired
    private UserService userService;

    @Autowired
    private InterviewRecordDAO interviewRecordDAO;

    @Autowired
    private EntryRecordDAO entryRecordDAO;

    @Autowired
    private SettleDetailsDAO settleDetailsDAO;

    @Autowired
    private FollowRevokeLogDAO followRevokeLogDAO;

    @Autowired
    private FollowLogDAO followLogDAO;

    @Autowired
    private SettleLogDAO settleLogDAO;

    @Autowired
    private RechargeLogDAO rechargeLogDAO;

    @Autowired
    private CommissionGaveLogDAO commissionGaveLogDAO;

    @Autowired
    private ProjectService projectService;

    @Autowired
    private KafkaProducer kafkaProducer;

    /**
     * 数据校对-面试列表
     * @param model
     * @return
     */
    @Override
    public Map getRevokeInterviewList(QueryFollowRevokeModel model) {

        Integer pageBegin = (model.getPageNum() - 1) * model.getPageSize();
        model.setPageBegin(pageBegin);

        //数据查看权限
        Set<String> collect = Sets.newHashSet();
        Object o = userService.getUserRoleMenuDetailsForServerV2(model.getUserId());
        JSONObject jsonObject = JSONObject.parseObject(JSON.toJSON(o).toString());
        if (null != jsonObject.getInteger("no") && jsonObject.getInteger("no").equals(1)) {
            JSONArray objects = jsonObject.getJSONArray("details");
            List<MenuDetailVO> menuDetailVOS = JSONArray.parseArray(JSONArray.toJSONString(objects), MenuDetailVO.class);
            collect = menuDetailVOS.stream().map(MenuDetailVO::getPermission).collect(Collectors.toSet());
        }

        if (collect.contains("data_proofreading_all")) {
            model.setRange(0);
        } else if (collect.contains("data_proofreading_person")) {
            model.setRange(1);
        }

        if (model.getRange() != null) {
            List<FollowRevokeVO> followRevokeList = interviewRecordDAO.getRevokeInterviewList(model);
            int count = interviewRecordDAO.getRevokeInterviewListCount(model);
            return MapUtils.create("no", 1, "details", followRevokeList, "count", count);
        }

        return MapUtils.create("no", 1, "details", Collections.emptyList(), "count", 0);
    }

    /**
     * 数据校对-入职列表
     * @param model
     * @return
     */
    @Override
    public Map getRevokeEntryList(QueryFollowRevokeModel model) {

        Integer pageBegin = (model.getPageNum() - 1) * model.getPageSize();
        model.setPageBegin(pageBegin);

        //数据查看权限
        Set<String> collect = Sets.newHashSet();
        Object o = userService.getUserRoleMenuDetailsForServerV2(model.getUserId());
        JSONObject jsonObject = JSONObject.parseObject(JSON.toJSON(o).toString());
        if (null != jsonObject.getInteger("no") && jsonObject.getInteger("no").equals(1)) {
            JSONArray objects = jsonObject.getJSONArray("details");
            List<MenuDetailVO> menuDetailVOS = JSONArray.parseArray(JSONArray.toJSONString(objects), MenuDetailVO.class);
            collect = menuDetailVOS.stream().map(MenuDetailVO::getPermission).collect(Collectors.toSet());
        }

        if (collect.contains("data_proofreading_all")) {
            model.setRange(0);
        } else if (collect.contains("data_proofreading_person")) {
            model.setRange(1);
        }

        if (model.getRange() != null) {
            List<FollowRevokeVO> followRevokeList = entryRecordDAO.getRevokeEntryList(model);
            int count = entryRecordDAO.getRevokeEntryListCount(model);
            return MapUtils.create("no", 1, "details", followRevokeList, "count", count);
        }

        return MapUtils.create("no", 1, "details", Collections.emptyList(), "count", 0);
    }

    /**
     * 数据校对-结算列表
     *
     * @param model
     * @return
     */
    @Override
    public Map getRevokeSettleList(QueryFollowRevokeModel model) {

        Integer pageBegin = (model.getPageNum() - 1) * model.getPageSize();
        model.setPageBegin(pageBegin);

        //数据查看权限
        Set<String> collect = Sets.newHashSet();
        Object o = userService.getUserRoleMenuDetailsForServerV2(model.getUserId());
        JSONObject jsonObject = JSONObject.parseObject(JSON.toJSON(o).toString());
        if (null != jsonObject.getInteger("no") && jsonObject.getInteger("no").equals(1)) {
            JSONArray objects = jsonObject.getJSONArray("details");
            List<MenuDetailVO> menuDetailVOS = JSONArray.parseArray(JSONArray.toJSONString(objects), MenuDetailVO.class);
            collect = menuDetailVOS.stream().map(MenuDetailVO::getPermission).collect(Collectors.toSet());
        }

        if (collect.contains("data_proofreading_all")) {
            model.setRange(0);
        } else if (collect.contains("data_proofreading_person")) {
            model.setRange(1);
        }

        if (model.getRange() != null) {
            List<FollowRevokeVO> followRevokeList = settleDetailsDAO.getRevokeSettleList(model);
            int count = settleDetailsDAO.getRevokeSettleListCount(model);
            return MapUtils.create("no", 1, "details", followRevokeList, "count", count);
        }

        return MapUtils.create("no", 1, "details", Collections.emptyList(), "count", 0);
    }

    @Override
    public Map revoke(FollowRevokeLog followRevoke) {
        //当前时间
        Timestamp timestamp = new Timestamp(System.currentTimeMillis());

        // 如果撤销的跟进记录中的求职者有在进行中的跟进，不能撤销。
        FollowUpVO onGoingFollowUp = followUpDAO.getOnGoingFollowUp(followRevoke.getFollowId());
        if(onGoingFollowUp != null){
            return MapUtils.create("no", 0, "msg", onGoingFollowUp.getContactName()+ "在" + onGoingFollowUp.getTitle() + "中" + onGoingFollowUp.getUserName() + "正在跟进，不能撤销。");
        }

        if(followRevoke.getType() == 0){
            return revokeInterview(followRevoke, timestamp);
        }else if(followRevoke.getType() == 1){
            return revokeEntry(followRevoke, timestamp);
        }else if(followRevoke.getType() == 2){
            Map map = revokeSettle(followRevoke, timestamp);
            if(map.get("projectId") != null){
                // 结算完更新项目进度
                kafkaProducer.syncUpdateProjectProgress(Integer.parseInt(map.get("projectId") + ""));
            }
            return map;
        }

        return MapUtils.create("no", 0, "msg", "参数异常");
    }

    /**
     * 撤销面试
     * @param followRevoke
     * @return
     */
    @Transactional
    public Map revokeInterview(FollowRevokeLog followRevoke, Timestamp timestamp) {

        // ---------记录撤销日志---------
        // 面试记录
        InterviewRecord interviewRecord = interviewRecordDAO.getInterviewRecordById(followRevoke.getTargetId());
        if (interviewRecord == null) {
            return MapUtils.create("no", 0, "msg", "数据异常");
        }
        Integer followCurrentStatus = followUpDAO.getCurrentStatusById(followRevoke.getFollowId());
        if (followCurrentStatus == null) {
            return MapUtils.create("no", 0, "msg", "数据异常");
        }
        followRevoke.setFollowId(followRevoke.getFollowId());
        followRevoke.setType(followRevoke.getType());
        followRevoke.setFollowStatus(followCurrentStatus);
        followRevoke.setStatusBeforeRevocation(interviewRecord.getStatus());
        followRevoke.setStatusAfterRevocation(FollowRevokeConstants.INTERVIEW_RECORD_DEFAULT);
        followRevoke.setRevokeRemark(followRevoke.getRevokeRemark());
        followRevoke.setRevokeTime(timestamp);
        followRevokeLogDAO.insertFollowRevokeLog(followRevoke);
        // ---------记录撤销日志---------

        // ---------修改follow_up状态---------
        FollowUp followUp = new FollowUp();
        followUp.setId(followRevoke.getFollowId());
        followUp.setStatus(1);
        followUp.setCurrentStatus(FollowRevokeConstants.FOLLOW_UP_APPOINTMENT_INTERVIEW);
        followUp.setFinish(0);
        followUp.setUpdateBy(followRevoke.getRevokeBy());
        followUp.setUpdateTime(timestamp);
        followUpDAO.updateFollowUp(followUp);
        // ---------修改follow_up状态---------

        // ---------增加follow_log---------
        FollowLog followLog = new FollowLog();
        followLog.setEnterpriseId(followRevoke.getEnterpriseId());
        followLog.setFollowId(followRevoke.getFollowId());
        followLog.setType(5);//已撤销
        followLog.setStatus(FollowRevokeConstants.FOLLOW_LOG_REVOKE_INTERVIEW);
        followLog.setRemark(followRevoke.getRevokeRemark());
        followLog.setBehaviorTime(timestamp);
        followLog.setCreateBy(followRevoke.getRevokeBy());
        followLog.setCreateTime(timestamp);
        followLogDAO.save(followLog);
        // ---------增加follow_log---------

        // ---------修改面试记录---------
        InterviewRecord interview = new InterviewRecord();
        interview.setId(followRevoke.getTargetId());
        interview.setNode(1);
        interview.setStatus(FollowRevokeConstants.INTERVIEW_RECORD_DEFAULT);
        interview.setAuth(0);
        interview.setUpdateBy(followRevoke.getRevokeBy());
        interview.setUpdateTime(timestamp);
        interviewRecordDAO.updateRevokeInterviewRecord(interview);
        // ---------修改面试记录---------

        // ---------删除入职记录---------
        int existEntryRecord = entryRecordDAO.existEntryRecordByFollowId(followRevoke.getFollowId());
        if (existEntryRecord > 0) {
            entryRecordDAO.deleteEntryRecordByFollowId(followRevoke.getFollowId());
        }
        // ---------删除入职记录---------

        return MapUtils.create("no", 1, "msg", "撤销成功");
    }

    /**
     * 撤销入职
     *
     * @param followRevoke
     * @return
     */
    @Transactional
    public Map revokeEntry(FollowRevokeLog followRevoke, Timestamp timestamp) {

        // ---------记录撤销日志---------
        // 入职记录
        EntryRecord entryRecord = entryRecordDAO.getEntryRecordById(followRevoke.getTargetId());
        if (entryRecord == null) {
            return MapUtils.create("no", 0, "msg", "数据异常");
        }
        Integer followCurrentStatus = followUpDAO.getCurrentStatusById(followRevoke.getFollowId());
        if (followCurrentStatus == null) {
            return MapUtils.create("no", 0, "msg", "数据异常");
        }
        followRevoke.setFollowId(followRevoke.getFollowId());
        followRevoke.setType(followRevoke.getType());
        followRevoke.setFollowStatus(followCurrentStatus);
        followRevoke.setStatusBeforeRevocation(entryRecord.getStatus());
        followRevoke.setStatusAfterRevocation(FollowRevokeConstants.ENTRY_RECORD_DEFAULT);
        followRevoke.setRevokeRemark(followRevoke.getRevokeRemark());
        followRevoke.setRevokeTime(timestamp);
        followRevokeLogDAO.insertFollowRevokeLog(followRevoke);
        // ---------记录撤销日志---------

        // ---------修改follow_up状态---------
        FollowUp followUp = new FollowUp();
        followUp.setId(followRevoke.getFollowId());
        followUp.setStatus(2);
        followUp.setCurrentStatus(FollowRevokeConstants.FOLLOW_UP_CHANGE_EMPLOYED_TIME);
        followUp.setFinish(0);
        followUp.setUpdateBy(followRevoke.getRevokeBy());
        followUp.setUpdateTime(timestamp);
        followUpDAO.updateFollowUp(followUp);
        // ---------修改follow_up状态---------

        // ---------增加follow_log---------
        FollowLog followLog = new FollowLog();
        followLog.setEnterpriseId(followRevoke.getEnterpriseId());
        followLog.setFollowId(followRevoke.getFollowId());
        followLog.setType(5);//已撤销
        followLog.setStatus(FollowRevokeConstants.FOLLOW_LOG_REVOKE_ENTRY);
        followLog.setRemark(followRevoke.getRevokeRemark());
        followLog.setBehaviorTime(timestamp);
        followLog.setCreateBy(followRevoke.getRevokeBy());
        followLog.setCreateTime(timestamp);
        followLogDAO.save(followLog);
        // ---------增加follow_log---------

        // ---------修改入职记录---------
        EntryRecord entry = new EntryRecord();
        entry.setId(followRevoke.getTargetId());
        entry.setStatus(FollowRevokeConstants.ENTRY_RECORD_DEFAULT);
        entry.setAuth(0);
        entry.setUpdateBy(followRevoke.getRevokeBy());
        entry.setUpdateTime(timestamp);
        entryRecordDAO.updateRevokeEntryRecord(entry);
        // ---------修改入职记录---------

        // ---------删除结算记录---------
        int settleCount = settleDetailsDAO.existSettleByFollowId(followRevoke.getFollowId());
        if (settleCount > 0) {
            settleDetailsDAO.deleteSettleDetailsByFollowId(followRevoke.getRevokeBy(), timestamp, followRevoke.getFollowId());
        }
        // ---------删除结算记录---------

        return MapUtils.create("no", 1, "msg", "撤销成功");
    }

    /**
     * 撤销结算
     *
     * @param followRevoke
     * @param timestamp
     * @return
     */
    @Transactional
    public Map revokeSettle(FollowRevokeLog followRevoke, Timestamp timestamp) {

        SettleDetails settleDetails = settleDetailsDAO.getSettleDetailsById(followRevoke.getTargetId());

        // 撤销只能撤销当月 往月的按已出账单处理
        if(settleDetails.getActualSettleTime() != null){
            if(DateUtils.isBeforeCurrentMonth(settleDetails.getActualSettleTime())){
                return MapUtils.create("no", 0, "msg", "该节点已产生账单，无法撤销");
            }
        }

        SettleLog settleLog = settleLogDAO.getSettleLogBySettleId(followRevoke.getTargetId());
        if (settleDetails != null && settleDetails.getStatus() != 0) {
            // 撤销时  已结算多个节点，必须按照3 2 1 的顺序撤销，离职按照1 2 3 的顺序撤销。
            int countOtherSettles = settleDetailsDAO.countOtherSettles(settleDetails.getStatus(), followRevoke.getFollowId(), settleDetails.getNode());
            if (countOtherSettles > 0) {
                if (settleDetails.getStatus() == 1) {
                    return MapUtils.create("no", 0, "msg", "请先撤销下一节点。");
                } else if (settleDetails.getStatus() == 2) {
                    return MapUtils.create("no", 0, "msg", "请先撤销上一节点。");
                } else {
                    return MapUtils.create("no", 0, "msg", "数据异常");
                }
            }
        } else {
            return MapUtils.create("no", 0, "msg", "数据异常");
        }

        Integer followCurrentStatus = followUpDAO.getCurrentStatusById(followRevoke.getFollowId());
        if (followCurrentStatus == null) {
            return MapUtils.create("no", 0, "msg", "数据异常");
        }

        followRevoke.setFollowId(followRevoke.getFollowId());
        followRevoke.setType(followRevoke.getType());
        followRevoke.setFollowStatus(followCurrentStatus);
        followRevoke.setStatusBeforeRevocation(settleDetails.getStatus());
        followRevoke.setStatusAfterRevocation(FollowRevokeConstants.SETTLE_DETAILS_DEFAULT);
        followRevoke.setRevokeRemark(followRevoke.getRevokeRemark());
        followRevoke.setRevokeTime(timestamp);
        followRevoke.setNode(settleDetails.getNode());
        followRevokeLogDAO.insertFollowRevokeLog(followRevoke);

        // 是否是最后一个跟进节点 最后一个节点要撤销到已入职
        int unProcessedSettleCount = settleDetailsDAO.countUnProcessedSettle(followRevoke.getFollowId());
        // 所有节点已撤销 修改follow_up主数据
        // ---------增加follow_up---------
        if(unProcessedSettleCount == 1){
            FollowUp followUp = new FollowUp();
            followUp.setId(followRevoke.getFollowId());
            followUp.setStatus(3);
            followUp.setCurrentStatus(FollowRevokeConstants.FOLLOW_LOG_EMPLOYED);
            followUp.setFinish(0);
            followUp.setUpdateBy(followRevoke.getRevokeBy());
            followUp.setUpdateTime(timestamp);
            followUpDAO.updateFollowUp(followUp);
        }
        // ---------增加follow_up---------

        // ---------增加follow_log---------
        FollowLog followLog = new FollowLog();
        followLog.setEnterpriseId(followRevoke.getEnterpriseId());
        followLog.setFollowId(followRevoke.getFollowId());
        followLog.setSettleLogId(settleLog.getId());
        followLog.setType(5);//已撤销
        followLog.setStatus(FollowRevokeConstants.FOLLOW_LOG_REVOKE_SETTLE);
        followLog.setRemark(followRevoke.getRevokeRemark());
        followLog.setBehaviorTime(timestamp);
        followLog.setCreateBy(followRevoke.getRevokeBy());
        followLog.setCreateTime(timestamp);
        followLogDAO.save(followLog);
        // ---------增加follow_log---------

        // 撤销结算/离职信息
        settleDetailsDAO.updateSettleDetailsRevoke(followRevoke.getRevokeBy(), timestamp, followRevoke.getTargetId());

        // 撤销结算离职日志
        settleLogDAO.deleteSettleLog(settleDetails.getId());

        // -------退费-------
        Integer projectId = followUpDAO.getProjectIdById(followRevoke.getFollowId());

        // 合同信息 获取客户id 合同id 付费类型
        JSONObject jsonObjectContract = null;
        Object contractInfoResult = projectService.getContractInfo(projectId);
        JSONObject jsonObjectResult = JSONObject.parseObject(JSON.toJSON(contractInfoResult).toString());
        if (jsonObjectResult.getInteger("no") != null && jsonObjectResult.getInteger("no").equals(200)) {
            jsonObjectContract = jsonObjectResult.getJSONObject("detail");
        }

        // 创建充值记录
        RechargeLog rechargeLog = new RechargeLog();
        rechargeLog.setEnterpriseId(followRevoke.getEnterpriseId());
        rechargeLog.setProjectId(projectId);
//        rechargeLog.setSalary(settleLog.getPayment());
        rechargeLog.setCreateBy(followRevoke.getRevokeBy());
        rechargeLog.setCreateTime(timestamp);
        rechargeLog.setRemark("撤销退款");
        rechargeLog.setTransactionType(RechargeLogTransactionTypeEnum.CANCEL_REFUND.getCode());

        if(jsonObjectContract != null){
            Integer paymentType = jsonObjectContract.getInteger("paymentType");
            rechargeLog.setCustomerId(jsonObjectContract.getInteger("customerId"));
            rechargeLog.setContractId(jsonObjectContract.getInteger("id"));
            if(paymentType != null){
                rechargeLog.setType(paymentType);
                rechargeLog.setSalary(paymentType == 1 ? settleLog.getPayment() : settleLog.getPayment().negate());
            }else{
                rechargeLog.setSalary(settleLog.getPayment());
            }
        }else{
            rechargeLog.setType(1);
            rechargeLog.setSalary(settleLog.getPayment());
        }

        rechargeLogDAO.save(rechargeLog);
        if(rechargeLog.getCustomerId() != null) {
            if (rechargeLog.getType() != null) {
                if (rechargeLog.getType() == 1) {
                    rechargeLogDAO.updataBalance(rechargeLog.getSalary(), rechargeLog.getCustomerId(), timestamp);
                } else {
                    rechargeLogDAO.updataPayment(rechargeLog.getSalary(), rechargeLog.getCustomerId(), timestamp);
                }
            } else {
                rechargeLogDAO.updataBalance(rechargeLog.getSalary(), rechargeLog.getCustomerId(), timestamp);
            }
        }

        // 项目扣款退回
        rechargeLogDAO.updataBalance(settleLog.getPayment(), projectId, timestamp);
        // 提成退回
        commissionGaveLogDAO.deleteCommissionGaveLogBySettleId(followRevoke.getTargetId());

        return MapUtils.create("no", 1, "msg", "撤销成功", "projectId", projectId);
    }
}
