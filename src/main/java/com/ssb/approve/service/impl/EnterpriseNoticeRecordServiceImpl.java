package com.ssb.approve.service.impl;

import com.ssb.approve.dao.EnterpriseNoticeRecordDAO;
import com.ssb.approve.entity.CrmEnterpriseNoticeRecord;
import com.ssb.approve.service.EnterpriseNoticeRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class EnterpriseNoticeRecordServiceImpl implements EnterpriseNoticeRecordService {

    @Autowired
    private EnterpriseNoticeRecordDAO enterpriseNoticeRecordDAO;

    @Override
    public void insertNoticeRecord(List<CrmEnterpriseNoticeRecord> crmEnterpriseNoticeRecords) {
        enterpriseNoticeRecordDAO.batchInsert(crmEnterpriseNoticeRecords);
    }

    @Override
    public void updateNoticeRecord(CrmEnterpriseNoticeRecord record) {
        enterpriseNoticeRecordDAO.updateRecord(record);
    }
}
