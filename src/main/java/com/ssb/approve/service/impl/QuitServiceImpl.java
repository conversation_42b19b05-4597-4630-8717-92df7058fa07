package com.ssb.approve.service.impl;

import com.google.common.collect.Lists;
import com.ssb.approve.dao.*;
import com.ssb.approve.entity.*;
import com.ssb.approve.model.QuitRecordModel;
import com.ssb.approve.model.vo.FollowUpVO;
import com.ssb.approve.service.QuitService;
import com.ssb.approve.utils.MapUtils;
import com.ssb.approve.utils.ThreadPoolManagerUtils;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.util.List;
import java.util.Map;

/**
 * @ClassName: QuitServiceImpl
 * @Description: 离职Service impl
 * @Author: YZK
 * @Date: 2020年02月20日09:44:37
 **/
@Slf4j
@Service
public class QuitServiceImpl implements QuitService {

    @Autowired
    private QuitRecordDAO quitRecordDAO;

    @Autowired
    private FollowUpDAO followUpDAO;

    @Autowired
    private InterviewRecordDAO interviewRecordDAO;

    @Autowired
    private EntryRecordDAO entryRecordDAO;

    @Autowired
    private SettleDetailsDAO settleDetailsDAO;

    @Autowired
    private SettleLogDAO settleLogDAO;

    /**
     * 更新离职招聘跟进
     * 替换对接人
     *
     * @param fromUserId
     * @param toUserId
     * @return
     */
    @Override
    @GlobalTransactional
    @Transactional
    public Map updateQuitFollowUp(Integer eid, Integer fromUserId, Integer toUserId, Integer uid) {

        log.warn("begin crm-approve -- updateQuitFollowUp" + eid + "_" + fromUserId + "_" + toUserId + "_" + uid);

        //当前时间
        Timestamp timestamp = new Timestamp(System.currentTimeMillis());

        //备份离职时的数据   后续依据使用    整体业务考虑  不会很多数据  所以就不采用分页查询   亲，如果后续很慢的话，这里可以优化的哦
        List<FollowUpVO> followUps = followUpDAO.getFollowUpNoFinishByCreateBy(fromUserId, null);

        ThreadPoolManagerUtils.getsInstance().execute(()->{

            //面试记录
            List<InterviewRecord> interviewRecords = Lists.newArrayList();

            //查询面试具体数据
            for (FollowUpVO followUp : followUps) {
                interviewRecords = interviewRecordDAO.getInterviewRecordByCreateBy(followUp.getEnterpriseId(), followUp.getId(), fromUserId);
            }

            //离职 -- 面试记录
            List<InterviewQuitRecord> interviewQuitRecords = Lists.newArrayList();
            InterviewQuitRecord interviewQuitRecord = null;

            //备份数据
            //面试
            for (InterviewRecord interviewRecord : interviewRecords) {
                interviewQuitRecord = new InterviewQuitRecord();
//                interviewQuitRecord.setQuitId(quitRecord.getId());
                interviewQuitRecord.setEnterpriseId(interviewRecord.getEnterpriseId());
                interviewQuitRecord.setFollowId(interviewRecord.getFollowId());
                interviewQuitRecord.setNode(interviewRecord.getNode());
                interviewQuitRecord.setStatus(interviewRecord.getStatus());
                interviewQuitRecord.setAuth(interviewRecord.getAuth());
                interviewQuitRecord.setBehaviorTime(interviewRecord.getBehaviorTime());
                interviewQuitRecord.setRemark(interviewRecord.getRemark());
                interviewQuitRecord.setFailure(interviewRecord.getFailure());
                interviewQuitRecord.setCreateBy(interviewRecord.getCreateBy());
                interviewQuitRecord.setCreateTime(interviewRecord.getCreateTime());
                interviewQuitRecord.setUpdateBy(interviewRecord.getUpdateBy());
                interviewQuitRecord.setUpdateTime(interviewRecord.getUpdateTime());
                interviewQuitRecord.setOperationBy(uid);
                interviewQuitRecord.setOperationTime(timestamp);

                interviewQuitRecords.add(interviewQuitRecord);
            }

            if (interviewQuitRecords.size() > 0) {
                quitRecordDAO.batchSaveInterviewQuitRecord(interviewQuitRecords);
            }
        });

        ThreadPoolManagerUtils.getsInstance().execute(()->{

            //入职记录
            List<EntryRecord> entryRecords = Lists.newArrayList();

            //查询面试具体数据
            for (FollowUpVO followUp : followUps) {
                entryRecords = entryRecordDAO.getEntryRecordByCreateBy(followUp.getEnterpriseId(), followUp.getId(), fromUserId);
            }

            //离职 -- 入职记录
            List<EntryQuitRecord> entryQuitRecords = Lists.newArrayList();
            EntryQuitRecord entryQuitRecord = null;
            for (EntryRecord entryRecord : entryRecords) {
                entryQuitRecord = new EntryQuitRecord();
//                entryQuitRecord.setQuitId(quitRecord.getId());
                entryQuitRecord.setEnterpriseId(entryRecord.getEnterpriseId());
                entryQuitRecord.setFollowId(entryRecord.getFollowId());
                entryQuitRecord.setStatus(entryRecord.getStatus());
                entryQuitRecord.setAuth(entryRecord.getAuth());
                entryQuitRecord.setEstimateTime(entryRecord.getEstimateTime());
                entryQuitRecord.setBehaviorTime(entryRecord.getBehaviorTime());
                entryQuitRecord.setRemark(entryRecord.getRemark());
                entryQuitRecord.setRefuse(entryRecord.getRefuse());
                entryQuitRecord.setCreateBy(entryRecord.getCreateBy());
                entryQuitRecord.setCreateTime(entryRecord.getCreateTime());
                entryQuitRecord.setUpdateBy(entryRecord.getUpdateBy());
                entryQuitRecord.setUpdateTime(entryRecord.getUpdateTime());
                entryQuitRecord.setOperationBy(uid);
                entryQuitRecord.setOperationTime(timestamp);

                entryQuitRecords.add(entryQuitRecord);
            }

            if (entryQuitRecords.size() > 0){
                quitRecordDAO.batchSaveEntryQuitRecord(entryQuitRecords);
            }
        });

        ThreadPoolManagerUtils.getsInstance().execute(()->{

            //结算明细
            List<SettleDetails> settleDetails = Lists.newArrayList();

            //查询面试具体数据
            for (FollowUpVO followUp : followUps) {
                settleDetails = settleDetailsDAO.getSettleDetailsByCreateBy(followUp.getEnterpriseId(), followUp.getId(), fromUserId);
            }

            //离职 -- 结算明细记录
            List<SettleQuitDetails> settleQuitDetails = Lists.newArrayList();
            SettleQuitDetails settleQuitDetail = null;
            for (SettleDetails settleDetail : settleDetails) {
                settleQuitDetail = new SettleQuitDetails();
//                settleQuitDetail.setQuitId(quitRecord.getId());
                settleQuitDetail.setEnterpriseId(settleDetail.getEnterpriseId());
                settleQuitDetail.setFollowId(settleDetail.getFollowId());
                settleQuitDetail.setNode(settleDetail.getNode());
                settleQuitDetail.setEntryTime(settleDetail.getEntryTime());
                settleQuitDetail.setSalary(settleDetail.getSalary());
                settleQuitDetail.setDate(settleDetail.getDate());
                settleQuitDetail.setTime(settleDetail.getTime());
                settleQuitDetail.setSettleTime(settleDetail.getSettleTime());
                settleQuitDetail.setStatus(settleDetail.getStatus());
                settleQuitDetail.setCreateBy(settleDetail.getCreateBy());
                settleQuitDetail.setCreateTime(settleDetail.getCreateTime());
                settleQuitDetail.setUpdateBy(settleDetail.getUpdateBy());
                settleQuitDetail.setUpdateTime(settleDetail.getUpdateTime());
                settleQuitDetail.setDelFlag(settleDetail.getDelFlag());
                settleQuitDetail.setOperationBy(uid);
                settleQuitDetail.setOperationTime(timestamp);

                settleQuitDetails.add(settleQuitDetail);
            }

            if (settleQuitDetails.size() > 0){
                quitRecordDAO.batchSaveSettleQuitDetail(settleQuitDetails);
            }
        });

        ThreadPoolManagerUtils.getsInstance().execute(()->{

            //结算记录
            List<SettleLog> settleLogs = Lists.newArrayList();

            //查询面试具体数据
            for (FollowUpVO followUp : followUps) {
                settleLogs = settleLogDAO.getSettleLogByCreateBy(followUp.getEnterpriseId(), followUp.getId(), fromUserId);
            }

            //离职 -- 结算日志记录
            List<SettleQuitLog> settleQuitLogs = Lists.newArrayList();
            SettleQuitLog settleQuitLog = null;
            for (SettleLog settleLog : settleLogs) {
                settleQuitLog = new SettleQuitLog();
//                settleQuitLog.setQuitId(quitRecord.getId());
                settleQuitLog.setEnterpriseId(settleLog.getEnterpriseId());
                settleQuitLog.setFollowId(settleLog.getFollowId());
                settleQuitLog.setSettleId(settleLog.getSettleId());
                settleQuitLog.setBehaviorTime(settleLog.getBehaviorTime());
                settleQuitLog.setPerformance(settleLog.getPerformance());
                settleQuitLog.setPayment(settleLog.getPayment());
                settleQuitLog.setCreateBy(settleLog.getCreateBy());
                settleQuitLog.setCreateTime(settleLog.getCreateTime());
                settleQuitLog.setDelFlag(settleLog.getDelFlag());
                settleQuitLog.setRemark(settleLog.getRemark());
                settleQuitLog.setOperationBy(uid);
                settleQuitLog.setOperationTime(timestamp);

                settleQuitLogs.add(settleQuitLog);
            }

            if (settleQuitLogs.size() > 0){
                quitRecordDAO.batchSaveSettleQuitLog(settleQuitLogs);
            }
        });

        ThreadPoolManagerUtils.getsInstance().execute(() -> {

            //离职 -- 跟进记录
            List<FollowUpQuitRecord> followUpQuitRecords = Lists.newArrayList();
            FollowUpQuitRecord followUpQuitRecord = null;

            //跟进记录id
            List<Integer> followIds = Lists.newArrayList();
            for (FollowUpVO followUp : followUps) {
                followUpQuitRecord = new FollowUpQuitRecord();
//                followUpQuitRecord.setQuitId(quitRecord.getId());
                followUpQuitRecord.setFollowId(followUp.getId());
                followUpQuitRecord.setEnterpriseId(followUp.getEnterpriseId());
                followUpQuitRecord.setContactId(followUp.getContactId());
                followUpQuitRecord.setProjectId(followUp.getProjectId());
                followUpQuitRecord.setStatus(followUp.getStatus());
                followUpQuitRecord.setCurrentStatus(followUp.getCurrentStatus());
                followUpQuitRecord.setFinish(followUp.getFinish());
                followUpQuitRecord.setCreateBy(followUp.getCreateBy());
                followUpQuitRecord.setCreateTime(followUp.getCreateTime());
                followUpQuitRecord.setUpdateBy(followUp.getUpdateBy());
                followUpQuitRecord.setUpdateTime(followUp.getUpdateTime());
                followUpQuitRecord.setDelFlag(followUp.getDelFlag());
                followUpQuitRecord.setRemark(followUp.getRemark());
                followUpQuitRecord.setOperationBy(uid);
                followUpQuitRecord.setOperationTime(timestamp);

                followUpQuitRecords.add(followUpQuitRecord);
                followIds.add(followUp.getId());
            }

            //批量保存
            if (followUpQuitRecords.size() > 0) {
                quitRecordDAO.batchSaveFollowUpQuitRecord(followUpQuitRecords);
            }

            //更新交接人
            QuitRecordModel quitRecordModel = new QuitRecordModel();
            quitRecordModel.setFromUserId(fromUserId);
            quitRecordModel.setToUserId(toUserId);
            quitRecordModel.setUpdateTime(timestamp);
            quitRecordModel.setEnterpriseId(eid);
            quitRecordModel.setFollowIds(followIds);

            //存在 更新对应的 流程数据
            if (followIds.size() > 0) {
                followUpDAO.quitUpdateFollowUpCreateBy(quitRecordModel);
                interviewRecordDAO.quitUpdateInterviewRecordCreateBy(quitRecordModel);
                entryRecordDAO.quitUpdateEntryRecordCreateBy(quitRecordModel);
                settleDetailsDAO.quitUpdateSettleDetailsCreateBy(quitRecordModel);
                settleLogDAO.quitUpdateSettleLogCreateBy(quitRecordModel);
            }
        });

        //更新离职状态  为完成态
        QuitRecord quitRecordUpdate = new QuitRecord();
//        quitRecordUpdate.setId(quitRecord.getId());
        quitRecordUpdate.setStatus(1);
        quitRecordUpdate.setUpdateTime(timestamp);
        quitRecordDAO.updateQuitRecord(quitRecordUpdate);

        log.warn("end success crm-approve -- updateQuitFollowUp" + eid + "_" + fromUserId + "_" + toUserId + "_" + uid);

        return MapUtils.create("no", 200, "msg", "离职交接完成");
    }
}
