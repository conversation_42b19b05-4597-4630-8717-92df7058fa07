package com.ssb.approve.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.ssb.approve.dao.CommissionStrategyDAO;
import com.ssb.approve.dao.CommissionStrategyRatioDAO;
import com.ssb.approve.dao.FollowCommissionDAO;
import com.ssb.approve.entity.CommissionStrategyRatio;
import com.ssb.approve.entity.FollowCommission;
import com.ssb.approve.model.vo.CommissionStrategyVO;
import com.ssb.approve.service.FollowCommissionService;
import com.ssb.approve.service.client.ProjectService;
import com.ssb.approve.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class FollowCommissionServiceImpl implements FollowCommissionService {

    @Autowired
    private FollowCommissionDAO followCommissionDAO;


    /**
     * 跟进 提成
     *
     * @param followId  跟进id
     * @param projectId 项目id
     */
    @Async
    @Override
    public void saveFollowCommission(Integer followId, Integer projectId, JSONObject jsonObjectResult) {
        // 跟进提成
        FollowCommission followCommission = new FollowCommission();
        // 提成比例占比
        Map<String, Object> rateContentMap = Maps.newHashMap();

        //获取项目开始时间以及当前结算节点
        //获取项目详情信息
        //判断返回参数是否正常
        // 经产品确认  锁定第一期合同开始时间以及当前一期的合同结束时间
        if (jsonObjectResult.getInteger("no") != null && jsonObjectResult.getInteger("no").equals(200)) {
            JSONObject jsonObject = jsonObjectResult.getJSONObject("obj");
            //结算节点
            String peojectSettles = jsonObject.getString("projectSettles");
            followCommission.setSettlementContent(peojectSettles);

            followCommission.setContractStartTime(jsonObject.getTimestamp("contractStartTime"));

            Timestamp contractEndTime = jsonObject.getTimestamp("contractEndTime");
            followCommission.setContractEndTime(contractEndTime);
        }

        //判断返回参数是否正常
        if (jsonObjectResult.getInteger("no") != null && jsonObjectResult.getInteger("no").equals(200)) {
            //正常保存逻辑
            JSONObject jsonObject = jsonObjectResult.getJSONObject("obj");
            if (jsonObject != null) {
                followCommission.setClassifyId(0);
                followCommission.setIndustryFirstId(jsonObject.getInteger("industryFirst"));
                followCommission.setIndustrySecondId(jsonObject.getInteger("industrySecond"));
                followCommission.setPositionFirstId(jsonObject.getInteger("position2First"));
                followCommission.setPositionSecondId(jsonObject.getInteger("position2Second"));
                followCommission.setStrategyId(0);
                followCommission.setDifficulty(2);
            }
        } else {
            log.error("saveFollowCommission 提成结算 调用project失败 请尽快排查");
            return;
        }

        followCommission.setFollowId(followId);
        followCommission.setRateContent(JSON.toJSONString(rateContentMap));

        followCommissionDAO.save(followCommission);
    }

    @Override
    public void savePartTimeJobFollowCommission(Integer followId, JSONObject jsonObjectResult, JSONObject userJsonObject) {
        // 跟进提成
        FollowCommission followCommission = new FollowCommission();

        Map<String, Object> rateContentMap = Maps.newHashMap();
        Map<String, Object> headhunterMap = Maps.newHashMap();

        //获取项目开始时间以及当前结算节点
        //获取项目详情信息
        JSONObject jsonObject = jsonObjectResult.getJSONObject("obj");
        //结算节点
        String projectSettles = jsonObject.getString("projectSettles");
        followCommission.setSettlementContent(projectSettles);

        // 合同开始时间
        followCommission.setContractStartTime(jsonObject.getTimestamp("contractStartTime"));
        followCommission.setContractEndTime(jsonObject.getTimestamp("contractEndTime"));
        followCommission.setStrategyId(jsonObject.getInteger("strategyId"));
        followCommission.setClassifyId(0);
        followCommission.setIndustryFirstId(jsonObject.getInteger("industryFirst"));
        followCommission.setIndustrySecondId(jsonObject.getInteger("industrySecond"));
        followCommission.setPositionFirstId(jsonObject.getInteger("position2First"));
        followCommission.setPositionSecondId(jsonObject.getInteger("position2Second"));
        followCommission.setDifficulty(2);
        followCommission.setFollowId(followId);

        // 用户提成比例 项目提成比例  项目提成比例优先
        headhunterMap.put("userCommissionRatio", userJsonObject.getInteger("commissionRatio") == 0 ? 0 : Double.toString((double) userJsonObject.getInteger("commissionRatio") / 100));
        headhunterMap.put("projectCommissionRatio", jsonObject.getInteger("partTimeJobCommissionRatio") == 0 ? 0 : Double.toString((double) jsonObject.getInteger("partTimeJobCommissionRatio") / 100));
        rateContentMap.put("headhunterMap", headhunterMap);
        followCommission.setRateContent(JSON.toJSONString(rateContentMap));

        followCommissionDAO.save(followCommission);
    }
}