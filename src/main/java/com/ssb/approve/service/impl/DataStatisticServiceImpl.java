package com.ssb.approve.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.ssb.approve.common.constants.FollowUpConstants;
import com.ssb.approve.dao.*;
import com.ssb.approve.entity.CheckSlip;
import com.ssb.approve.model.*;
import com.ssb.approve.model.dto.*;
import com.ssb.approve.model.vo.*;
import com.ssb.approve.service.CommonService;
import com.ssb.approve.service.DataStatisticService;
import com.ssb.approve.service.PerformanceTargetService;
import com.ssb.approve.service.client.ProjectService;
import com.ssb.approve.service.client.ResumeService;
import com.ssb.approve.service.client.UserService;
import com.ssb.approve.utils.ExportExcelWorker;
import com.ssb.approve.utils.MapUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName: DataStatisticServiceImpl
 * @Description: 数据统计 跟进 impl
 * @Author: YZK
 * @Date: 2020年9月2日10:15:26
 **/
@Slf4j
@Service
public class DataStatisticServiceImpl implements DataStatisticService {

    private static final long YEAR_TIME_STAMP = 31536000; //一年时间戳 秒

    @Autowired
    private DataStatisticDAO dataStatisticDAO;

    @Autowired
    private InterviewRecordDAO interviewRecordDAO;

    @Autowired
    private EntryRecordDAO entryRecordDAO;

    @Autowired
    private SettleDetailsDAO settleDetailsDAO;

    @Autowired
    private CommissionGaveLogDAO commissionGaveLogDAO;

    @Autowired
    private FollowUpDAO followUpDAO;

    @Autowired
    private UserService userService;

    @Autowired
    private ProjectService projectService;

    @Value("${config.temFilePath}")
    private String temFilePath;

    @Autowired
    private FollowRevokeLogDAO followRevokeLogDAO;

    @Autowired
    private CommonService commonService;

    @Autowired
    private UserEverydayWorkloadDAO userEverydayWorkloadDAO;

    @Autowired
    private ResumeService resumeService;

    @Autowired
    private PerformanceTargetDAO performanceTargetDAO;

    @Autowired
    private FollowRecommendDAO followRecommendDAO;

    private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final DateTimeFormatter displayFormatter = DateTimeFormatter.ofPattern("MM-dd");

    /**
     * 团队项目跟进数据
     *
     * @param queryFollowUpModel
     * @return
     */
    @Override
    public Map getTeamFollowUpData(QueryFollowUpModel queryFollowUpModel) {
        // 计算页码开始位置
        Integer pageBegin = (queryFollowUpModel.getPageNum() - 1) * queryFollowUpModel.getPageSize();
        queryFollowUpModel.setPageBegin(pageBegin);
        // 判断 当前身份   -1 是 成员  只查看自己
        if (queryFollowUpModel.getDeptIds().contains(-1)) {
            queryFollowUpModel.setDeptIds(Sets.newHashSet());
        }

        List<TeamFollowUpDataVO> teamFollowUpDataVOS = dataStatisticDAO.getTeamFollowUpData(queryFollowUpModel);
        int count = dataStatisticDAO.getTeamFollowUpDataCount(queryFollowUpModel);
        TeamFollowUpDataVO teamFollowUpDataTotal = dataStatisticDAO.getTeamFollowUpDataTotal(queryFollowUpModel);

        return MapUtils.create("no", 1, "msg", "成功", "results", teamFollowUpDataVOS, "count", count, "total", teamFollowUpDataTotal);
    }

    /**
     * 个人跟进数据
     *
     * @param queryFollowUpModel
     * @return
     */
    @Override
    public Map getIndividualData(QueryFollowUpModel queryFollowUpModel) {
        //查询跟进人
        TeamFollowUpDataVO teamFollowUpDataTotal = dataStatisticDAO.getIndividualDataTotal(queryFollowUpModel);
        return MapUtils.create("no", 1, "msg", "成功", "results", teamFollowUpDataTotal);
    }

    /**
     * 个人数据 邀约量
     *
     * @param queryFollowUpModel
     * @return
     */
    @Override
    public Map getIndividualInviteData(QueryFollowUpModel queryFollowUpModel) {
        Integer pageBegin = (queryFollowUpModel.getPageNum() - 1) * queryFollowUpModel.getPageSize();
        queryFollowUpModel.setPageBegin(pageBegin);
        List<InterviewRecordVO> interviewRecord = interviewRecordDAO.getIndividualInviteData(queryFollowUpModel);
        int count = interviewRecordDAO.getIndividualInviteDataCount(queryFollowUpModel);
        return MapUtils.create("no", 1, "msg", "成功", "results", interviewRecord, "count", count);
    }

    /**
     * 个人数据 面试量
     *
     * @param queryFollowUpModel
     * @return
     */
    @Override
    public Map getIndividualInterviewData(QueryFollowUpModel queryFollowUpModel) {
        Integer pageBegin = (queryFollowUpModel.getPageNum() - 1) * queryFollowUpModel.getPageSize();
        queryFollowUpModel.setPageBegin(pageBegin);
        List<InterviewRecordVO> interviewRecord = interviewRecordDAO.getIndividualInterviewData(queryFollowUpModel);
        int count = interviewRecordDAO.getIndividualInterviewDataCount(queryFollowUpModel);
        return MapUtils.create("no", 1, "msg", "成功", "results", interviewRecord, "count", count);
    }

    /**
     * 个人数据 offer量
     *
     * @param queryFollowUpModel
     * @return
     */
    @Override
    public Map getIndividualOfferData(QueryFollowUpModel queryFollowUpModel) {
        Integer pageBegin = (queryFollowUpModel.getPageNum() - 1) * queryFollowUpModel.getPageSize();
        queryFollowUpModel.setPageBegin(pageBegin);
        List<InterviewRecordVO> interviewRecord = interviewRecordDAO.getIndividualOfferData(queryFollowUpModel);
        int count = interviewRecordDAO.getIndividualOfferDataCount(queryFollowUpModel);
        return MapUtils.create("no", 1, "msg", "成功", "results", interviewRecord, "count", count);
    }

    /**
     * 个人数据 入职量
     *
     * @param queryFollowUpModel
     * @return
     */
    @Override
    public Map getIndividualEntryData(QueryFollowUpModel queryFollowUpModel) {
        Integer pageBegin = (queryFollowUpModel.getPageNum() - 1) * queryFollowUpModel.getPageSize();
        queryFollowUpModel.setPageBegin(pageBegin);
        List<EntryRecordVO> interviewRecord = entryRecordDAO.getIndividualEntryData(queryFollowUpModel);
        int count = entryRecordDAO.getIndividualEntryDataCount(queryFollowUpModel);
        return MapUtils.create("no", 1, "msg", "成功", "results", interviewRecord, "count", count);
    }

    /**
     * 个人数据 结算量
     *
     * @param queryFollowUpModel
     * @return
     */
    @Override
    public Map getIndividualSettleData(QueryFollowUpModel queryFollowUpModel) {
        Integer pageBegin = (queryFollowUpModel.getPageNum() - 1) * queryFollowUpModel.getPageSize();
        queryFollowUpModel.setPageBegin(pageBegin);
        List<SettleDetailsVO> interviewRecord = settleDetailsDAO.getIndividualSettleData(queryFollowUpModel);
        int count = settleDetailsDAO.getIndividualSettleDataCount(queryFollowUpModel);
        return MapUtils.create("no", 1, "msg", "成功", "results", interviewRecord, "count", count);
    }

    public List<Integer> getUserIds(Integer userId, Integer enterpriseId) {
        Object result = userService.getOrganizationUserId(userId, enterpriseId);
        JSONObject jsonObjectResult = JSONObject.parseObject(JSON.toJSON(result).toString());
        //判断返回参数是否正常
        if (jsonObjectResult.getInteger("no") != null && jsonObjectResult.getInteger("no").equals(200)) {

            List<Integer> userIdList = (List) jsonObjectResult.get("userIds");
            userIdList.add(userId);
            return userIdList;
        }
        return Collections.emptyList();
    }

    //销售工作台
    @Override
    public Map getVertrieb(MarketWorkModel mk) {
        mk.setOffset((mk.getPage() - 1) * mk.getPageSize());
        Map result = Maps.newHashMap();
        int resortProject = dataStatisticDAO.getResortProject(mk.getUid()); //在招项目数量
        result.put("resortProject", resortProject);
        int forInterviewCount = dataStatisticDAO.getForInterviewCount(mk);//待面试数量
        result.put("forInterviewCount", forInterviewCount);
        int awaitEntyCount = dataStatisticDAO.getAwaitEntyCount(mk);//待入职数量
        result.put("awaitEntyCount", awaitEntyCount);
        int forTheCount = dataStatisticDAO.getForTheCount(mk);//待结算
        result.put("forTheCount", forTheCount);
        int nodeCount = dataStatisticDAO.getNodeCount(mk);//节点
        result.put("nodeCount", nodeCount);
        if (mk.getStatus().equals(1)) {//待面试
            List<WorkWaiting> forInterview = dataStatisticDAO.getForInterview(mk);
            result.put("list", forInterview);
        }
        if (mk.getStatus().equals(2)) {//待入职
            List<WorkWaiting> awaitEnty = dataStatisticDAO.getAwaitEnty(mk);
            result.put("list", awaitEnty);
        }
        if (mk.getStatus().equals(3)) {//待结算
            List<WorkWaiting> node = dataStatisticDAO.getNode(mk);
            result.put("list", node);
        }
        return MapUtils.create("no", 1, "msg", "获取成功", "market", result);
    }

    //核对单
    @Override
    public Map getCheckSlip(CheckSlipModel cs) {
        cs.setPageBegin((cs.getPageNum() - 1) * cs.getPageSize());
        //数据权限
        Set<String> collect = Sets.newHashSet();
        Object o = userService.getUserRoleMenuDetailsForServerV2(cs.getUserId());
        JSONObject jsonObject = JSONObject.parseObject(JSON.toJSON(o).toString());
        if (null != jsonObject.getInteger("no") && jsonObject.getInteger("no").equals(1)) {
            JSONArray objects = jsonObject.getJSONArray("details");
            List<MenuDetailVO> menuDetailVOS = JSONArray.parseArray(JSONArray.toJSONString(objects), MenuDetailVO.class);
            collect = menuDetailVOS.stream().map(MenuDetailVO::getPermission).collect(Collectors.toSet());
        }

        Object userInfo = userService.getUserInfo(cs.getUserId(), cs.getEnterpriseId(), 0);
        JSONObject result = JSONObject.parseObject(JSON.toJSON(userInfo).toString());

        JSONArray userIdList = result.getJSONArray("userIdList");

        List<Integer> integerList = new ArrayList<>();
        for (int i = 0; i < userIdList.size(); i++) {
            integerList.add(userIdList.getInteger(i));
        }
        cs.setUserIds(integerList);

        if (collect.contains("check_all")) {
            cs.setRange(0);
        } else if (collect.contains("check_person")) {
            cs.setRange(1);
        }

        if (cs.getRange() != null) {
            List<CheckSlip> checkSlip = dataStatisticDAO.getCheckSlip(cs);
            int checkSlipCount = dataStatisticDAO.getCheckSlipCount(cs);
            BigDecimal totalAmount = dataStatisticDAO.getSumCheckSlip(cs);
            return MapUtils.create("no", 1, "msg", "获取成功", "checkSlipCount", checkSlipCount, "checkSlip", checkSlip, "totalAmount", totalAmount);
        }
        return MapUtils.create("no", 1, "details", Collections.emptyList(), "count", 0);
    }


    //核对单明细
    @Override
    public Map getCheckSlipDetail(CheckSlipModel cs) {
        cs.setPageBegin((cs.getPageNum() - 1) * cs.getPageSize());
        //数据权限
        Set<String> collect = Sets.newHashSet();
        Object o = userService.getUserRoleMenuDetailsForServerV2(cs.getUserId());
        JSONObject jsonObject = JSONObject.parseObject(JSON.toJSON(o).toString());
        if (null != jsonObject.getInteger("no") && jsonObject.getInteger("no").equals(1)) {
            JSONArray objects = jsonObject.getJSONArray("details");
            List<MenuDetailVO> menuDetailVOS = JSONArray.parseArray(JSONArray.toJSONString(objects), MenuDetailVO.class);
            collect = menuDetailVOS.stream().map(MenuDetailVO::getPermission).collect(Collectors.toSet());
        }

        Object userInfo = userService.getUserInfo(cs.getUserId(), cs.getEnterpriseId(), 0);
        JSONObject result = JSONObject.parseObject(JSON.toJSON(userInfo).toString());

        JSONArray userIdList = result.getJSONArray("userIdList");

        List<Integer> integerList = new ArrayList<>();
        for (int i = 0; i < userIdList.size(); i++) {
            integerList.add(userIdList.getInteger(i));
        }
        cs.setUserIds(integerList);

        if (collect.contains("check_all")) {
            cs.setRange(0);
        } else if (collect.contains("check_person")) {
            cs.setRange(1);
        }
        if (cs.getRange() != null) {
            List<CheckSlip> checkSlip = dataStatisticDAO.getCheckSlipDetail(cs);
            int checkSlipCount = dataStatisticDAO.getCheckSlipDetailCount(cs);
            return MapUtils.create("no", 1, "msg", "获取成功", "checkSlipCount", checkSlipCount, "checkSlip", checkSlip);
        }
        return MapUtils.create("no", 1, "checkSlip", Collections.emptyList(), "checkSlipCount", 0);
    }

    //核对单明细导出
    @Override
    public String exportExpectedCheckSlip(CheckSlipModel cs) throws ParseException {
        List<CheckSlip> list = dataStatisticDAO.getCheckSlipDetail(cs);
        String strDateFormatDay = "yyyy-MM-dd HH:mm:ss";
        String strDateFormatDay1 = "yyyy-MM-dd";
        String entryTime = null, settleTime = null;
        SimpleDateFormat sdfDay = new SimpleDateFormat(strDateFormatDay);
        SimpleDateFormat entryDay = new SimpleDateFormat(strDateFormatDay1);
        String[][] rws = new String[list.size()][];
        for (int i = 0; i < rws.length; i++) {
            CheckSlip checkSlip = list.get(i);
            if (checkSlip.getEntryTime() == null) {
                entryTime = "-";
            } else {
                entryTime = entryDay.format(checkSlip.getEntryTime());
            }
            if (checkSlip.getSettleTime() == null) {
                settleTime = "-";
            } else {
                settleTime = sdfDay.format(checkSlip.getSettleTime());
            }
            String[] rs = new String[]{
                    checkSlip.getEnterpriseName(), checkSlip.getProjectName(), checkSlip.getContactName(),
                    checkSlip.getContactPhone(), entryTime, settleTime, String.valueOf(checkSlip.getSalary()),
                    String.valueOf(checkSlip.getNode())
            };
            rws[i] = rs;
        }
        ExportExcelWorker exportExcelWorker = new ExportExcelWorker("temFilePath");
        return exportExcelWorker.WorkbookCheckSlip(new String[]{
                "公司名称", "项目名称", "求职者姓名", "电话", "入职时间",
                "结算时间", "计费金额", "结算节点"}, rws);
    }

    /**
     * 处理预计提成数据
     *
     * @param list
     */
    public List<EstimatedCommissionStatisticVO> handleExpectedCommissionReportData(List<EstimatedCommissionStatisticVO> list) {

        List<EstimatedCommissionStatisticVO> processedList = Lists.newArrayList();

        for (EstimatedCommissionStatisticVO estimatedCommissionStatistic : list) {

            EstimatedCommissionStatisticVO headhunter = new EstimatedCommissionStatisticVO();

            //猎头信息
            BeanUtils.copyProperties(estimatedCommissionStatistic, headhunter);

            // 积分总计
            BigDecimal headhunterExpectIntegralTotal = BigDecimal.ZERO;

            //获取跟进创建时的 项目节点信息 为空则取项目当前节点信息
            JSONArray jsonProjectSettleArray = null;
            if (estimatedCommissionStatistic.getSettlementContent() != null) {
                jsonProjectSettleArray = JSONArray.parseArray(estimatedCommissionStatistic.getSettlementContent());
            } else {
                Object result = projectService.getSettleDetails(estimatedCommissionStatistic.getProjectId());
                JSONObject jsonObjectResult = JSONObject.parseObject(JSON.toJSON(result).toString());
                //判断返回参数是否正常
                if (jsonObjectResult.getInteger("no") != null && jsonObjectResult.getInteger("no").equals(200)) {
                    jsonProjectSettleArray = jsonObjectResult.getJSONArray("obj");
                }
            }

            if (jsonProjectSettleArray != null) {
                JSONObject settleDetailsObject = null;
                Integer date, node; //天数  节点 金额
                BigDecimal salary = BigDecimal.ZERO; // 金额
                Map<String, Object> headhunterExpectCommissionDetail;
                BigDecimal headhunterSalary;
                BigDecimal headhunterIntegral;
                for (int i = 0; i < jsonProjectSettleArray.size(); i++) {
                    settleDetailsObject = jsonProjectSettleArray.getJSONObject(i);
                    date = settleDetailsObject.getInteger("date");
                    node = settleDetailsObject.getInteger("node");
                    salary = settleDetailsObject.getBigDecimal("salary");

                    //猎头预计提成 积分
                    //销售离职情况 提成为0
                    if (estimatedCommissionStatistic.getHeadhunterStatus() != null && estimatedCommissionStatistic.getHeadhunterStatus() == 1) {
                        headhunterIntegral = BigDecimal.ZERO;
                    } else {
                        headhunterIntegral = salary;
                    }

                    headhunterExpectCommissionDetail = Maps.newHashMap();
                    headhunterExpectCommissionDetail.put("data", date);
                    headhunterExpectCommissionDetail.put("node", node);
                    headhunterExpectCommissionDetail.put("integral", headhunterIntegral);

                    //累计猎头提成积分总数
                    headhunterExpectIntegralTotal = headhunterExpectIntegralTotal.add(headhunterIntegral);
                }

                headhunter.setCommissionUserId(estimatedCommissionStatistic.getHeadhunterId());
                headhunter.setDeptId(estimatedCommissionStatistic.getHeadhunterDeptId());
                headhunter.setIntegral(headhunterExpectIntegralTotal);
                processedList.add(headhunter);

            }
        }
        return processedList;
    }

    /**
     * 已撤销列表
     *
     * @param model
     * @return
     */
    @Override
    public Map getRevokeList(QueryFollowRevokeStatisticsModel model) {
        Integer pageBegin = (model.getPageNum() - 1) * model.getPageSize();
        model.setPageBegin(pageBegin);

        List<FollowRevokeStatisticsVO> list = followRevokeLogDAO.getFollowRevokeStatisticsList(model);
        int count = followRevokeLogDAO.getFollowRevokeStatisticsCount(model);

        return MapUtils.create("no", 1, "details", list, "count", count);
    }

    @Override
    public Map card(Integer userId, Integer enterpriseId) {

        CardUserVO cardUserVO = commonService.getCardUserInfo(userId, enterpriseId, 0, 1);
        CardVO card = new CardVO();
        BeanUtils.copyProperties(cardUserVO, card);
        BigDecimal performance = commissionGaveLogDAO.getPerformanceByUserIdList(card);
        if (card.getType() == 3) {
            Object countObject = projectService.getCustomerCountAndProjectCount(enterpriseId);
            JSONObject countJSON = JSONObject.parseObject(JSON.toJSON(countObject).toString());
            card.setProjectCount(countJSON.getInteger("projectCount"));
            card.setCustomerCount(countJSON.getInteger("customerCount"));
        }

        if (cardUserVO.getType() == 1) {
            card.setPerformance(performance);
        } else {
            // 获取接取了当前公司分享项目的项目金额
            BigDecimal shareProjectPerformance = commissionGaveLogDAO.getShareProjectPerformance(enterpriseId, card.getUserIdList());
            card.setPerformance(performance.add(shareProjectPerformance));
        }

        return MapUtils.create("no", 1, "detail", card);
    }

    @Override
    public Map getTheCharts(TheChartsDTO dto) {
        if (dto.getType() == 2) {
            if (dto.getPageNum() == null || dto.getPageNum() < 1) {
                dto.setPageNum(1);
            }
            Integer pageBegin = (dto.getPageNum() - 1) * dto.getPageSize();
            dto.setPageBegin(pageBegin);
        }

        List<TheChartsVO> list = commissionGaveLogDAO.getTheCharts(dto);
        int count = commissionGaveLogDAO.getTheChartsCount(dto);

        return MapUtils.create("no", 1, "list", list, "count", count);
    }

    @Override
    public Map getDailyData(DailyDataDTO dto) {
        List<Integer> userIdList = new ArrayList<>();
        if (dto.getType() == 1) {
            userIdList.add(dto.getUserId());
        } else if (dto.getType() == 2) {
            CardUserVO cardUserVO = commonService.getCardUserInfo(dto.getUserId(), dto.getEnterpriseId(), dto.getDeptId(), dto.getType());
            userIdList = cardUserVO.getUserIdList();
        }
        if(CollectionUtils.isEmpty(userIdList)){
            return MapUtils.create("no", 1,
                    "callCount", 0,
                    "yesterdayCallCount", 0,
                    "wechatCount", 0,
                    "yesterdayWechatCount", 0,
                    "resumeCount", 0,
                    "yesterdayResumeCount", 0,
                    "contractCount", 0,
                    "yesterdayContractCount", 0);
        }
        dto.setUserIdList(userIdList);
        UserEverydayWorkloadVO vo = userEverydayWorkloadDAO.getDailyData(dto.getUserIdList(), 1);

        Object dailyDataResumeCountObject = resumeService.getDailyDataResumeCount(dto);
        JSONObject dailyDataResumeCountJSON = JSONObject.parseObject(JSON.toJSON(dailyDataResumeCountObject).toString());
        if (!(dailyDataResumeCountJSON.getInteger("no") != null && dailyDataResumeCountJSON.getInteger("no").equals(200))) {
            return MapUtils.create("no", 0, "msg", "数据异常，请稍后重试！");
        }

        int resumeCount = dailyDataResumeCountJSON.getInteger("count");

        UserEverydayWorkloadVO yesterdayVO = userEverydayWorkloadDAO.getDailyData(dto.getUserIdList(), 2);
        int resumeYesterdayCount = dailyDataResumeCountJSON.getInteger("yesterdayCount");

        return MapUtils.create("no", 1,
                "callCount", vo.getCallCount(),
                "yesterdayCallCount", yesterdayVO.getCallCount(),
                "wechatCount", vo.getWechatCount(),
                "yesterdayWechatCount", yesterdayVO.getWechatCount(),
                "resumeCount", resumeCount,
                "yesterdayResumeCount", resumeYesterdayCount,
                "contractCount", vo.getContractCount(),
                "yesterdayContractCount", yesterdayVO.getContractCount());
    }

    @Override
    public Map getRecruitmentData(RecruitmentDataDTO dto) {

        CardUserVO cardUserVO = commonService.getCardUserInfo(dto.getUserId(), dto.getEnterpriseId(), dto.getDeptId(), dto.getType());
        if (cardUserVO.getType() == 2 || cardUserVO.getType() == 3) {
            if(CollectionUtils.isEmpty(cardUserVO.getUserIdList())){
                return MapUtils.create("no", 1,
                        "invitationForInterviewDataList", completeDates(Collections.emptyList(), dto.getTimeStart(), dto.getTimeEnd()),
                        "interviewDataList", completeDates(Collections.emptyList(), dto.getTimeStart(), dto.getTimeEnd()),
                        "entryDataList", completeDates(Collections.emptyList(), dto.getTimeStart(), dto.getTimeEnd()),
                        "settleDataList", completeDates(Collections.emptyList(), dto.getTimeStart(), dto.getTimeEnd()),
                        "contractCountList", completeDates(Collections.emptyList(), dto.getTimeStart(), dto.getTimeEnd()),
                        "wechatCountList", completeDates(Collections.emptyList(), dto.getTimeStart(), dto.getTimeEnd()),
                        "callCountList", completeDates(Collections.emptyList(), dto.getTimeStart(), dto.getTimeEnd()),
                        "resumeCountList", completeDates(Collections.emptyList(), dto.getTimeStart(), dto.getTimeEnd())
                );
            }
            dto.setUserIdList(cardUserVO.getUserIdList());
        }

        List<StatisticalChartDataVO> invitationForInterviewDataList = interviewRecordDAO.getInvitationForInterviewData(dto);
        List<StatisticalChartDataVO> interviewDataList = interviewRecordDAO.getInterviewData(dto);
        List<StatisticalChartDataVO> entryDataList = entryRecordDAO.getEntryData(dto);
        List<StatisticalChartDataVO> settleDataList = followUpDAO.getSettleData(dto);

        Object dataBoardWorkLoadObject  = userService.getDataBoardWorkLoad(dto);
        JSONObject dataBoardWorkLoadJSONObject = JSONObject.parseObject(JSON.toJSON(dataBoardWorkLoadObject).toString());
        Map<String, List<StatisticalChartDataVO>> map = convertToStatisticalChartDataLists(dataBoardWorkLoadJSONObject);
        List<StatisticalChartDataVO> contractCountList = map.get("contractCountList");
        List<StatisticalChartDataVO> wechatCountList = map.get("wechatCountList");
        List<StatisticalChartDataVO> callCountList = map.get("callCountList");
        List<StatisticalChartDataVO> resumeCountList = map.get("resumeCountList");

        return MapUtils.create("no", 1,
                "invitationForInterviewDataList", completeDates(invitationForInterviewDataList, dto.getTimeStart(), dto.getTimeEnd()),
                "interviewDataList", completeDates(interviewDataList, dto.getTimeStart(), dto.getTimeEnd()),
                "entryDataList", completeDates(entryDataList, dto.getTimeStart(), dto.getTimeEnd()),
                "settleDataList", completeDates(settleDataList, dto.getTimeStart(), dto.getTimeEnd()),
                "contractCountList", completeDates(contractCountList, dto.getTimeStart(), dto.getTimeEnd()),
                "wechatCountList", completeDates(wechatCountList, dto.getTimeStart(), dto.getTimeEnd()),
                "callCountList", completeDates(callCountList, dto.getTimeStart(), dto.getTimeEnd()),
                "resumeCountList", completeDates(resumeCountList, dto.getTimeStart(), dto.getTimeEnd())
                );
    }

    /**
     * 补全日期数据并格式化为MM-dd格式
     * @param data 原始数据
     * @param startTime 开始时间
     * @param endTime 结束时间 (如果为null则使用startTime所在月份的最后一天)
     * @return 补全并格式化后的数据
     */
    public List<StatisticalChartDataVO> completeDates(List<StatisticalChartDataVO> data, Timestamp startTime, Timestamp endTime) {
        // 如果data和startTime都为空，返回空列表
        if ((data == null || data.isEmpty()) && startTime == null) {
            return new ArrayList<>();
        }

        // 确定起止日期
        LocalDate start, end;
        
        if (startTime != null) {
            start = startTime.toLocalDateTime().toLocalDate();
            
            if (endTime != null) {
                end = endTime.toLocalDateTime().toLocalDate();
            } else {
                // 如果没有提供endTime，则使用startTime所在月份的最后一天
                end = start.withDayOfMonth(start.lengthOfMonth());
            }
        } else {
            // 如果没有startTime但有data，则使用data中第一个日期
            LocalDate firstDate = LocalDate.parse(data.get(0).getDate(), formatter);
            start = firstDate.withDayOfMonth(1);
            end = firstDate.withDayOfMonth(firstDate.lengthOfMonth());
        }

        // 生成从start到end的所有日期的数据，初始count为0
        List<StatisticalChartDataVO> fullDateData = new ArrayList<>();
        for (LocalDate d = start; !d.isAfter(end); d = d.plusDays(1)) {
            StatisticalChartDataVO vo = new StatisticalChartDataVO();
            vo.setDate(d.format(displayFormatter));  // 使用MM-dd格式
            vo.setCount(0);
            fullDateData.add(vo);
        }

        // 如果data不为空，将已有数据合并到生成的数据中
        if (data != null && !data.isEmpty()) {
            for (StatisticalChartDataVO vo : data) {
                try {
                    // 尝试解析输入数据的日期格式，可能是yyyy-MM-dd
                    LocalDate inputDate = LocalDate.parse(vo.getDate(), formatter);
                    String formattedDate = inputDate.format(displayFormatter);
                    
                    for (StatisticalChartDataVO fullVo : fullDateData) {
                        if (fullVo.getDate().equals(formattedDate)) {
                            fullVo.setCount(vo.getCount());
                            break;
                        }
                    }
                } catch (Exception e) {
                    // 如果日期已经是MM-dd格式，直接比较
                    for (StatisticalChartDataVO fullVo : fullDateData) {
                        if (fullVo.getDate().equals(vo.getDate())) {
                            fullVo.setCount(vo.getCount());
                            break;
                        }
                    }
                }
            }
        }

        return fullDateData;
    }

    /**
     * 支持旧的方法签名，保持向后兼容
     */
    public List<StatisticalChartDataVO> completeDates(List<StatisticalChartDataVO> data, Timestamp referenceDate) {
        return completeDates(data, referenceDate, null);
    }

    @Override
    public Map getInvitationForInterviewDataList(RecruitmentDataListDTO dto) {
        CardUserVO cardUserVO = commonService.getCardUserInfo(dto.getUserId(), dto.getEnterpriseId(), dto.getDeptId(), dto.getType());
        if (cardUserVO.getType() == 2 || cardUserVO.getType() == 3) {
            if(CollectionUtils.isEmpty(cardUserVO.getUserIdList())){
                return MapUtils.create("no", 1, "list", Collections.emptyList(), "count", 0);
            }
            dto.setUserIdList(cardUserVO.getUserIdList());
        }

        if (dto.getPageNum() == null || dto.getPageNum() < 1) {
            dto.setPageNum(1);
        }
        Integer pageBegin = (dto.getPageNum() - 1) * dto.getPageSize();
        dto.setPageBegin(pageBegin);

        List<RecruitmentDataListVO> list = interviewRecordDAO.getInvitationForInterviewDataList(dto);
        int count = interviewRecordDAO.getInvitationForInterviewDataCount(dto);

        return MapUtils.create("no", 1, "list", list, "count", count);
    }

    @Override
    public Map getInterviewDataList(RecruitmentDataListDTO dto) {
        CardUserVO cardUserVO = commonService.getCardUserInfo(dto.getUserId(), dto.getEnterpriseId(), dto.getDeptId(), dto.getType());
        if (cardUserVO.getType() == 2 || cardUserVO.getType() == 3) {
            if(CollectionUtils.isEmpty(cardUserVO.getUserIdList())){
                return MapUtils.create("no", 1, "list", Collections.emptyList(), "count", 0);
            }
            dto.setUserIdList(cardUserVO.getUserIdList());
        }

        if (dto.getPageNum() == null || dto.getPageNum() < 1) {
            dto.setPageNum(1);
        }
        Integer pageBegin = (dto.getPageNum() - 1) * dto.getPageSize();
        dto.setPageBegin(pageBegin);

        List<RecruitmentDataListVO> list = interviewRecordDAO.getInterviewDataList(dto);
        int count = interviewRecordDAO.getInterviewDataCount(dto);

        return MapUtils.create("no", 1, "list", list, "count", count);
    }

    @Override
    public Map getOfferDataList(RecruitmentDataListDTO dto) {
        CardUserVO cardUserVO = commonService.getCardUserInfo(dto.getUserId(), dto.getEnterpriseId(), dto.getDeptId(), dto.getType());
        if (cardUserVO.getType() == 2 || cardUserVO.getType() == 3) {
            if(CollectionUtils.isEmpty(cardUserVO.getUserIdList())){
                return MapUtils.create("no", 1, "list", Collections.emptyList(), "count", 0);
            }
            dto.setUserIdList(cardUserVO.getUserIdList());
        }

        if (dto.getPageNum() == null || dto.getPageNum() < 1) {
            dto.setPageNum(1);
        }
        Integer pageBegin = (dto.getPageNum() - 1) * dto.getPageSize();
        dto.setPageBegin(pageBegin);

        List<RecruitmentDataListVO> list = interviewRecordDAO.getOfferDataList(dto);
        int count = interviewRecordDAO.getOfferDataCount(dto);

        return MapUtils.create("no", 1, "list", list, "count", count);
    }

    @Override
    public Map getEntryDataList(RecruitmentDataListDTO dto) {
        CardUserVO cardUserVO = commonService.getCardUserInfo(dto.getUserId(), dto.getEnterpriseId(), dto.getDeptId(), dto.getType());
        if (cardUserVO.getType() == 2 || cardUserVO.getType() == 3) {
            if(CollectionUtils.isEmpty(cardUserVO.getUserIdList())){
                return MapUtils.create("no", 1, "list", Collections.emptyList(), "count", 0);
            }
            dto.setUserIdList(cardUserVO.getUserIdList());
        }

        if (dto.getPageNum() == null || dto.getPageNum() < 1) {
            dto.setPageNum(1);
        }
        Integer pageBegin = (dto.getPageNum() - 1) * dto.getPageSize();
        dto.setPageBegin(pageBegin);

        List<RecruitmentDataListVO> list = entryRecordDAO.getEntryDataList(dto);
        int count = entryRecordDAO.getEntryDataCount(dto);

        return MapUtils.create("no", 1, "list", list, "count", count);
    }

    @Override
    public Map getSettleDataList(RecruitmentDataListDTO dto) {
        CardUserVO cardUserVO = commonService.getCardUserInfo(dto.getUserId(), dto.getEnterpriseId(), dto.getDeptId(), dto.getType());
        if (cardUserVO.getType() == 2 || cardUserVO.getType() == 3) {
            if(CollectionUtils.isEmpty(cardUserVO.getUserIdList())){
                return MapUtils.create("no", 1, "list", Collections.emptyList(), "count", 0);
            }
            dto.setUserIdList(cardUserVO.getUserIdList());
        }

        if (dto.getPageNum() == null || dto.getPageNum() < 1) {
            dto.setPageNum(1);
        }
        Integer pageBegin = (dto.getPageNum() - 1) * dto.getPageSize();
        dto.setPageBegin(pageBegin);

        List<RecruitmentDataListVO> list = followUpDAO.getSettleDataList(dto);
        int count = followUpDAO.getSettleDataCount(dto);

        return MapUtils.create("no", 1, "list", list, "count", count);
    }

    @Override
    public Map getConversionRateDataList(ConversionRateDataDTO dto) {

        CardUserVO cardUserVO = commonService.getCardUserInfo(dto.getUserId(), dto.getEnterpriseId(), dto.getDeptId(), dto.getType());
        if (cardUserVO.getType() == 2 || cardUserVO.getType() == 3) {
            if(CollectionUtils.isEmpty(cardUserVO.getUserIdList())){
                return MapUtils.create("no", 1, "invitationForInterviewCount", 0,
                        "interviewCount", 0, "offerCount", 0,
                        "entryCount", 0, "settleCount", 0);
            }
            dto.setUserIdList(cardUserVO.getUserIdList());
        }

        int invitationForInterviewCount = interviewRecordDAO.getInvitationForInterviewCount(dto);
        int interviewCount = interviewRecordDAO.getInterviewCount(dto);
        int offerCount = interviewRecordDAO.getOfferCount(dto);
        int entryCount = entryRecordDAO.getEntryCount(dto);
        int settleCount = followUpDAO.getSettleCountByInterviewTime(dto);
        return MapUtils.create("no", 1, "invitationForInterviewCount", invitationForInterviewCount,
                "interviewCount", interviewCount, "offerCount", offerCount,
                "entryCount", entryCount, "settleCount", settleCount);
    }

    @Override
    public Map getBasicData(BasicDataDTO dto) {
        CardUserVO cardUserVO = commonService.getCardUserInfo(dto.getUserId(), dto.getEnterpriseId(), dto.getDeptId(), dto.getType());
        if (CollectionUtils.isEmpty(cardUserVO.getUserIdList())) {
            return MapUtils.create("no", 1,
                    "performance", 0, "diffPerformance", 0,
                    "inviteCount", 0, "diffInviteCount", 0,
                    "interviewCount", 0, "diffInterviewCount", 0,
                    "entryCount", 0, "diffEntryCount", 0);
        }
        dto.setUserIdList(cardUserVO.getUserIdList());

        dto.setTimeRangeByType();
        // 业绩
        BigDecimal performance = commissionGaveLogDAO.getDataStatisticsPerformance(dto, 1);
        BigDecimal historyPerformance = commissionGaveLogDAO.getDataStatisticsPerformance(dto, 2);
        BigDecimal diffPerformance = safeSubtract(performance, historyPerformance);

        // 邀约
        int inviteCount = followUpDAO.getDataStatisticsInviteCount(dto, 1);
        int historyInviteCount = followUpDAO.getDataStatisticsInviteCount(dto, 2);
        int diffInviteCount = inviteCount - historyInviteCount;
        // 面试
        int interviewCount = interviewRecordDAO.getDataStatisticsInterviewCount(dto, 1);
        int historyInterviewCount = interviewRecordDAO.getDataStatisticsInterviewCount(dto, 2);
        int diffInterviewCount = interviewCount - historyInterviewCount;

        // 入职
        int entryCount = entryRecordDAO.getDataStatisticsEntryCount(dto, 1);
        int historyEntryCount = entryRecordDAO.getDataStatisticsEntryCount(dto, 2);
        int diffEntryCount = entryCount - historyEntryCount;

        return MapUtils.create("no", 1,
                "performance", performance, "diffPerformance", diffPerformance,
                "inviteCount", inviteCount, "diffInviteCount", diffInviteCount,
                "interviewCount", interviewCount, "diffInterviewCount", diffInterviewCount,
                "entryCount", entryCount, "diffEntryCount", diffEntryCount);
    }

    @Override
    public Map getEnterpriseBasicData(BasicDataDTO dto) {
        // 推荐
        int recommendCount = followRecommendDAO.getFollowRecommendCountByEnterpriseId(dto);
        // 邀约
        int inviteCount = followUpDAO.getEnterpriseDataStatisticsInviteCount(dto);
        // 面试
        int interviewCount = interviewRecordDAO.getEnterpriseDataStatisticsInterviewCount(dto);
        // 入职
        int entryCount = entryRecordDAO.getEnterpriseDataStatisticsEntryCount(dto);
        // 结算
        int SettleCount = followUpDAO.getEnterpriseSettleCount(dto);
        return MapUtils.create("no", 1,
                "recommendCount", recommendCount,
                "inviteCount", inviteCount,
                "interviewCount", interviewCount,
                "entryCount", entryCount,
                "SettleCount", SettleCount);
    }

    public static BigDecimal safeSubtract(BigDecimal a, BigDecimal b) {
        return (a != null ? a : BigDecimal.ZERO).subtract(b != null ? b : BigDecimal.ZERO);
    }

    @Override
    public Map getPerformanceTarget(PerformanceTargetDTO dto) {
        CardUserVO cardUserVO = commonService.getCardUserInfo(dto.getUserId(), dto.getEnterpriseId(), dto.getDeptId(), dto.getType());
        if (CollectionUtils.isEmpty(cardUserVO.getUserIdList())) {
            return MapUtils.create("no", 1, "performanceTarget", 0, "performance", 0);
        }
        dto.setUserIdList(cardUserVO.getUserIdList());

        String date = dto.getDate();
        String[] dateArray = date.split("-");
        dto.setYear(Integer.parseInt(dateArray[0]));
        dto.setMonth(Integer.parseInt(dateArray[1]));

        BigDecimal performanceTarget = performanceTargetDAO.getPerformanceTarget(dto);

        BasicDataDTO basicDataDTO = new BasicDataDTO();
        // 设置开始时间和结束时间
        Calendar calendar = Calendar.getInstance();
        calendar.set(dto.getYear(), dto.getMonth() - 1, 1, 0, 0, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        basicDataDTO.setTimeStart(new Timestamp(calendar.getTimeInMillis()));
        
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        basicDataDTO.setTimeEnd(new Timestamp(calendar.getTimeInMillis()));

        basicDataDTO.setUserIdList(cardUserVO.getUserIdList());
        BigDecimal performance = commissionGaveLogDAO.getDataStatisticsPerformance(basicDataDTO, 1);

        return MapUtils.create("no", 1, "performanceTarget", performanceTarget, "performance", performance);
    }

    /**
     * 将数据看板工作量JSON转换为四个统计图表数据列表
     * @param dataBoardWorkLoadJSONObject JSON数据
     * @return 包含四个数据列表的Map
     */
    private Map<String, List<StatisticalChartDataVO>> convertToStatisticalChartDataLists(JSONObject dataBoardWorkLoadJSONObject) {
        // 创建四个不同类型的数据列表
        List<StatisticalChartDataVO> contractCountList = new ArrayList<>();
        List<StatisticalChartDataVO> wechatCountList = new ArrayList<>();
        List<StatisticalChartDataVO> callCountList = new ArrayList<>();
        List<StatisticalChartDataVO> resumeCountList = new ArrayList<>();
        
        try {
            
            // 检查返回结果是否成功
            if (dataBoardWorkLoadJSONObject != null && dataBoardWorkLoadJSONObject.getInteger("no") != null && dataBoardWorkLoadJSONObject.getInteger("no") == 200) {
                // 检查数据结构，直接获取list数据
                if (dataBoardWorkLoadJSONObject.containsKey("list")) {
                    JSONArray dataList = dataBoardWorkLoadJSONObject.getJSONArray("list");
                    
                    // 直接遍历数据列表
                    for (int i = 0; i < dataList.size(); i++) {
                        JSONObject dataItem = dataList.getJSONObject(i);
                        
                        // 确保数据项包含日期和计数信息
                        if (dataItem != null && dataItem.containsKey("date")) {
                            String date = dataItem.getString("date");
                            
                            // 提取各种计数数据，缺失则默认为0
                            Integer contractCount = dataItem.getInteger("contractCount");
                            Integer wechatCount = dataItem.getInteger("wechatCount");
                            Integer callCount = dataItem.getInteger("callCount");
                            Integer resumeCount = dataItem.getInteger("resumeCount");
                            
                            // 添加到合同列表
                            StatisticalChartDataVO contractVO = new StatisticalChartDataVO();
                            contractVO.setDate(date);
                            contractVO.setCount(contractCount != null ? contractCount : 0);
                            contractCountList.add(contractVO);
                            
                            // 添加到微信列表
                            StatisticalChartDataVO wechatVO = new StatisticalChartDataVO();
                            wechatVO.setDate(date);
                            wechatVO.setCount(wechatCount != null ? wechatCount : 0);
                            wechatCountList.add(wechatVO);
                            
                            // 添加到电话列表
                            StatisticalChartDataVO callVO = new StatisticalChartDataVO();
                            callVO.setDate(date);
                            callVO.setCount(callCount != null ? callCount : 0);
                            callCountList.add(callVO);
                            
                            // 添加到简历列表
                            StatisticalChartDataVO resumeVO = new StatisticalChartDataVO();
                            resumeVO.setDate(date);
                            resumeVO.setCount(resumeCount != null ? resumeCount : 0);
                            resumeCountList.add(resumeVO);
                        }
                    }
                }
            }
            
        } catch (Exception e) {
            log.error("解析数据看板工作量JSON出错", e);
        }
        
        // 返回包含四个列表的Map
        Map<String, List<StatisticalChartDataVO>> result = new HashMap<>();
        result.put("contractCountList", contractCountList);
        result.put("wechatCountList", wechatCountList);
        result.put("callCountList", callCountList);
        result.put("resumeCountList", resumeCountList);
        
        return result;
    }

}
