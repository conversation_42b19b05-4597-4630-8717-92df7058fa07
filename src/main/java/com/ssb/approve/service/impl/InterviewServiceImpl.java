package com.ssb.approve.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.ssb.approve.common.constants.Enum.NoticeReadEnum;
import com.ssb.approve.common.constants.Enum.NoticeStatusEnum;
import com.ssb.approve.common.constants.Enum.NoticeTypeEnum;
import com.ssb.approve.common.kafka.KafkaProducer;
import com.ssb.approve.dao.*;
import com.ssb.approve.entity.*;
import com.ssb.approve.model.FollowUpModel;
import com.ssb.approve.model.QueryFollowUpModel;
import com.ssb.approve.model.vo.CopyLinkVO;
import com.ssb.approve.model.vo.FollowUpVO;
import com.ssb.approve.model.vo.InterviewRecordVO;
import com.ssb.approve.service.DataStatisticService;
import com.ssb.approve.service.EnterpriseNoticeRecordService;
import com.ssb.approve.service.InterviewService;
import com.ssb.approve.service.NoticeRecordService;
import com.ssb.approve.service.client.ProjectService;
import com.ssb.approve.service.client.ResumeService;
import com.ssb.approve.service.client.UserService;
import com.ssb.approve.utils.DateUtils;
import com.ssb.approve.utils.MapUtils;
import com.ssb.approve.utils.RedisLockUtils;
import com.ssb.approve.utils.WeChatUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.util.*;

/**
 * @ClassName: InterviewServiceImpl
 * @Description: 面试 impl
 * @Author: YZK
 * @Date: 2019年12月23日15:16:14
 **/
@Service
public class InterviewServiceImpl implements InterviewService {

    @Autowired
    private FollowUpDAO followUpDAO;

    @Autowired
    private InterviewRecordDAO interviewRecordDAO;

    @Autowired
    private FollowLogDAO followLogDAO;
    @Autowired
    private ProjectDAO projectDAO;

    @Autowired
    private EntryRecordDAO entryRecordDAO;

    @Autowired
    private RedisLockUtils redisLockUtils;

    @Autowired
    private UserService userService;

    @Autowired
    private ProjectService projectService;

    @Autowired
    private CommonServiceImpl commonService;

    @Autowired
    private ResumeService resumeService;

    @Autowired
    private NoticeRecordService noticeRecordService;

    @Autowired
    private ProjectParticipantDAO projectParticipantDAO;

    @Autowired
    private DataStatisticService dataStatisticService;

    @Autowired
    private KafkaProducer kafkaProducer;

    @Autowired
    private ContactDAO contactDAO;

    @Autowired
    private EnterpriseNoticeRecordService enterpriseNoticeRecordService;

    @Autowired
    private WeChatUtil weChatUtil;


    /**
     * 获取已约待面试列表
     *
     * @param queryFollowUpModel
     * @return
     */
    @Override
    public Map getInterviewList(QueryFollowUpModel queryFollowUpModel) {
        queryFollowUpModel.setFinish(0);
        Integer pageBegin = (queryFollowUpModel.getPageNum() - 1) * queryFollowUpModel.getPageSize();
        queryFollowUpModel.setPageBegin(pageBegin);

        List<Integer> userIdList = dataStatisticService.getUserIds(queryFollowUpModel.getUserId(), queryFollowUpModel.getEnterpriseId());
        if(CollectionUtils.isNotEmpty(userIdList)){
            queryFollowUpModel.setUserIdList(userIdList);
        }

        List<FollowUpVO> followUps = followUpDAO.getInterviewList(queryFollowUpModel);
        commonService.handleProjectExpectDetail(followUps);
        int count = followUpDAO.getInterviewCount(queryFollowUpModel);
        return MapUtils.create("no", 1, "msg", "获取成功", "results", followUps, "count", count);
    }

    /**
     * 更新面试结果状态
     *
     * @param followUpModel
     * @return
     */
    @Override
    @Transactional
    public Map updateInterview(FollowUpModel followUpModel) {

        //数据加锁  防止出现同时操作情况
        if (!redisLockUtils.lock(1, followUpModel.getId(), followUpModel.getUserId(), 1)) {
            return MapUtils.create("no", 0, "msg", "数据异常，请刷新后再操作！");
        }
        //当前时间
        Timestamp timestamp = new Timestamp(System.currentTimeMillis());
        //更新面试记录
        InterviewRecord interviewRecord = interviewRecordDAO.getInterviewRecordById(followUpModel.getId());
        if (interviewRecord == null) {
            return MapUtils.create("no", 0, "msg", "数据异常更新失败");
        }
        // 0 默认  2 改约时间 4 下一轮面试
        if (!(interviewRecord.getStatus() == 0 || interviewRecord.getStatus() == 2 || interviewRecord.getStatus() == 4)) {
            return MapUtils.create("no", 0, "msg", "面试记录已跟进");
        }
        // 检测面试每日上线   改约时间   下一轮面试
        if (followUpModel.getInterviewStatus() == 2 || followUpModel.getInterviewStatus() == 4) {
            FollowUp followUp = followUpDAO.getFollowUpById(interviewRecord.getFollowId());
            if (followUp == null) {
                return MapUtils.create("no", 0, "msg", "招聘跟进异常，请稍后重试");
            }
            //约面时间 是否已满
            Integer code = checkInterviewLimit(followUp.getProjectId(), followUpModel.getBehaviorTime());
            if (code.equals(500)) {
                return MapUtils.create("no", 0, "msg", "项目异常，请稍后重试");
            }
            //已达到上限
            if (code.equals(201)) {
                return MapUtils.create("no", 0, "msg", "您选择的时间已约满，请重新预约时间");
            }

            if(followUpModel.getInterviewStatus() == 2){
                // 将面试时间修改同步到app
                Map<String, Object> ssbUpdateInterviewTimeMap = new HashMap<>();
                if(followUpModel.getId() != null && followUpModel.getBehaviorTime() != null){
                    ssbUpdateInterviewTimeMap.put("followId", followUpModel.getId());
                    ssbUpdateInterviewTimeMap.put("interviewTime", followUpModel.getBehaviorTime());
                    kafkaProducer.updateInterviewTimeFromCRM(ssbUpdateInterviewTimeMap);
                }
            }
        }

        InterviewRecord interviewRecordUpdate = new InterviewRecord();
        interviewRecordUpdate.setId(interviewRecord.getId());
        interviewRecordUpdate.setStatus(followUpModel.getInterviewStatus());
        //下轮面试 轮数增加 状态重置成 0
        if (followUpModel.getInterviewStatus() == 4) {
            interviewRecordUpdate.setNode(interviewRecord.getNode() + 1);
            interviewRecordUpdate.setStatus(0);
        }
//        取消
//        // 5 发offer
//        if (followUpModel.getInterviewStatus() == 5){
//            // 发offer 需要额外记录 预估时间
//            interviewRecordUpdate.setEstimateTime(followUpModel.getBehaviorTime());
//        } else {
//            interviewRecordUpdate.setBehaviorTime(followUpModel.getBehaviorTime());
//        }

        interviewRecordUpdate.setBehaviorTime(followUpModel.getBehaviorTime());
        interviewRecordUpdate.setUpdateBy(followUpModel.getUserId());
        interviewRecordUpdate.setUpdateTime(timestamp);
        interviewRecordUpdate.setRemark(followUpModel.getRemark());
        interviewRecordUpdate.setFailure(followUpModel.getFailure());
        interviewRecordDAO.updateInterviewRecord(interviewRecordUpdate);

        //更新主数据
        FollowUp followUpUpdate = new FollowUp();
        followUpUpdate.setId(interviewRecord.getFollowId());
        // interviewStatus 1未参加 2 改约时间 3 未通过 4 下轮面试 5 发offer  (没办法做到统一  手动匹配)
        if (followUpModel.getInterviewStatus() == 1) {
            followUpUpdate.setCurrentStatus(2);
        } else if (followUpModel.getInterviewStatus() == 2) {
            followUpUpdate.setCurrentStatus(3);
        } else if (followUpModel.getInterviewStatus() == 3) {
            followUpUpdate.setCurrentStatus(4);
        } else if (followUpModel.getInterviewStatus() == 4) {
            followUpUpdate.setCurrentStatus(5);
        } else if (followUpModel.getInterviewStatus() == 5) {
            followUpUpdate.setCurrentStatus(6);
        }
        followUpUpdate.setUpdateBy(followUpModel.getUserId());
        followUpUpdate.setUpdateTime(timestamp);
        followUpDAO.updateFollowUp(followUpUpdate);
        //添加操作记录
        FollowLog followLog = new FollowLog();
        followLog.setEnterpriseId(followUpModel.getEnterpriseId());
        followLog.setFollowId(interviewRecord.getFollowId());
        followLog.setType(1);
        // interviewStatus 1未参加 2 改约时间 3 未通过 4 下轮面试 5 发offer  (没办法做到统一  手动匹配)
        if (followUpModel.getInterviewStatus() == 1) {
            followLog.setStatus(2);
        } else if (followUpModel.getInterviewStatus() == 2) {
            followLog.setStatus(3);
        } else if (followUpModel.getInterviewStatus() == 3) {
            followLog.setStatus(4);
        } else if (followUpModel.getInterviewStatus() == 4) {
            followLog.setStatus(5);
        } else if (followUpModel.getInterviewStatus() == 5) {
            followLog.setStatus(6);
        }
        followLog.setBehaviorTime(followUpModel.getBehaviorTime());
        followLog.setCreateBy(followUpModel.getUserId());
        followLog.setCreateTime(timestamp);
        followLog.setRemark(followUpModel.getRemark());
        followLog.setFailure(followUpModel.getFailure());
        followLogDAO.save(followLog);
        //解锁
        redisLockUtils.lock(1, followUpModel.getId(), followUpModel.getUserId(), 2);
        //面试时间变更-消息中心通知消息
        if (followUpModel.getInterviewStatus() == 2) {
            Integer projectId = followUpDAO.getProjectIdById(followUpModel.getId());
            String projectTitle = projectDAO.getTitleByProjectId(projectId);
            List<Integer> modifyUserId = projectParticipantDAO.getProjectParticipant(projectId);

            List<CrmNoticeRecord> records = new ArrayList<>();
            modifyUserId.forEach(userId -> {
                //发送面试时间变更-通知消息
                CrmNoticeRecord crmNoticeRecord = new CrmNoticeRecord(userId,
                        projectId, null, 3, "面试时间变更",
                        projectTitle + "项目信息变更，请及时查看",
                        2, 2, 0, projectId, 9,
                        11, 0, userId,
                        new Timestamp(System.currentTimeMillis()), userId,
                        new Timestamp(System.currentTimeMillis()));
                records.add(crmNoticeRecord);
            });
            noticeRecordService.noticeCancelShareProject(records);
        }

        return MapUtils.create("no", 1, "msg", "更新成功");
    }

    /**
     * 获取面试确认列表
     *
     * @param queryFollowUpModel
     * @return
     */
    @Override
    public Map getInterviewAuthList(QueryFollowUpModel queryFollowUpModel) {
        Integer pageBegin = (queryFollowUpModel.getPageNum() - 1) * queryFollowUpModel.getPageSize();
        queryFollowUpModel.setPageBegin(pageBegin);
        List<InterviewRecordVO> interviewRecords = Lists.newArrayList();
        interviewRecords = interviewRecordDAO.getInterviewAuthList(queryFollowUpModel);
        int count = interviewRecordDAO.getInterviewAuthCount(queryFollowUpModel);
        return MapUtils.create("no", 1, "msg", "获取成功", "results", interviewRecords, "count", count);
    }

    /**
     * 面试确认 -- 审核操作
     *
     * @param id 面试记录表id
     * @param userId  操作人id
     * @return
     */
    @Override
    @Transactional
    public Map updateInterviewAuth(Integer id, Integer userId) {
        //数据加锁  防止出现同时操作情况
        if (!redisLockUtils.lock(1, id, userId, 1)) {
            return MapUtils.create("no", 0, "msg", "数据异常，请刷新后再操作！");
        }
        //当前时间
        Timestamp timestamp = new Timestamp(System.currentTimeMillis());
        InterviewRecord interviewRecord = interviewRecordDAO.getInterviewRecordById(id);
        //面试 状态
        Integer status = 0;
        if (interviewRecord != null) {
            status = interviewRecord.getStatus();
            //审核已通过
            if (interviewRecord.getAuth() == 1) {
                return MapUtils.create("no", 0, "msg", "面试结果已审核");
            }
            // 1 未参加 3 未通过 5 发offer 取反
            if (!(status == 1 || status == 3 || status == 5)) {
                return MapUtils.create("no", 0, "msg", "面试结果不是结束状态不能审核，请继续跟进。");
            }

            InterviewRecord interviewRecordUpdate = new InterviewRecord();
            interviewRecordUpdate.setId(interviewRecord.getId());
            interviewRecordUpdate.setAuth(1);
            interviewRecordUpdate.setUpdateBy(userId);
            interviewRecordUpdate.setUpdateTime(timestamp);
            interviewRecordDAO.updateInterviewAuth(interviewRecordUpdate);

            //未参加、未通过  更新主数据 为完成状态
            FollowUp followUp = new FollowUp();
            followUp.setId(interviewRecord.getFollowId());
            // 1 未参加  3 未通过
            if (status == 1 || status == 3) {
                followUp.setFinish(1);
            } else {
                // 5 发offer
                followUp.setStatus(2); //审核通过  状态变成  通过待入职
            }
            followUp.setUpdateBy(userId);
            followUp.setUpdateTime(timestamp);
            followUpDAO.updateFollowUp(followUp);

            //发offer 直接进入下个环节  新增 入职记录
            if (status == 5) {
                //强制校验
                EntryRecord entryRecord = entryRecordDAO.getEntryRecordByFollowUpId(interviewRecord.getFollowId());
                if (entryRecord == null) {
                    entryRecord = new EntryRecord();
                    entryRecord.setEnterpriseId(interviewRecord.getEnterpriseId());
                    entryRecord.setFollowId(interviewRecord.getFollowId());
//  审核通过   没有预估入职时间
//                if (interviewRecord.getEstimateTime() != null) {
//                    entryRecord.setEstimateTime(interviewRecord.getEstimateTime());
//                } else {
//                    entryRecord.setEstimateTime(interviewRecord.getBehaviorTime());
//                }
                    entryRecord.setCreateBy(interviewRecord.getCreateBy());
                    entryRecord.setCreateTime(timestamp);
                    entryRecord.setUpdateBy(userId);
                    entryRecord.setUpdateTime(timestamp);
                    entryRecord.setRemark(interviewRecord.getRemark());
                    entryRecord.setAuthType(interviewRecord.getAuthType());
                    // 保存入职信息
                    entryRecordDAO.save(entryRecord);
                }
            }

            return MapUtils.create("no", 1, "msg", "审核成功");
        }
        //解锁
        redisLockUtils.lock(1, id, userId, 2);
        return MapUtils.create("no", 0, "msg", "审核异常，请稍后重试");
    }

    /**
     * 面试审核 跟进操作
     *
     * @param followUpModel
     * @return
     */
    @Override
    @Transactional
    public Map interviewAuthFollowUp(FollowUpModel followUpModel) {
        //数据加锁  防止出现同时操作情况
        if (!redisLockUtils.lock(1, followUpModel.getId(), followUpModel.getUserId(), 1)) {
            return MapUtils.create("no", 0, "msg", "数据异常，请刷新后再操作！");
        }

        //当前时间
        Timestamp timestamp = new Timestamp(System.currentTimeMillis());
        //更新面试记录
        InterviewRecord interviewRecord = interviewRecordDAO.getInterviewRecordById(followUpModel.getId());
        if (interviewRecord == null) {
            return MapUtils.create("no", 0, "msg", "数据异常更新失败");
        }
        //审核通过
        if (interviewRecord.getAuth() == 1) {
            return MapUtils.create("no", 0, "msg", "审核通过不能跟进，请刷新页面");
        }

        FollowUp follow = followUpDAO.getFollowUpById(interviewRecord.getFollowId());
        if (follow == null) {
            return MapUtils.create("no", 0, "msg", "招聘跟进异常，请稍后重试");
        }
        // 处理项目的危险状态
        projectService.updateProjectDanger(follow.getProjectId(), followUpModel.getUserId());

        // 处理待办消息
        CrmNoticeRecord record = new CrmNoticeRecord();
        record.setNoticeType(NoticeTypeEnum.HEADHUNTER_REVIEW_INTERVIEW_RESULT_CONFIRM.getValue());
        record.setTargetId(followUpModel.getId());
        record.setTargetType(1);
        record.setStatus(NoticeStatusEnum.PROCESSED.getValue());
        record.setRead(NoticeReadEnum.READ.getValue());
        record.setUpdateBy(followUpModel.getUserId());
        record.setUpdateTime(new Timestamp(System.currentTimeMillis()));
        noticeRecordService.updateNoticeRecord(record);

        CrmNoticeRecord noFollowUpRecord = new CrmNoticeRecord();
        noFollowUpRecord.setNoticeType(NoticeTypeEnum.RECRUITMENT_FOLLOW_UP_PROJECT_REMINDER_NO_FOLLOW_UP_7_DAYS.getValue());
        noFollowUpRecord.setProjectId(follow.getProjectId());
        noFollowUpRecord.setStatus(NoticeStatusEnum.PROCESSED.getValue());
        noFollowUpRecord.setRead(NoticeReadEnum.READ.getValue());
        noFollowUpRecord.setUpdateBy(followUpModel.getUserId());
        noFollowUpRecord.setUpdateTime(new Timestamp(System.currentTimeMillis()));
        noticeRecordService.updateProjectNoticeRecord(noFollowUpRecord);

        CrmNoticeRecord withFollowUpRecord = new CrmNoticeRecord();
        withFollowUpRecord.setNoticeType(NoticeTypeEnum.RECRUITMENT_FOLLOW_UP_PROJECT_REMINDER_WITH_FOLLOW_UP_7_DAYS.getValue());
        withFollowUpRecord.setProjectId(follow.getProjectId());
        withFollowUpRecord.setStatus(NoticeStatusEnum.PROCESSED.getValue());
        withFollowUpRecord.setRead(NoticeReadEnum.READ.getValue());
        withFollowUpRecord.setUpdateBy(followUpModel.getUserId());
        withFollowUpRecord.setUpdateTime(new Timestamp(System.currentTimeMillis()));
        noticeRecordService.updateProjectNoticeRecord(withFollowUpRecord);

        //处理企业端待办消息
        CrmEnterpriseNoticeRecord recordEnterprise = new CrmEnterpriseNoticeRecord();
        recordEnterprise.setContactId(follow.getContactId());
        recordEnterprise.setProjectId(follow.getProjectId());
        recordEnterprise.setNoticeType(2);
        recordEnterprise.setType(1);
        recordEnterprise.setStatus(4);
        recordEnterprise.setRead(1);
        recordEnterprise.setUpdateBy(followUpModel.getUserId());
        recordEnterprise.setUpdateTime(new Timestamp(System.currentTimeMillis()));
        enterpriseNoticeRecordService.updateNoticeRecord(recordEnterprise);

        // 检测面试每日上线   改约时间   下一轮面试
        if (followUpModel.getInterviewStatus() == 2 || followUpModel.getInterviewStatus() == 4) {

            //约面时间 是否已满
            Integer code = checkInterviewLimit(follow.getProjectId(), followUpModel.getBehaviorTime());
            if (code.equals(500)) {
                return MapUtils.create("no", 0, "msg", "项目异常，请稍后重试");
            }
            //已达到上限
            if (code.equals(201)) {
                return MapUtils.create("no", 0, "msg", "您选择的时间已约满，请重新预约时间");
            }

            if(followUpModel.getInterviewStatus() == 2){
                // 将面试时间修改同步到app
                Map<String, Object> ssbUpdateInterviewTimeMap = new HashMap<>();
                if(followUpModel.getId() != null && followUpModel.getBehaviorTime() != null){
                    ssbUpdateInterviewTimeMap.put("followId", followUpModel.getId());
                    ssbUpdateInterviewTimeMap.put("interviewTime", followUpModel.getBehaviorTime());
                    kafkaProducer.updateInterviewTimeFromCRM(ssbUpdateInterviewTimeMap);
                }
            }
        }

        InterviewRecord interviewRecordUpdate = new InterviewRecord();
        interviewRecordUpdate.setId(interviewRecord.getId());
        interviewRecordUpdate.setStatus(followUpModel.getInterviewStatus());
        //下轮面试 轮数增加 状态重置成 0
        if (followUpModel.getInterviewStatus() == 4) {
            interviewRecordUpdate.setNode(interviewRecord.getNode() + 1);
            interviewRecordUpdate.setStatus(0);
        }
//  发offer  取消 预估时间
//        //发offer
//        if (followUpModel.getInterviewStatus() == 5) {
//            interviewRecordUpdate.setEstimateTime(followUpModel.getBehaviorTime());
//        } else {
//            interviewRecordUpdate.setBehaviorTime(followUpModel.getBehaviorTime());
//        }
        interviewRecordUpdate.setBehaviorTime(followUpModel.getBehaviorTime());
        interviewRecordUpdate.setUpdateBy(followUpModel.getUserId());
        interviewRecordUpdate.setUpdateTime(timestamp);
        interviewRecordUpdate.setRemark(followUpModel.getRemark());
        interviewRecordUpdate.setFailure(followUpModel.getFailure());
        interviewRecordUpdate.setReviewTime(timestamp);
        interviewRecordUpdate.setReviewUserId(followUpModel.getUserId());
        interviewRecordDAO.updateInterviewRecord(interviewRecordUpdate);

        //未参加、未通过、发offer  更新成审核通过状态
        if (followUpModel.getInterviewStatus() == 1 || followUpModel.getInterviewStatus() == 3 || followUpModel.getInterviewStatus() == 5) {
            InterviewRecord interviewRecordAuthUpdate = new InterviewRecord();
            interviewRecordAuthUpdate.setId(interviewRecord.getId());
            interviewRecordAuthUpdate.setAuth(1);
            interviewRecordAuthUpdate.setUpdateBy(followUpModel.getUserId());
            interviewRecordAuthUpdate.setUpdateTime(timestamp);
            interviewRecordDAO.updateInterviewAuth(interviewRecordAuthUpdate);
            //发offer 创建入职记录
            if (followUpModel.getInterviewStatus() == 5) {
                EntryRecord entryRecord = new EntryRecord();
                entryRecord.setEnterpriseId(interviewRecord.getEnterpriseId());
                entryRecord.setFollowId(interviewRecord.getFollowId());
//   审核通过   没有预估入职时间
//                if (interviewRecord.getEstimateTime() != null) {
//                    entryRecord.setEstimateTime(interviewRecord.getEstimateTime());
//                } else {
//                    entryRecord.setEstimateTime(interviewRecord.getBehaviorTime());
//                }
                entryRecord.setCreateBy(interviewRecord.getCreateBy());
                entryRecord.setCreateTime(timestamp);
                entryRecord.setUpdateBy(followUpModel.getUserId());
                entryRecord.setUpdateTime(timestamp);
                entryRecord.setRemark(interviewRecord.getRemark());
                entryRecord.setAuthType(interviewRecord.getAuthType());
                // 保存入职信息
                entryRecordDAO.save(entryRecord);
            }

            // 将面试结果同步到app
            Map<String, Object> ssbInterviewMap = new HashMap<>();
            String phone = contactDAO.getPhone(followUpModel.getContactId());
            if(phone != null){
                ssbInterviewMap.put("phone", phone);
                // app表中对应状态  0/待反馈（企业未给出面试结果）、1/未参加、2/面试通过、3/面试不通过
                // interviewStatus 1未参加 2 改约时间 3 未通过 4 下轮面试 5 发offer
                if (followUpModel.getInterviewStatus() == 1) {
                    ssbInterviewMap.put("status", 1);
                } else if (followUpModel.getInterviewStatus() == 3) {
                    ssbInterviewMap.put("status", 3);
                    if(followUpModel.getFailure() != null){
                        ssbInterviewMap.put("failure", followUpModel.getFailure());
                    }
                } else if (followUpModel.getInterviewStatus() == 5) {
                    ssbInterviewMap.put("status", 2);
                }
                kafkaProducer.updateInterviewFromCRM(ssbInterviewMap);
            }
        }

        //更新主数据
        FollowUp followUp = new FollowUp();
        //未参加、未通过
        if (followUpModel.getInterviewStatus() == 1 || followUpModel.getInterviewStatus() == 3) {
            followUp.setFinish(1); //完成状态
        }
        // interviewStatus 1未参加 2 改约时间 3 未通过 4 下轮面试 5 发offer  (没办法做到统一  手动匹配)
        if (followUpModel.getInterviewStatus() == 1) {
            followUp.setCurrentStatus(2);
        } else if (followUpModel.getInterviewStatus() == 2) {
            followUp.setCurrentStatus(3);
        } else if (followUpModel.getInterviewStatus() == 3) {
            followUp.setCurrentStatus(4);
        } else if (followUpModel.getInterviewStatus() == 4) {
            followUp.setCurrentStatus(5);
        } else if (followUpModel.getInterviewStatus() == 5) {
            followUp.setCurrentStatus(6);
            followUp.setStatus(2);  //审核通过  状态变成  通过待入职
        }
        followUp.setUpdateBy(followUpModel.getUserId());
        followUp.setUpdateTime(timestamp);
        followUp.setId(interviewRecord.getFollowId());
        followUpDAO.updateFollowUp(followUp);
        //添加操作记录
        FollowLog followLog = new FollowLog();
        followLog.setEnterpriseId(followUpModel.getEnterpriseId());
        followLog.setFollowId(interviewRecord.getFollowId());
        followLog.setType(1);
        // interviewStatus 1未参加 2 改约时间 3 未通过 4 下轮面试 5 发offer  (没办法做到统一  手动匹配)
        if (followUpModel.getInterviewStatus() == 1) {
            followLog.setStatus(2);
        } else if (followUpModel.getInterviewStatus() == 2) {
            followLog.setStatus(3);
        } else if (followUpModel.getInterviewStatus() == 3) {
            followLog.setStatus(4);
        } else if (followUpModel.getInterviewStatus() == 4) {
            followLog.setStatus(5);
        } else if (followUpModel.getInterviewStatus() == 5) {
            followLog.setStatus(6);
        }
        followLog.setBehaviorTime(followUpModel.getBehaviorTime());
        followLog.setCreateBy(followUpModel.getUserId());
        followLog.setCreateTime(timestamp);
        followLog.setRemark(followUpModel.getRemark());
        followLog.setFailure(followUpModel.getFailure());
        followLogDAO.save(followLog);

        // app简历转化状态
        if (followUpModel.getInterviewStatus() == 3 || followUpModel.getInterviewStatus() == 5) {
            // 更新app用户转化结果
            int contactId = followUpDAO.getContactIdByFollowId(interviewRecord.getFollowId());
            resumeService.updateAppResume(contactId, 0, 1);
        }

        //解锁
        redisLockUtils.lock(1, followUpModel.getId(), followUpModel.getUserId(), 2);
        return MapUtils.create("no", 1, "msg", "跟进成功");
    }

    /**
     * 获取面试每日上线数
     *
     * @param projectId
     * @param days
     * @return
     */
    @Override
    public Map getInterviewDailyLimit(Integer projectId, Integer days) {
        // 当天在内的7个自然天
        //今天
        String beginTime = DateUtils.getCurrentDate();
        //第七天
        Calendar c = Calendar.getInstance();
        c.setTime(DateUtils.getDateBefore(new Date(), -6));
        String endTime = DateUtils.getStringDate(c);
        //时间段内满足条件的天数
        List<String> daysStr = interviewRecordDAO.getInterviewDailyLimit(projectId, days, beginTime + " 00:00:00", endTime + " 23:59:59");
        return MapUtils.create("no", 1, "msg", "获取成功", "daysStr", daysStr);
    }

    /**
     * 检测面试上限
     * 指定日期
     *
     * @param projectId 项目
     * @param timestamp 时间
     * @return
     */
    @Override
    public Integer checkInterviewLimit(Integer projectId, Timestamp timestamp) {
        //状态码
        Integer code = 500;
        //获取指定时间上线
        Object projectResult = projectService.getProjectInfo(projectId);
        JSONObject jsonObjectResult = JSONObject.parseObject(JSON.toJSON(projectResult).toString());
        //判断返回参数是否正常
        if (jsonObjectResult.getInteger("no") != null && jsonObjectResult.getInteger("no").equals(200)) {
            //正常保存逻辑
            JSONObject jsonObject = jsonObjectResult.getJSONObject("obj");
            if (jsonObject != null) {
                Integer interviewUpperLimit = jsonObject.getInteger("interviewUpperLimit");
                if (interviewUpperLimit != null) {
                    //行为时间
                    String behaviorTime = DateUtils.formatDate(timestamp);
                    //时间段内面试数量
                    int interviewCount = interviewRecordDAO.getInterviewCountByProjectId(projectId, behaviorTime + " 00:00:00", behaviorTime + " 23:59:59");
                    if (interviewCount < interviewUpperLimit.intValue()) {
                        code = 200;
                    } else {
                        code = 201;
                    }
                } else {
                    code = 200;
                }
            }
        }
        return code;
    }

    @Override
    public Map getEnterpriseProjectInterviewAuthList(QueryFollowUpModel queryFollowUpModel) {
        List<InterviewRecordVO> interviewRecords = interviewRecordDAO.getEnterpriseProjectInterviewAuthList(queryFollowUpModel);
        int count = interviewRecordDAO.getEnterpriseProjectInterviewAuthListCount(queryFollowUpModel);
        return MapUtils.create("no", 1, "msg", "获取成功", "results", interviewRecords, "count", count);
    }

    @Override
    @Transactional
    public Map interviewEnterpriseAuthFollowUp(FollowUpModel followUpModel) {
        //数据加锁  防止出现同时操作情况
        if (!redisLockUtils.lock(1, followUpModel.getId(), followUpModel.getUserId(), 1)) {
            return MapUtils.create("no", 0, "msg", "数据异常，请刷新后再操作！");
        }

        //当前时间
        Timestamp timestamp = new Timestamp(System.currentTimeMillis());
        //更新面试记录
        InterviewRecord interviewRecord = interviewRecordDAO.getInterviewRecordById(followUpModel.getId());
        if (interviewRecord == null) {
            return MapUtils.create("no", 0, "msg", "数据异常更新失败");
        }
        //审核通过
        if (interviewRecord.getAuth() == 1) {
            return MapUtils.create("no", 0, "msg", "审核通过不能跟进，请刷新页面");
        }

        FollowUp follow = followUpDAO.getFollowUpById(interviewRecord.getFollowId());
        if (follow == null) {
            return MapUtils.create("no", 0, "msg", "招聘跟进异常，请稍后重试");
        }
        // 处理项目的危险状态
        projectService.updateProjectDanger(follow.getProjectId(), followUpModel.getUserId());

        // 处理待办消息
        CrmNoticeRecord record = new CrmNoticeRecord();
        record.setNoticeType(NoticeTypeEnum.HEADHUNTER_REVIEW_INTERVIEW_RESULT_CONFIRM.getValue());
        record.setTargetId(followUpModel.getId());
        record.setTargetType(1);
        record.setStatus(NoticeStatusEnum.PROCESSED.getValue());
        record.setRead(NoticeReadEnum.READ.getValue());
        record.setUpdateBy(followUpModel.getUserId());
        record.setUpdateTime(new Timestamp(System.currentTimeMillis()));
        noticeRecordService.updateNoticeRecord(record);

        //处理企业端待办消息
        CrmEnterpriseNoticeRecord recordEnterprise = new CrmEnterpriseNoticeRecord();
        recordEnterprise.setContactId(follow.getContactId());
        recordEnterprise.setProjectId(follow.getProjectId());
        recordEnterprise.setNoticeType(2);
        recordEnterprise.setType(1);
        recordEnterprise.setStatus(4);
        recordEnterprise.setRead(1);
        recordEnterprise.setUpdateBy(followUpModel.getUserId());
        recordEnterprise.setUpdateTime(new Timestamp(System.currentTimeMillis()));
        enterpriseNoticeRecordService.updateNoticeRecord(recordEnterprise);

        // 检测面试每日上线   改约时间   下一轮面试
        if (followUpModel.getInterviewStatus() == 2 || followUpModel.getInterviewStatus() == 4) {
            //约面时间 是否已满
            Integer code = checkInterviewLimit(follow.getProjectId(), followUpModel.getBehaviorTime());
            if (code.equals(500)) {
                return MapUtils.create("no", 0, "msg", "项目异常，请稍后重试");
            }
            //已达到上限
            if (code.equals(201)) {
                return MapUtils.create("no", 0, "msg", "您选择的时间已约满，请重新预约时间");
            }

            if(followUpModel.getInterviewStatus() == 2){
                // 将面试时间修改同步到app
                Map<String, Object> ssbUpdateInterviewTimeMap = new HashMap<>();
                if(followUpModel.getId() != null && followUpModel.getBehaviorTime() != null){
                    ssbUpdateInterviewTimeMap.put("followId", followUpModel.getId());
                    ssbUpdateInterviewTimeMap.put("interviewTime", followUpModel.getBehaviorTime());
                    kafkaProducer.updateInterviewTimeFromCRM(ssbUpdateInterviewTimeMap);
                }
            }
        }

        InterviewRecord interviewRecordUpdate = new InterviewRecord();
        interviewRecordUpdate.setId(interviewRecord.getId());
        interviewRecordUpdate.setStatus(followUpModel.getInterviewStatus());
        //下轮面试 轮数增加 状态重置成 0
        if (followUpModel.getInterviewStatus() == 4) {
            interviewRecordUpdate.setNode(interviewRecord.getNode() + 1);
            interviewRecordUpdate.setStatus(0);
        }
        interviewRecordUpdate.setBehaviorTime(followUpModel.getBehaviorTime());
        interviewRecordUpdate.setUpdateBy(followUpModel.getUserId());
        interviewRecordUpdate.setUpdateTime(timestamp);
        interviewRecordUpdate.setRemark(followUpModel.getRemark());
        interviewRecordUpdate.setFailure(followUpModel.getFailure());
        interviewRecordUpdate.setReviewTime(timestamp);
        interviewRecordUpdate.setReviewUserId(followUpModel.getUserId());
        interviewRecordDAO.updateInterviewRecord(interviewRecordUpdate);

        //未参加、未通过、发offer  更新成审核通过状态
        if (followUpModel.getInterviewStatus() == 1 || followUpModel.getInterviewStatus() == 3 || followUpModel.getInterviewStatus() == 5) {
            InterviewRecord interviewRecordAuthUpdate = new InterviewRecord();
            interviewRecordAuthUpdate.setId(interviewRecord.getId());
            interviewRecordAuthUpdate.setAuth(1);
            interviewRecordAuthUpdate.setUpdateBy(followUpModel.getUserId());
            interviewRecordAuthUpdate.setUpdateTime(timestamp);
            interviewRecordDAO.updateInterviewAuth(interviewRecordAuthUpdate);
            //发offer 创建入职记录
            if (followUpModel.getInterviewStatus() == 5) {
                EntryRecord entryRecord = new EntryRecord();
                entryRecord.setEnterpriseId(interviewRecord.getEnterpriseId());
                entryRecord.setFollowId(interviewRecord.getFollowId());
                entryRecord.setCreateBy(interviewRecord.getCreateBy());
                entryRecord.setCreateTime(timestamp);
                entryRecord.setUpdateBy(followUpModel.getUserId());
                entryRecord.setUpdateTime(timestamp);
                entryRecord.setRemark(interviewRecord.getRemark());
                entryRecord.setAuthType(interviewRecord.getAuthType());
                // 保存入职信息
                entryRecordDAO.save(entryRecord);
            }

            // 将面试结果同步到app
            Map<String, Object> ssbInterviewMap = new HashMap<>();
            String phone = contactDAO.getPhone(followUpModel.getContactId());
            if(phone != null){
                ssbInterviewMap.put("phone", phone);
                // app表中对应状态  0/待反馈（企业未给出面试结果）、1/未参加、2/面试通过、3/面试不通过
                // interviewStatus 1未参加 2 改约时间 3 未通过 4 下轮面试 5 发offer
                if (followUpModel.getInterviewStatus() == 1) {
                    ssbInterviewMap.put("status", 1);
                } else if (followUpModel.getInterviewStatus() == 3) {
                    ssbInterviewMap.put("status", 3);
                    if(followUpModel.getFailure() != null){
                        ssbInterviewMap.put("failure", followUpModel.getFailure());
                    }
                } else if (followUpModel.getInterviewStatus() == 5) {
                    ssbInterviewMap.put("status", 2);
                }
                kafkaProducer.updateInterviewFromCRM(ssbInterviewMap);
            }
        }

        //更新主数据
        FollowUp followUp = new FollowUp();
        //未参加、未通过
        if (followUpModel.getInterviewStatus() == 1 || followUpModel.getInterviewStatus() == 3) {
            followUp.setFinish(1); //完成状态
        }
        // interviewStatus 1未参加 2 改约时间 3 未通过 4 下轮面试 5 发offer  (没办法做到统一  手动匹配)
        if (followUpModel.getInterviewStatus() == 1) {
            followUp.setCurrentStatus(2);
        } else if (followUpModel.getInterviewStatus() == 2) {
            followUp.setCurrentStatus(3);
        } else if (followUpModel.getInterviewStatus() == 3) {
            followUp.setCurrentStatus(4);
        } else if (followUpModel.getInterviewStatus() == 4) {
            followUp.setCurrentStatus(5);
        } else if (followUpModel.getInterviewStatus() == 5) {
            followUp.setCurrentStatus(6);
            followUp.setStatus(2);  //审核通过  状态变成  通过待入职
        }
        followUp.setUpdateBy(followUpModel.getUserId());
        followUp.setUpdateTime(timestamp);
        followUp.setId(interviewRecord.getFollowId());
        followUpDAO.updateFollowUp(followUp);
        //添加操作记录
        FollowLog followLog = new FollowLog();
        followLog.setEnterpriseId(followUpModel.getEnterpriseId());
        followLog.setFollowId(interviewRecord.getFollowId());
        followLog.setType(1);
        // interviewStatus 1未参加 2 改约时间 3 未通过 4 下轮面试 5 发offer  (没办法做到统一  手动匹配)
        if (followUpModel.getInterviewStatus() == 1) {
            followLog.setStatus(2);
        } else if (followUpModel.getInterviewStatus() == 2) {
            followLog.setStatus(3);
        } else if (followUpModel.getInterviewStatus() == 3) {
            followLog.setStatus(4);
        } else if (followUpModel.getInterviewStatus() == 4) {
            followLog.setStatus(5);
        } else if (followUpModel.getInterviewStatus() == 5) {
            followLog.setStatus(6);
        }
        followLog.setBehaviorTime(followUpModel.getBehaviorTime());
        followLog.setCreateBy(followUpModel.getUserId());
        followLog.setCreateTime(timestamp);
        followLog.setRemark(followUpModel.getRemark());
        followLog.setFailure(followUpModel.getFailure());
        followLogDAO.save(followLog);

        // app简历转化状态
        if (followUpModel.getInterviewStatus() == 3 || followUpModel.getInterviewStatus() == 5) {
            // 更新app用户转化结果
            int contactId = followUpDAO.getContactIdByFollowId(interviewRecord.getFollowId());
            resumeService.updateAppResume(contactId, 0, 1);
        }

        //解锁
        redisLockUtils.lock(1, followUpModel.getId(), followUpModel.getUserId(), 2);
        return MapUtils.create("no", 1, "msg", "跟进成功");
    }

    @Override
    public Map copyLink(Integer projectId, Integer interviewRecordId, Integer contactId, Integer userId, Integer enterpriseId) {
        if (projectId == null || interviewRecordId == null || contactId == null || userId == null || enterpriseId == null) {
            return MapUtils.create("no", 0, "msg", "参数异常");
        }

        CopyLinkVO copyLink = new CopyLinkVO();

        InterviewRecord interviewRecord = interviewRecordDAO.getInterviewRecordById(interviewRecordId);
        if(interviewRecord == null){
            return MapUtils.create("no", 0, "msg", "项目异常，请稍后重试。");
        }

        copyLink.setInterviewTime(interviewRecord.getBehaviorTime());

        Object projectResult = projectService.getProjectInfo(projectId);
        JSONObject jsonObjectResult = JSONObject.parseObject(JSON.toJSON(projectResult).toString());
        //判断返回参数是否正常
        if (jsonObjectResult.getInteger("no") != null && jsonObjectResult.getInteger("no").equals(200)) {
            JSONObject jsonObject = jsonObjectResult.getJSONObject("obj");

            copyLink.setEnterpriseName(jsonObject.getString("fullName"));
            copyLink.setInterviewPositionDetailedAddress(jsonObject.getString("interviewPositionDetailedAddress"));
            copyLink.setPositionName(jsonObject.getString("positionSecondName"));

        } else {
            return MapUtils.create("no", 0, "msg", "项目异常，请稍后重试。");
        }

        String phone = contactDAO.getPhone(contactId);

        // 生成微信小程序URL Link
        String urlLink = generateWeChatUrlLink(phone, projectId, interviewRecordId, contactId);
        if (urlLink != null) {
            copyLink.setLink(urlLink);
        } else {
            return MapUtils.create("no", 0, "msg", "项目异常，请稍后重试。");
        }

        return MapUtils.create("no", 1, "detail", copyLink);
    }

    /**
     * 生成微信小程序URL Link
     * todo 参数还不确定
     * @param phone 手机号
     * @param projectId 项目ID
     * @param interviewRecordId 面试记录ID
     * @param contactId 联系人ID
     * @return URL Link字符串，失败返回null
     */
    private String generateWeChatUrlLink(String phone, Integer projectId, Integer interviewRecordId, Integer contactId) {
        try {

            // 构建query参数，包含手机号和其他必要参数
            StringBuilder queryBuilder = new StringBuilder();
            if (phone != null && !phone.isEmpty()) {
                queryBuilder.append("phone=").append(phone);
            }
            if (projectId != null) {
                if (queryBuilder.length() > 0) {
                    queryBuilder.append("&");
                }
                queryBuilder.append("projectId=").append(projectId);
            }
            if (interviewRecordId != null) {
                if (queryBuilder.length() > 0) {
                    queryBuilder.append("&");
                }
                queryBuilder.append("interviewRecordId=").append(interviewRecordId);
            }
            if (contactId != null) {
                if (queryBuilder.length() > 0) {
                    queryBuilder.append("&");
                }
                queryBuilder.append("contactId=").append(contactId);
            }

            String query = queryBuilder.toString();

            // 调用WeChatUtil生成URL Link（使用默认30天过期时间）
            WeChatUtil.GenerateUrlLinkResponse response = weChatUtil.generateUrlLink(query);

            if (response != null && response.isSuccess()) {
                return response.getUrl_link();
            } else {
                // 记录错误日志
                String errorMsg = response != null ? response.getErrmsg() : "未知错误";
                System.err.println("生成微信URL Link失败: " + errorMsg);
                return null;
            }

        } catch (Exception e) {
            // 记录异常日志
            System.err.println("生成微信URL Link异常: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }
}
