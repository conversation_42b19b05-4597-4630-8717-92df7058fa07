package com.ssb.approve.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.ssb.approve.common.constants.Enum.NoticeReadEnum;
import com.ssb.approve.common.constants.Enum.NoticeStatusEnum;
import com.ssb.approve.common.constants.Enum.NoticeTypeEnum;
import com.ssb.approve.dao.*;
import com.ssb.approve.entity.*;
import com.ssb.approve.model.FollowUpModel;
import com.ssb.approve.model.QueryFollowUpModel;
import com.ssb.approve.model.vo.EntryRecordVO;
import com.ssb.approve.model.vo.FollowUpVO;
import com.ssb.approve.service.CommonService;
import com.ssb.approve.service.EnterpriseNoticeRecordService;
import com.ssb.approve.service.EntryRecordService;
import com.ssb.approve.service.NoticeRecordService;
import com.ssb.approve.service.client.ProjectService;
import com.ssb.approve.service.client.UserService;
import com.ssb.approve.utils.DateUtils;
import com.ssb.approve.utils.MapUtils;
import com.ssb.approve.utils.RedisLockUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @ClassName: EntryRecordServiceImpl
 * @Description: 入职记录 impl
 * @Author: YZK
 * @Date: 2019年12月25日16:32:05
 **/
@Slf4j
@Service
public class EntryRecordServiceImpl implements EntryRecordService {

    @Autowired
    private FollowUpDAO followUpDAO;

    @Autowired
    private EntryRecordDAO entryRecordDAO;

    @Autowired
    private FollowLogDAO followLogDAO;

    @Autowired
    private ProjectService projectService;

    @Autowired
    private SettleDetailsDAO settleDetailsDAO;

    @Autowired
    private RedisLockUtils redisLockUtils;

    @Autowired
    private UserService userService;

    @Autowired
    private CommonService commonService;

    @Autowired
    private FollowCommissionDAO followCommissionDAO;

    @Autowired
    private NoticeRecordService noticeRecordService;

    @Autowired
    private EnterpriseNoticeRecordService enterpriseNoticeRecordService;

    /**
     * 获取发offer列表
     *
     * @param queryFollowUpModel
     * @return
     */
    @Override
    public Map getOnlyOfferList(QueryFollowUpModel queryFollowUpModel) {
        Integer pageBegin = (queryFollowUpModel.getPageNum() - 1) * queryFollowUpModel.getPageSize();
        queryFollowUpModel.setPageBegin(pageBegin);
        List<FollowUpVO> followUps = followUpDAO.getOnlyOfferList(queryFollowUpModel);
        //装配 项目 预计 明细
        commonService.handleProjectExpectDetail(followUps);
        int count = followUpDAO.getOnlyOfferCount(queryFollowUpModel);
        return MapUtils.create("no", 1, "msg", "获取成功", "results", followUps, "count", count);
    }

    /**
     * 更新发offer 操作
     *
     * @param followUpModel
     * @return
     */
    @Override
    public Map updateOnlyOffer(FollowUpModel followUpModel) {
        //数据加锁  防止出现同时操作情况
        if (!redisLockUtils.lock(4, followUpModel.getId(), followUpModel.getUserId(), 1)) {
            return MapUtils.create("no", 0, "msg", "数据异常，请刷新后再操作！");
        }
        //当前时间
        Timestamp timestamp = new Timestamp(System.currentTimeMillis());
        //获取入职记录
        EntryRecord entryRecord = entryRecordDAO.getEntryRecordById(followUpModel.getId());
        if (entryRecord == null) {
            return MapUtils.create("no", 0, "msg", "数据异常更新失败");
        }
        //校验数据状态
        if (entryRecord.getStatus() != 0) {
            return MapUtils.create("no", 0, "msg", "入职记录已跟进");
        }

        if(followUpModel.getEntryStatus() == 0 || followUpModel.getEntryStatus() == 3){
            // 处理待办消息
            CrmNoticeRecord record = new CrmNoticeRecord();
            record.setNoticeType(NoticeTypeEnum.RECRUITMENT_FOLLOW_UP_APPOINTMENT_ENTRY_REMINDER.getValue());
            record.setTargetId(entryRecord.getFollowId());
            record.setTargetType(4);
            record.setStatus(NoticeStatusEnum.PROCESSED.getValue());
            record.setRead(NoticeReadEnum.READ.getValue());
            record.setUpdateBy(followUpModel.getUserId());
            record.setUpdateTime(new Timestamp(System.currentTimeMillis()));
            noticeRecordService.updateNoticeRecord(record);
        }

        EntryRecord entryRecordUpdate = new EntryRecord();
        entryRecordUpdate.setId(entryRecord.getId());
        entryRecordUpdate.setEstimateTime(followUpModel.getBehaviorTime());
        entryRecordUpdate.setStatus(followUpModel.getEntryStatus());
        // 发offer状态 拒绝入职  直接审核通过
        if (followUpModel.getEntryStatus() == 3) {
            entryRecordUpdate.setAuth(1);
        }
        entryRecordUpdate.setRemark(followUpModel.getRemark());
        entryRecordUpdate.setRefuse(followUpModel.getRefuse());
        entryRecordUpdate.setUpdateBy(followUpModel.getUserId());
        entryRecordUpdate.setUpdateTime(timestamp);
        entryRecordDAO.updateEntryRecord(entryRecordUpdate);

        //更新主数据
        FollowUp followUp = new FollowUp();
        followUp.setId(entryRecord.getFollowId());
        followUp.setStatus(2);
        // entryStatus 0 预约入职 1 已入职 2 更改入职时间 3 拒绝入职  (没办法做到统一  手动匹配)
        if (followUpModel.getEntryStatus() == 0) {
            followUp.setCurrentStatus(8);  //预约入职  默认和“更改时间”  在具体日志中会有 明确记录
        } else if (followUpModel.getEntryStatus() == 3) {
            followUp.setFinish(1); //  发offer状态 拒绝入职  直接标志为完成状态
            followUp.setCurrentStatus(9);
        }

        followUp.setUpdateBy(followUpModel.getUserId());
        followUp.setUpdateTime(timestamp);
        followUpDAO.updateFollowUp(followUp);

        //添加操作记录
        FollowLog followLog = new FollowLog();
        followLog.setEnterpriseId(followUpModel.getEnterpriseId());
        followLog.setFollowId(entryRecord.getFollowId());
        followLog.setType(2);
        followLog.setRemark(followUpModel.getRemark());
        followLog.setBehaviorTime(followUpModel.getBehaviorTime());
        followLog.setCreateBy(followUpModel.getUserId());
        followLog.setCreateTime(timestamp);
        followLog.setFailure(followUpModel.getRefuse());
        // entryStatus 0 预约入职 1 已入职 2 更改入职时间 3 拒绝入职  (没办法做到统一  手动匹配)
        if (followUpModel.getEntryStatus() == 0) {
            followLog.setStatus(8);
            followLog.setRemark("预约入职");
        } else if (followUpModel.getEntryStatus() == 3) {
            followLog.setStatus(9);
        }
        followLogDAO.save(followLog);

        //解锁
        redisLockUtils.lock(4, followUpModel.getId(), followUpModel.getUserId(), 2);
        return MapUtils.create("no", 1, "msg", "更新成功");
    }

    /**
     * 获取待入职列表
     *
     * @param queryFollowUpModel
     * @return
     */
    @Override
    public Map getEntryRecordList(QueryFollowUpModel queryFollowUpModel) {
        Integer pageBegin = (queryFollowUpModel.getPageNum() - 1) * queryFollowUpModel.getPageSize();
        queryFollowUpModel.setPageBegin(pageBegin);
        List<FollowUpVO> followUps = followUpDAO.getEntryRecordList(queryFollowUpModel);
        //装配 项目 预计 明细
        commonService.handleProjectExpectDetail(followUps);
        int count = followUpDAO.getEntryRecordCount(queryFollowUpModel);
        return MapUtils.create("no", 1, "msg", "获取成功", "results", followUps, "count", count);
    }

    /**
     * 更新入职结果
     *
     * @param followUpModel
     * @return
     */
    @Override
    public Map updateEntryRecord(FollowUpModel followUpModel) {
        //数据加锁  防止出现同时操作情况
        if (!redisLockUtils.lock(2, followUpModel.getId(), followUpModel.getUserId(), 1)) {
            return MapUtils.create("no", 0, "msg", "数据异常，请刷新后再操作！");
        }
        //当前时间
        Timestamp timestamp = new Timestamp(System.currentTimeMillis());
        //获取入职记录
        EntryRecord entryRecord = entryRecordDAO.getEntryRecordById(followUpModel.getId());
        if (entryRecord == null) {
            return MapUtils.create("no", 0, "msg", "数据异常更新失败");
        }
        // 默认  更改时间
        if (!(entryRecord.getStatus() == 0 || entryRecord.getStatus() == 2)) {
            return MapUtils.create("no", 0, "msg", "入职记录已跟进");
        }

        EntryRecord entryRecordUpdate = new EntryRecord();
        entryRecordUpdate.setId(entryRecord.getId());
        entryRecordUpdate.setEstimateTime(entryRecord.getEstimateTime()); // 保留之前的预估时间
        entryRecordUpdate.setBehaviorTime(entryRecord.getBehaviorTime()); // 保留之前的行为时间
        entryRecordUpdate.setStatus(followUpModel.getEntryStatus());
        //入职  修改实际入职时间
        if (followUpModel.getEntryStatus() == 1) {
            entryRecordUpdate.setBehaviorTime(followUpModel.getBehaviorTime());
        }
        //修改入职时间  修改预计入职时间
        if (followUpModel.getEntryStatus() == 2) {
            entryRecordUpdate.setEstimateTime(followUpModel.getBehaviorTime());
            entryRecordUpdate.setBehaviorTime(null); // 修改入职   重置行为时间
        }
        entryRecordUpdate.setRemark(followUpModel.getRemark());
        entryRecordUpdate.setRefuse(followUpModel.getRefuse());
        entryRecordUpdate.setUpdateBy(followUpModel.getUserId());
        entryRecordUpdate.setUpdateTime(timestamp);
        entryRecordDAO.updateEntryRecord(entryRecordUpdate);

        //更新主数据
        FollowUp followUp = new FollowUp();
        followUp.setId(entryRecord.getFollowId());
        followUp.setStatus(2);
        // entryStatus 1 已入职 2 更改入职时间 3 拒绝入职  (没办法做到统一  手动匹配)
        if (followUpModel.getEntryStatus() == 1) {
            followUp.setCurrentStatus(7);
        } else if (followUpModel.getEntryStatus() == 2) {
            followUp.setCurrentStatus(8);
        } else if (followUpModel.getEntryStatus() == 3) {
            followUp.setCurrentStatus(9);
        }
        followUp.setUpdateBy(followUpModel.getUserId());
        followUp.setUpdateTime(timestamp);
        followUpDAO.updateFollowUp(followUp);

        //添加操作记录
        FollowLog followLog = new FollowLog();
        followLog.setEnterpriseId(followUpModel.getEnterpriseId());
        followLog.setFollowId(entryRecord.getFollowId());
        followLog.setType(2);
        // entryStatus 1 已入职 2 更改入职时间 3 拒绝入职  (没办法做到统一  手动匹配)
        if (followUpModel.getEntryStatus() == 1) {
            followLog.setStatus(7);
        } else if (followUpModel.getEntryStatus() == 2) {
            followLog.setStatus(8);
        } else if (followUpModel.getEntryStatus() == 3) {
            followLog.setStatus(9);
        }

        followLog.setBehaviorTime(followUpModel.getBehaviorTime());
        followLog.setCreateBy(followUpModel.getUserId());
        followLog.setCreateTime(timestamp);
        followLog.setRemark(followUpModel.getRemark());
        followLog.setFailure(followUpModel.getRefuse());
        followLogDAO.save(followLog);
        //解锁
        redisLockUtils.lock(2, followUpModel.getId(), followUpModel.getUserId(), 2);
        return MapUtils.create("no", 1, "msg", "更新成功");
    }

    /**
     * 获取入职确认列表
     *
     * @param queryFollowUpModel
     * @return
     */
    @Override
    public Map getEntryAuthList(QueryFollowUpModel queryFollowUpModel) {
        Integer pageBegin = (queryFollowUpModel.getPageNum() - 1) * queryFollowUpModel.getPageSize();
        queryFollowUpModel.setPageBegin(pageBegin);
        List<EntryRecordVO> entryRecords = Lists.newArrayList();
        queryFollowUpModel.setAuthType(0);
        entryRecords = entryRecordDAO.getEntryAuthList(queryFollowUpModel);
        int count = entryRecordDAO.getEntryAuthCount(queryFollowUpModel);
        return MapUtils.create("no", 1, "msg", "获取成功", "results", entryRecords, "count", count);
    }

    /**
     * 入职确认 -- 审核操作更新
     *
     * @param id        入职记录id
     * @param userId    用户id
     * @return
     */
    @Override
    @Transactional
    public Map updateEntryAuth(Integer id, Integer userId) {
        //数据加锁  防止出现同时操作情况
        if (!redisLockUtils.lock(2, id, userId, 1)) {
            return MapUtils.create("no", 0, "msg", "数据异常，请刷新后再操作！");
        }
        //当前时间
        Timestamp timestamp = new Timestamp(System.currentTimeMillis());
        EntryRecord entryRecord = entryRecordDAO.getEntryRecordById(id);
        //入职状态
        Integer entryStatus = 0;
        if (entryRecord != null) {
            entryStatus = entryRecord.getStatus();
            //审核通过校验
            if (entryRecord.getAuth() == 1) {
                return MapUtils.create("no", 0, "msg", "入职结果已审核");
            }
            // 1 已入职  3 拒绝入职
            if (!(entryStatus == 1 || entryStatus == 3)) {
                return MapUtils.create("no", 0, "msg", "入职结果不是结束状态不能审核，请继续跟进。");
            }

            EntryRecord entryRecordUpdate = new EntryRecord();
            entryRecordUpdate.setId(entryRecord.getId());
            entryRecordUpdate.setAuth(1);
            entryRecordUpdate.setUpdateBy(userId);
            entryRecordUpdate.setUpdateTime(timestamp);
            entryRecordDAO.updateEntryAuth(entryRecordUpdate);

            FollowUp followUp = followUpDAO.getFollowUpById(entryRecord.getFollowId());
            //确认已入职 直接进入下个环节  新增 待结算记录
            if (entryStatus == 1) {

                Integer date, node; //天数  节点
                BigDecimal salary = BigDecimal.ZERO; //金额
                String time;// 几点
                List<SettleDetails> settleDetails = Lists.newArrayList();

                //取新建意向时保存的当时项目的结算节点   没有保存节点是  仍取项目当前结算节点信息
                FollowCommission followCommission = followCommissionDAO.getFollowCommission(entryRecord.getFollowId());
                if(followCommission != null && followCommission.getSettlementContent() != null && !followCommission.getSettlementContent().isEmpty()){
                    JSONArray settlementContentArray = JSONArray.parseArray(followCommission.getSettlementContent());
                    JSONObject settlementContentObject = null;

                    SettleDetails settleDetail;
                    for(int i=0;i<settlementContentArray.size();i++){
                        settlementContentObject = settlementContentArray.getJSONObject(i);

                        date = settlementContentObject.getInteger("date");
                        node = settlementContentObject.getInteger("node");
                        time = settlementContentObject.getString("time");
                        salary = settlementContentObject.getBigDecimal("salary");

                        settleDetail = new SettleDetails();
                        settleDetail.setEnterpriseId(entryRecord.getEnterpriseId());
                        settleDetail.setFollowId(entryRecord.getFollowId());
                        settleDetail.setNode(node);
                        settleDetail.setEntryTime(entryRecord.getBehaviorTime());
                        //计算结算时间
                        Date d = DateUtils.getDateBefore(entryRecord.getBehaviorTime(), -date + 1);
                        String dateStr = DateUtils.formatDate(d, "yyyy-MM-dd");
                        settleDetail.setSettleTime(new Timestamp(DateUtils.parseDate(dateStr + " " + time).getTime()));
                        settleDetail.setSalary(salary);
                        settleDetail.setDate(date);
                        settleDetail.setTime(time);
                        settleDetail.setCreateBy(entryRecord.getCreateBy());
                        settleDetail.setCreateTime(timestamp);
                        settleDetail.setUpdateBy(userId);
                        settleDetail.setUpdateTime(timestamp);
                        settleDetail.setAuthType(entryRecord.getAuthType());

                        settleDetails.add(settleDetail);
                    }

                }else{
                    // 保存待结算记录
                    Object result = projectService.getSettleDetails(followUp.getProjectId());
                    JSONObject jsonObjectResult = JSONObject.parseObject(JSON.toJSON(result).toString());
                    //判断返回参数是否正常
                    if (jsonObjectResult.getInteger("no") != null && jsonObjectResult.getInteger("no").equals(200)) {
                        //正常保存逻辑
                        JSONArray jsonArray = jsonObjectResult.getJSONArray("obj");
                        JSONObject settleDetailsObject = null;

                        SettleDetails settleDetail;
                        for (int i = 0; i < jsonArray.size(); i++) {
                            settleDetailsObject = jsonArray.getJSONObject(i);
                            date = settleDetailsObject.getInteger("date");
                            node = settleDetailsObject.getInteger("node");
                            time = settleDetailsObject.getString("time");
                            salary = settleDetailsObject.getBigDecimal("salary");

                            settleDetail = new SettleDetails();
                            settleDetail.setEnterpriseId(entryRecord.getEnterpriseId());
                            settleDetail.setFollowId(entryRecord.getFollowId());
                            settleDetail.setNode(node);
                            settleDetail.setEntryTime(entryRecord.getBehaviorTime());
                            //计算结算时间
                            Date d = DateUtils.getDateBefore(entryRecord.getBehaviorTime(), -date + 1);
                            String dateStr = DateUtils.formatDate(d, "yyyy-MM-dd");
                            settleDetail.setSettleTime(new Timestamp(DateUtils.parseDate(dateStr + " " + time).getTime()));
                            settleDetail.setSalary(salary);
                            settleDetail.setDate(date);
                            settleDetail.setTime(time);
                            settleDetail.setCreateBy(entryRecord.getCreateBy());
                            settleDetail.setCreateTime(timestamp);
                            settleDetail.setUpdateBy(userId);
                            settleDetail.setUpdateTime(timestamp);
                            settleDetail.setAuthType(entryRecord.getAuthType());

                            settleDetails.add(settleDetail);
                        }

                    } else {
                        log.error("CRM-PROJECT服务请求异常，数据回滚");
                        //异常情况 手动事务回滚机制
                        TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                        return MapUtils.create("no", 0, "msg", "审核异常，请稍后重试");
                    }
                }

                //保证集合有数据
                if (settleDetails.size() > 0) {
                    settleDetailsDAO.saveBatch(settleDetails);
                } else {
                    log.error("未获取到项目结算节点信息，数据回滚");
                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                    return MapUtils.create("no", 0, "msg", "审核异常，请稍后重试");
                }

            }
            //更新主数据
            //如果拒绝入职  更新成完成状态
            FollowUp followUpUpdate = new FollowUp();
            followUpUpdate.setId(entryRecord.getFollowId());
            if (entryStatus == 3) {
                followUpUpdate.setFinish(1);
            } else {
                //已入职状态   审核状态  变成 入职待结算
                followUpUpdate.setStatus(3);
            }
            followUpUpdate.setUpdateBy(userId);
            followUpUpdate.setUpdateTime(timestamp);
            followUpDAO.updateFollowUp(followUpUpdate);

            return MapUtils.create("no", 1, "msg", "审核成功");
        }
        //解锁
        redisLockUtils.lock(2, id, userId, 2);
        return MapUtils.create("no", 0, "msg", "审核异常，请稍后重试");
    }

    /**
     * 入职确认 -- 跟进操作
     *
     * @param followUpModel
     * @return
     */
    @Override
    @Transactional
    public Map entryAuthFollowUp(FollowUpModel followUpModel) {
        //数据加锁  防止出现同时操作情况
        if (!redisLockUtils.lock(2, followUpModel.getId(), followUpModel.getUserId(), 1)) {
            return MapUtils.create("no", 0, "msg", "数据异常，请刷新后再操作！");
        }
        //当前时间
        Timestamp timestamp = new Timestamp(System.currentTimeMillis());
        EntryRecord entryRecord = entryRecordDAO.getEntryRecordById(followUpModel.getId());
        if (entryRecord == null) {
            return MapUtils.create("no", 0, "msg", "数据异常更新失败");
        }
        //审核通过
        if (entryRecord.getAuth() == 1) {
            return MapUtils.create("no", 0, "msg", "审核通过不能跟进，请刷新页面");
        }

        // 处理待办消息
        CrmNoticeRecord record = new CrmNoticeRecord();
        record.setNoticeType(NoticeTypeEnum.HEADHUNTER_REVIEW_EMPLOYMENT_RESULT_CONFIRM.getValue());
        record.setTargetId(followUpModel.getId());
        record.setTargetType(2);
        record.setStatus(NoticeStatusEnum.PROCESSED.getValue());
        record.setRead(NoticeReadEnum.READ.getValue());
        record.setUpdateBy(followUpModel.getUserId());
        record.setUpdateTime(new Timestamp(System.currentTimeMillis()));
        noticeRecordService.updateNoticeRecord(record);

        FollowUp followUp = followUpDAO.getFollowUpById(entryRecord.getFollowId());
        //处理企业端待办消息
        CrmEnterpriseNoticeRecord recordEnterprise = new CrmEnterpriseNoticeRecord();
        recordEnterprise.setContactId(followUp.getContactId());
        recordEnterprise.setProjectId(followUp.getProjectId());
        recordEnterprise.setNoticeType(3);
        recordEnterprise.setType(1);
        recordEnterprise.setStatus(4);
        recordEnterprise.setRead(1);
        recordEnterprise.setUpdateBy(followUpModel.getUserId());
        recordEnterprise.setUpdateTime(new Timestamp(System.currentTimeMillis()));
        enterpriseNoticeRecordService.updateNoticeRecord(recordEnterprise);

        EntryRecord entryRecordUpdate = new EntryRecord();
        entryRecordUpdate.setId(entryRecord.getId());
        entryRecordUpdate.setStatus(followUpModel.getEntryStatus());
        entryRecordUpdate.setEstimateTime(entryRecord.getEstimateTime()); // 预估入职时间
        entryRecordUpdate.setBehaviorTime(entryRecord.getBehaviorTime()); // 保留之前的行为时间
        //入职
        if (followUpModel.getEntryStatus() == 1) {
            entryRecordUpdate.setBehaviorTime(followUpModel.getBehaviorTime());
        }
        //更改入职时间
        if (followUpModel.getEntryStatus() == 2) {
            entryRecordUpdate.setEstimateTime(followUpModel.getBehaviorTime());
            entryRecordUpdate.setBehaviorTime(null); // 修改入职 重置时间
        }
        entryRecordUpdate.setRemark(followUpModel.getRemark());
        entryRecordUpdate.setRefuse(followUpModel.getRefuse());
        entryRecordUpdate.setUpdateBy(followUpModel.getUserId());
        entryRecordUpdate.setUpdateTime(timestamp);
        entryRecordDAO.updateEntryRecord(entryRecordUpdate);

        //更新成完成状态
        //1 已入职 3 拒绝入职
        if (followUpModel.getEntryStatus() == 1 || followUpModel.getEntryStatus() == 3) {
            EntryRecord entryRecordAuthUpdate = new EntryRecord();
            entryRecordAuthUpdate.setId(entryRecord.getId());
            entryRecordAuthUpdate.setAuth(1);
            entryRecordAuthUpdate.setUpdateBy(followUpModel.getUserId());
            entryRecordAuthUpdate.setUpdateTime(timestamp);
            entryRecordDAO.updateEntryAuth(entryRecordAuthUpdate);
            //已入职  创建待结算记录
            if (followUpModel.getEntryStatus() == 1) {

                Integer date, node; //天数  节点
                BigDecimal salary = BigDecimal.ZERO; // 金额
                String time;// 几点
                List<SettleDetails> settleDetails = Lists.newArrayList();

                //取新建意向时保存的当时项目的结算节点   没有保存节点是  扔取项目当前结算节点信息
                FollowCommission followCommission = followCommissionDAO.getFollowCommission(entryRecord.getFollowId());
                if(followCommission != null && followCommission.getSettlementContent() != null && !followCommission.getSettlementContent().isEmpty()){
                    JSONArray settlementContentArray = JSONArray.parseArray(followCommission.getSettlementContent());
                    JSONObject settlementContentObject = null;

                    SettleDetails settleDetail;
                    for(int i=0;i<settlementContentArray.size();i++){
                        settlementContentObject = settlementContentArray.getJSONObject(i);

                        date = settlementContentObject.getInteger("date");
                        node = settlementContentObject.getInteger("node");
                        time = settlementContentObject.getString("time");
                        salary = settlementContentObject.getBigDecimal("salary");

                        settleDetail = new SettleDetails();
                        settleDetail.setEnterpriseId(entryRecord.getEnterpriseId());
                        settleDetail.setFollowId(entryRecord.getFollowId());
                        settleDetail.setNode(node);
                        settleDetail.setEntryTime(followUpModel.getBehaviorTime());
                        //计算结算时间
                        Date d = DateUtils.getDateBefore(followUpModel.getBehaviorTime(), -date + 1);
                        String dateStr = DateUtils.formatDate(d, "yyyy-MM-dd");
                        settleDetail.setSettleTime(new Timestamp(DateUtils.parseDate(dateStr + " " + time).getTime()));
                        settleDetail.setSalary(salary);
                        settleDetail.setDate(date);
                        settleDetail.setTime(time);
                        settleDetail.setCreateBy(entryRecord.getCreateBy());
                        settleDetail.setCreateTime(timestamp);
                        settleDetail.setUpdateBy(followUpModel.getUserId());
                        settleDetail.setUpdateTime(timestamp);
                        settleDetail.setAuthType(entryRecord.getAuthType());

                        settleDetails.add(settleDetail);
                    }
                }else{
                    // 保存待结算记录
                    Object result = projectService.getSettleDetails(followUp.getProjectId());
                    JSONObject jsonObjectResult = JSONObject.parseObject(JSON.toJSON(result).toString());
                    //判断返回参数是否正常
                    if (jsonObjectResult.getInteger("no") != null && jsonObjectResult.getInteger("no").equals(200)) {
                        //正常保存逻辑
                        JSONArray jsonArray = jsonObjectResult.getJSONArray("obj");
                        JSONObject settleDetailsObject = null;

                        SettleDetails settleDetail;
                        for (int i = 0; i < jsonArray.size(); i++) {
                            settleDetailsObject = jsonArray.getJSONObject(i);
                            date = settleDetailsObject.getInteger("date");
                            node = settleDetailsObject.getInteger("node");
                            time = settleDetailsObject.getString("time");
                            salary = settleDetailsObject.getBigDecimal("salary");

                            settleDetail = new SettleDetails();
                            settleDetail.setEnterpriseId(entryRecord.getEnterpriseId());
                            settleDetail.setFollowId(entryRecord.getFollowId());
                            settleDetail.setNode(node);
                            settleDetail.setEntryTime(followUpModel.getBehaviorTime());
                            //计算结算时间
                            Date d = DateUtils.getDateBefore(followUpModel.getBehaviorTime(), -date + 1);
                            String dateStr = DateUtils.formatDate(d, "yyyy-MM-dd");
                            settleDetail.setSettleTime(new Timestamp(DateUtils.parseDate(dateStr + " " + time).getTime()));
                            settleDetail.setSalary(salary);
                            settleDetail.setDate(date);
                            settleDetail.setTime(time);
                            settleDetail.setCreateBy(entryRecord.getCreateBy());
                            settleDetail.setCreateTime(timestamp);
                            settleDetail.setUpdateBy(followUpModel.getUserId());
                            settleDetail.setUpdateTime(timestamp);
                            settleDetail.setAuthType(entryRecord.getAuthType());

                            settleDetails.add(settleDetail);
                        }

                    } else {
                        log.error("CRM-PROJECT服务请求异常，数据回滚");
                        //解锁
                        redisLockUtils.lock(2, followUpModel.getId(), followUpModel.getUserId(), 2);
                        //异常情况 手动事务回滚机制
                        TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                        return MapUtils.create("no", 0, "msg", "审核异常，请稍后重试");
                    }
                }

                //保证集合有数据
                if (settleDetails.size() > 0) {
                    settleDetailsDAO.saveBatch(settleDetails);
                } else {
                    log.error("未获取到项目结算节点信息，数据回滚");
                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                    return MapUtils.create("no", 0, "msg", "审核异常，请稍后重试");
                }
            }
        }

        //更新主数据
        FollowUp followUpUpdate = new FollowUp();
        followUpUpdate.setId(entryRecord.getFollowId());
        //未参加、未通过
        if (followUpModel.getEntryStatus() == 3) {
            followUpUpdate.setFinish(1); //完成状态
        }
        // entryStatus 1 已入职 2 更改入职时间 3 拒绝入职  (没办法做到统一  手动匹配)
        if (followUpModel.getEntryStatus() == 1) {
            followUpUpdate.setCurrentStatus(7);
            followUpUpdate.setStatus(3);
        } else if (followUpModel.getEntryStatus() == 2) {
            followUpUpdate.setCurrentStatus(8);
        } else if (followUpModel.getEntryStatus() == 3) {
            followUpUpdate.setCurrentStatus(9);
        }
        followUpUpdate.setUpdateBy(followUpModel.getUserId());
        followUpUpdate.setUpdateTime(timestamp);

        followUpDAO.updateFollowUp(followUpUpdate);

        //添加操作记录
        FollowLog followLog = new FollowLog();
        followLog.setEnterpriseId(followUpModel.getEnterpriseId());
        followLog.setFollowId(entryRecord.getFollowId());
        followLog.setType(2);
        // entryStatus 1 已入职 2 更改入职时间 3 拒绝入职  (没办法做到统一  手动匹配)
        if (followUpModel.getEntryStatus() == 1) {
            followLog.setStatus(7);
        } else if (followUpModel.getEntryStatus() == 2) {
            followLog.setStatus(8);
        } else if (followUpModel.getEntryStatus() == 3) {
            followLog.setStatus(9);
        }
        followLog.setBehaviorTime(followUpModel.getBehaviorTime());
        followLog.setCreateBy(followUpModel.getUserId());
        followLog.setCreateTime(timestamp);
        followLog.setRemark(followUpModel.getRemark());
        followLog.setFailure(followUpModel.getRefuse());
        followLogDAO.save(followLog);
        //解锁
        redisLockUtils.lock(2, followUpModel.getId(), followUpModel.getUserId(), 2);
        return MapUtils.create("no", 1, "msg", "跟进成功");
    }

    @Override
    public Map getEnterpriseProjectEntryAuthList(QueryFollowUpModel queryFollowUpModel) {
        List<EntryRecordVO> entryRecords = entryRecordDAO.getEnterpriseProjectEntryAuthList(queryFollowUpModel);
        int count = entryRecordDAO.getEnterpriseProjectEntryAuthCount(queryFollowUpModel);
        return MapUtils.create("no", 1, "msg", "获取成功", "results", entryRecords, "count", count);
    }

    /**
     * 入职确认 -- 跟进操作
     *
     * @param followUpModel
     * @return
     */
    @Override
    @Transactional
    public Map entryEnterpriseAuthFollowUp(FollowUpModel followUpModel) {
        //数据加锁  防止出现同时操作情况
        if (!redisLockUtils.lock(2, followUpModel.getId(), followUpModel.getUserId(), 1)) {
            return MapUtils.create("no", 0, "msg", "数据异常，请刷新后再操作！");
        }
        //当前时间
        Timestamp timestamp = new Timestamp(System.currentTimeMillis());
        EntryRecord entryRecord = entryRecordDAO.getEntryRecordById(followUpModel.getId());
        if (entryRecord == null) {
            return MapUtils.create("no", 0, "msg", "数据异常更新失败");
        }
        //审核通过
        if (entryRecord.getAuth() == 1) {
            return MapUtils.create("no", 0, "msg", "审核通过不能跟进，请刷新页面");
        }

        // 处理待办消息
        CrmNoticeRecord record = new CrmNoticeRecord();
        record.setNoticeType(NoticeTypeEnum.HEADHUNTER_REVIEW_EMPLOYMENT_RESULT_CONFIRM.getValue());
        record.setTargetId(followUpModel.getId());
        record.setTargetType(2);
        record.setStatus(NoticeStatusEnum.PROCESSED.getValue());
        record.setRead(NoticeReadEnum.READ.getValue());
        record.setUpdateBy(followUpModel.getUserId());
        record.setUpdateTime(new Timestamp(System.currentTimeMillis()));
        noticeRecordService.updateNoticeRecord(record);

        //处理企业端待办消息
        FollowUp followUp = followUpDAO.getFollowUpById(entryRecord.getFollowId());
        CrmEnterpriseNoticeRecord recordEnterprise = new CrmEnterpriseNoticeRecord();
        recordEnterprise.setContactId(followUp.getContactId());
        recordEnterprise.setProjectId(followUp.getProjectId());
        recordEnterprise.setNoticeType(3);
        recordEnterprise.setType(1);
        recordEnterprise.setStatus(4);
        recordEnterprise.setRead(1);
        recordEnterprise.setUpdateBy(followUpModel.getUserId());
        recordEnterprise.setUpdateTime(new Timestamp(System.currentTimeMillis()));
        enterpriseNoticeRecordService.updateNoticeRecord(recordEnterprise);

        EntryRecord entryRecordUpdate = new EntryRecord();
        entryRecordUpdate.setId(entryRecord.getId());
        entryRecordUpdate.setStatus(followUpModel.getEntryStatus());
        entryRecordUpdate.setEstimateTime(entryRecord.getEstimateTime()); // 预估入职时间
        entryRecordUpdate.setBehaviorTime(entryRecord.getBehaviorTime()); // 保留之前的行为时间
        //入职
        if (followUpModel.getEntryStatus() == 1) {
            entryRecordUpdate.setBehaviorTime(followUpModel.getBehaviorTime());
        }
        //更改入职时间
        if (followUpModel.getEntryStatus() == 2) {
            entryRecordUpdate.setEstimateTime(followUpModel.getBehaviorTime());
            entryRecordUpdate.setBehaviorTime(null); // 修改入职 重置时间
        }
        entryRecordUpdate.setRemark(followUpModel.getRemark());
        entryRecordUpdate.setRefuse(followUpModel.getRefuse());
        entryRecordUpdate.setUpdateBy(followUpModel.getUserId());
        entryRecordUpdate.setUpdateTime(timestamp);
        entryRecordUpdate.setReviewTime(timestamp);
        entryRecordUpdate.setReviewUserId(followUpModel.getUserId());
        entryRecordDAO.updateEntryRecord(entryRecordUpdate);

        //更新成完成状态
        //1 已入职 3 拒绝入职
        if (followUpModel.getEntryStatus() == 1 || followUpModel.getEntryStatus() == 3) {
            EntryRecord entryRecordAuthUpdate = new EntryRecord();
            entryRecordAuthUpdate.setId(entryRecord.getId());
            entryRecordAuthUpdate.setAuth(1);
            entryRecordAuthUpdate.setUpdateBy(followUpModel.getUserId());
            entryRecordAuthUpdate.setUpdateTime(timestamp);
            entryRecordDAO.updateEntryAuth(entryRecordAuthUpdate);
            //已入职  创建待结算记录
            if (followUpModel.getEntryStatus() == 1) {

                Integer date, node; //天数  节点 金额
                BigDecimal salary = BigDecimal.ZERO;
                String time;// 几点
                List<SettleDetails> settleDetails = Lists.newArrayList();

                //取新建意向时保存的当时项目的结算节点   没有保存节点是  扔取项目当前结算节点信息
                FollowCommission followCommission = followCommissionDAO.getFollowCommission(entryRecord.getFollowId());
                if(followCommission != null && followCommission.getSettlementContent() != null && !followCommission.getSettlementContent().isEmpty()){
                    JSONArray settlementContentArray = JSONArray.parseArray(followCommission.getSettlementContent());
                    JSONObject settlementContentObject = null;

                    SettleDetails settleDetail;
                    for(int i=0;i<settlementContentArray.size();i++){
                        settlementContentObject = settlementContentArray.getJSONObject(i);

                        date = settlementContentObject.getInteger("date");
                        node = settlementContentObject.getInteger("node");
                        time = settlementContentObject.getString("time");
                        salary = settlementContentObject.getBigDecimal("salary");

                        settleDetail = new SettleDetails();
                        settleDetail.setEnterpriseId(entryRecord.getEnterpriseId());
                        settleDetail.setFollowId(entryRecord.getFollowId());
                        settleDetail.setNode(node);
                        settleDetail.setEntryTime(followUpModel.getBehaviorTime());
                        //计算结算时间
                        Date d = DateUtils.getDateBefore(followUpModel.getBehaviorTime(), -date + 1);
                        String dateStr = DateUtils.formatDate(d, "yyyy-MM-dd");
                        settleDetail.setSettleTime(new Timestamp(DateUtils.parseDate(dateStr + " " + time).getTime()));
                        settleDetail.setSalary(salary);
                        settleDetail.setDate(date);
                        settleDetail.setTime(time);
                        settleDetail.setCreateBy(entryRecord.getCreateBy());
                        settleDetail.setCreateTime(timestamp);
                        settleDetail.setUpdateBy(followUpModel.getUserId());
                        settleDetail.setUpdateTime(timestamp);
                        settleDetail.setAuthType(entryRecord.getAuthType());

                        settleDetails.add(settleDetail);
                    }
                }else{
                    // 保存待结算记录
                    Object result = projectService.getSettleDetails(followUp.getProjectId());
                    JSONObject jsonObjectResult = JSONObject.parseObject(JSON.toJSON(result).toString());
                    //判断返回参数是否正常
                    if (jsonObjectResult.getInteger("no") != null && jsonObjectResult.getInteger("no").equals(200)) {
                        //正常保存逻辑
                        JSONArray jsonArray = jsonObjectResult.getJSONArray("obj");
                        JSONObject settleDetailsObject = null;

                        SettleDetails settleDetail;
                        for (int i = 0; i < jsonArray.size(); i++) {
                            settleDetailsObject = jsonArray.getJSONObject(i);
                            date = settleDetailsObject.getInteger("date");
                            node = settleDetailsObject.getInteger("node");
                            time = settleDetailsObject.getString("time");
                            salary = settleDetailsObject.getBigDecimal("salary");

                            settleDetail = new SettleDetails();
                            settleDetail.setEnterpriseId(entryRecord.getEnterpriseId());
                            settleDetail.setFollowId(entryRecord.getFollowId());
                            settleDetail.setNode(node);
                            settleDetail.setEntryTime(followUpModel.getBehaviorTime());
                            //计算结算时间
                            Date d = DateUtils.getDateBefore(followUpModel.getBehaviorTime(), -date + 1);
                            String dateStr = DateUtils.formatDate(d, "yyyy-MM-dd");
                            settleDetail.setSettleTime(new Timestamp(DateUtils.parseDate(dateStr + " " + time).getTime()));
                            settleDetail.setSalary(salary);
                            settleDetail.setDate(date);
                            settleDetail.setTime(time);
                            settleDetail.setCreateBy(entryRecord.getCreateBy());
                            settleDetail.setCreateTime(timestamp);
                            settleDetail.setUpdateBy(followUpModel.getUserId());
                            settleDetail.setUpdateTime(timestamp);
                            settleDetail.setAuthType(entryRecord.getAuthType());

                            settleDetails.add(settleDetail);
                        }

                    } else {
                        log.error("CRM-PROJECT服务请求异常，数据回滚");
                        //解锁
                        redisLockUtils.lock(2, followUpModel.getId(), followUpModel.getUserId(), 2);
                        //异常情况 手动事务回滚机制
                        TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                        return MapUtils.create("no", 0, "msg", "审核异常，请稍后重试");
                    }
                }

                //保证集合有数据
                if (settleDetails.size() > 0) {
                    settleDetailsDAO.saveBatch(settleDetails);
                } else {
                    log.error("未获取到项目结算节点信息，数据回滚");
                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                    return MapUtils.create("no", 0, "msg", "审核异常，请稍后重试");
                }
            }
        }

        //更新主数据
        FollowUp followUpUpdate = new FollowUp();
        followUpUpdate.setId(entryRecord.getFollowId());
        //未参加、未通过
        if (followUpModel.getEntryStatus() == 3) {
            followUpUpdate.setFinish(1); //完成状态
        }
        // entryStatus 1 已入职 2 更改入职时间 3 拒绝入职  (没办法做到统一  手动匹配)
        if (followUpModel.getEntryStatus() == 1) {
            followUpUpdate.setCurrentStatus(7);
            followUpUpdate.setStatus(3);
        } else if (followUpModel.getEntryStatus() == 2) {
            followUpUpdate.setCurrentStatus(8);
        } else if (followUpModel.getEntryStatus() == 3) {
            followUpUpdate.setCurrentStatus(9);
        }
        followUpUpdate.setUpdateBy(followUpModel.getUserId());
        followUpUpdate.setUpdateTime(timestamp);

        followUpDAO.updateFollowUp(followUpUpdate);

        //添加操作记录
        FollowLog followLog = new FollowLog();
        followLog.setEnterpriseId(followUpModel.getEnterpriseId());
        followLog.setFollowId(entryRecord.getFollowId());
        followLog.setType(2);
        // entryStatus 1 已入职 2 更改入职时间 3 拒绝入职  (没办法做到统一  手动匹配)
        if (followUpModel.getEntryStatus() == 1) {
            followLog.setStatus(7);
        } else if (followUpModel.getEntryStatus() == 2) {
            followLog.setStatus(8);
        } else if (followUpModel.getEntryStatus() == 3) {
            followLog.setStatus(9);
        }
        followLog.setBehaviorTime(followUpModel.getBehaviorTime());
        followLog.setCreateBy(followUpModel.getUserId());
        followLog.setCreateTime(timestamp);
        followLog.setRemark(followUpModel.getRemark());
        followLog.setFailure(followUpModel.getRefuse());
        followLogDAO.save(followLog);
        //解锁
        redisLockUtils.lock(2, followUpModel.getId(), followUpModel.getUserId(), 2);
        return MapUtils.create("no", 1, "msg", "跟进成功");
    }
}
