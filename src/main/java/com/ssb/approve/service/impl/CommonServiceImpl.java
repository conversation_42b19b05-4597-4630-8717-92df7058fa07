package com.ssb.approve.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.ssb.approve.dao.*;
import com.ssb.approve.entity.FollowCommission;
import com.ssb.approve.entity.FollowUp;
import com.ssb.approve.model.QueryFollowUpModel;
import com.ssb.approve.model.vo.CardUserVO;
import com.ssb.approve.model.vo.FollowUpVO;
import com.ssb.approve.service.CommonService;
import com.ssb.approve.service.client.ProjectService;
import com.ssb.approve.service.client.UserService;
import com.ssb.approve.utils.DateUtils;
import com.ssb.approve.utils.MapUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @ClassName: CommonServiceImpl
 * @Description: 公共 impl
 * @Author: YZK
 * @Date: 2020年01月14日14:57:31
 **/
@Slf4j
@Service
public class CommonServiceImpl implements CommonService {

    @Autowired
    private FollowUpDAO followUpDAO;

    @Autowired
    private ProjectService projectService;

    @Autowired
    private EntryRecordDAO entryRecordDAO;

    @Autowired
    private SettleDetailsDAO settleDetailsDAO;

    @Autowired
    private InterviewRecordDAO interviewRecordDAO;

    @Autowired
    private FollowCommissionDAO followCommissionDAO;

    @Autowired
    private UserService userService;

    /**
     * 检测项目暂停状态
     *
     * @param queryFollowUpModel
     * @return
     */
    @Override
    public Map checkSuspend(QueryFollowUpModel queryFollowUpModel) {
        //跟进详情
        FollowUp followUp = followUpDAO.getFollowUpById(queryFollowUpModel.getId());
        if (followUp == null) {
            return MapUtils.create("no", 0, "msg", "数据异常，请稍后重试！");
        }
        //确保是当前公司数据
        // 审核分享项目时，存在企业id不为当前公司情况
//        if (!followUp.getEnterpriseId().equals(queryFollowUpModel.getEnterpriseId())) {
//            return MapUtils.create("no", 0, "msg", "数据异常，禁止操作！");
//        }
        //获取项目详情信息
        Object result = projectService.getProjectInfo(followUp.getProjectId());
        JSONObject jsonObjectResult = JSONObject.parseObject(JSON.toJSON(result).toString());
        //判断返回参数是否正常
        if (jsonObjectResult.getInteger("no") != null && jsonObjectResult.getInteger("no").equals(200)) {
            //正常保存逻辑
            JSONObject jsonObject = jsonObjectResult.getJSONObject("obj");
            if (jsonObject != null) {
                // 0 默认 1 暂停
                int suspend = jsonObject.getInteger("suspend");
                //公司名称
                String enterpriseName = jsonObject.getString("fullName");
                //公司标题
                String title = jsonObject.getString("title");

                //招聘名额
                Integer recruitCount = jsonObject.getInteger("recruitCount");
                //入职结算名额   完全结算才算
                int settleCount = followUpDAO.getSettleCount(followUp.getProjectId());
                //待入职数量
                int waitEntryCount = entryRecordDAO.getWaitEntryCount(followUp.getProjectId());
                //待结算数量
                int waitSettleCount = settleDetailsDAO.getWaitSettleCount(followUp.getProjectId());
                //待面试数量
                int waitInterviewCount = interviewRecordDAO.getWaitInterviewCount(followUp.getProjectId());
                //剩余可招人数
                int surplusNum = recruitCount - settleCount;
                //当前正常状态
                // 暂停机制  剩余名额-待入职人数-待结算人数-待面试人数/5<=0
                if (suspend == 0) {
                    //亏损状态
                    if ((recruitCount - settleCount - waitEntryCount - waitSettleCount - waitInterviewCount / 5) <= 0) {
                        return MapUtils.create("no", -1, "suspend", suspend, "display", true, "surplusNum", surplusNum, "waitEntryCount", waitEntryCount, "waitSettleCount", waitSettleCount,
                                "waitInterviewCount", waitInterviewCount, "enterpriseName", enterpriseName, "title", title);
                    }
                }
                //已暂停
                // 回复机制  剩余名额-待入职人数-待结算人数-待面试人数/5>0
                if (suspend == 1) {
                    // 如果是续约项目，只有结算最新一期，才会触发恢复弹窗。
                    if (null != jsonObject.getInteger("lastId") && jsonObject.getInteger("lastId").equals(followUp.getProjectId())) {
                        //盈利状态
                        if ((recruitCount - settleCount - waitEntryCount - waitSettleCount - waitInterviewCount / 5) > 0) {
                            return MapUtils.create("no", -1, "suspend", suspend, "display", true, "surplusNum", surplusNum, "waitEntryCount", waitEntryCount, "waitSettleCount", waitSettleCount,
                                    "waitInterviewCount", waitInterviewCount, "enterpriseName", enterpriseName, "title", title);
                        }
                    }
                }
            }
        } else {
            return MapUtils.create("no", 0, "msg", "数据异常,请联系管理员！");
        }
        return MapUtils.create("no", 1, "msg", "检测成功");
    }

    @Override
    public List<FollowUpVO> handleProjectExpectDetail(List<FollowUpVO> followUpVOList) {
        for (FollowUpVO followUp : followUpVOList) {
            Integer userType = followUpDAO.getFollowUpUserCommissionInfoById(followUp.getFollowUpId());
            // 跟进 提成
            FollowCommission followCommission = followCommissionDAO.getFollowCommission(followUp.getFollowUpId());
            // 空集合
            if (null == followCommission || StringUtils.isBlank(followCommission.getRateContent())) {
                continue;
            }

            //获取跟进创建时的 项目节点信息 为空则取项目当前节点信息
            JSONArray jsonProjectSettleArray = null;
            if (followCommission.getSettlementContent() != null) {
                jsonProjectSettleArray = JSONArray.parseArray(followCommission.getSettlementContent());
            } else {
                Object result = projectService.getSettleDetails(followUp.getProjectId());
                JSONObject jsonObjectResult = JSONObject.parseObject(JSON.toJSON(result).toString());
                //判断返回参数是否正常
                if (jsonObjectResult.getInteger("no") != null && jsonObjectResult.getInteger("no").equals(200)) {
                    jsonProjectSettleArray = jsonObjectResult.getJSONArray("obj");
                }
            }

            // 预计提成
            List<Map<String, Object>> expectCommissionDetails = Lists.newArrayList();
            //提成总计
            BigDecimal expectCommissionTotal = new BigDecimal(0.00);
            // 积分总计
            Integer expectIntegralTotal = 0;
            //判断返回参数是否正常
            if (jsonProjectSettleArray != null) {
                JSONObject settleDetailsObject = null;
                Integer date, node; //天数  节点
                BigDecimal salary = BigDecimal.ZERO;
                Map<String, Object> expectCommissionDetail;
                for (int i = 0; i < jsonProjectSettleArray.size(); i++) {

                    expectCommissionDetail = Maps.newHashMap();

                    settleDetailsObject = jsonProjectSettleArray.getJSONObject(i);
                    date = settleDetailsObject.getInteger("date");
                    node = settleDetailsObject.getInteger("node");
                    salary = settleDetailsObject.getBigDecimal("salary");

                    if(userType == 1){
                        JSONObject rateContentObject = JSONObject.parseObject(followCommission.getRateContent());
                        BigDecimal rate = rateContentObject.getJSONObject("headhunterMap").getBigDecimal("projectCommissionRatio").compareTo(BigDecimal.ZERO) > 0 ? rateContentObject.getJSONObject("headhunterMap").getBigDecimal("projectCommissionRatio"):rateContentObject.getJSONObject("headhunterMap").getBigDecimal("userCommissionRatio");
                        BigDecimal bigDecimalSalary = rate.multiply(salary);
                        expectCommissionDetail.put("salary", bigDecimalSalary);
                        // 累计 提成金额
                        expectCommissionTotal = expectCommissionTotal.add(bigDecimalSalary);
                    }else{
                        BigDecimal bigDecimalSalary = salary;
                        expectCommissionDetail.put("salary", bigDecimalSalary);
                        // 累计 提成金额
                        expectCommissionTotal = expectCommissionTotal.add(bigDecimalSalary);
                    }
                    expectCommissionDetail.put("data", date);
                    expectCommissionDetail.put("node", node);
                    expectCommissionDetails.add(expectCommissionDetail);
                }
            }
            followUp.setExpectCommissionDetail(expectCommissionDetails);
            followUp.setExpectCommission(expectCommissionTotal);
        }

        return followUpVOList;
    }

    @Override
    public List<FollowUpVO> singleNodeHandleProjectExpectDetail(List<FollowUpVO> followUpVOList) {

        for (FollowUpVO followUp : followUpVOList) {
            Integer userType = followUpDAO.getFollowUpUserCommissionInfoById(followUp.getFollowUpId());
            if(userType == 1){
                FollowCommission followCommission = followCommissionDAO.getFollowCommission(followUp.getFollowUpId());
                // 空集合
                if (null == followCommission || StringUtils.isBlank(followCommission.getRateContent())) {
                    continue;
                }
                JSONObject rateContentObject = JSONObject.parseObject(followCommission.getRateContent());
                BigDecimal rate = rateContentObject.getJSONObject("headhunterMap").getBigDecimal("projectCommissionRatio").compareTo(BigDecimal.ZERO) > 0 ? rateContentObject.getJSONObject("headhunterMap").getBigDecimal("projectCommissionRatio"):rateContentObject.getJSONObject("headhunterMap").getBigDecimal("userCommissionRatio");

                //提成总计
                BigDecimal expectCommissionTotal = rate.multiply(new BigDecimal(followUp.getSalary()));

                followUp.setExpectCommission(expectCommissionTotal);
            }
        }
        return followUpVOList;
    }

    @Override
    public CardUserVO getCardUserInfo(Integer userId, Integer eid, Integer deptId, Integer type) {
        Object cardObject = userService.getCardUserInfo(userId, eid, deptId == null ? 0 :deptId, type);
        JSONObject cardObjectResult = JSONObject.parseObject(JSON.toJSON(cardObject).toString());

        if(!(cardObjectResult.getInteger("no") != null && cardObjectResult.getInteger("no").equals(200))){
            log.error("获取用户信息出错，userId" + userId);
            return null;
        }

        JSONObject user = cardObjectResult.getJSONObject("user");

        // 级别：3高级负责人2中级负责人1初级负责人0成员
        Integer level = user.getInteger("level");
        // 是否是管理员  0/否 1/是
        Integer isAdministrators = user.getInteger("isAdministrators");

        CardUserVO cardUser = new CardUserVO();
        cardUser.setId(userId);
        cardUser.setType(level == 0 ? 1 : isAdministrators == 1 ? 3 : 2);
        cardUser.setHead(user.getString("head"));
        cardUser.setName(user.getString("name"));
        cardUser.setDeptName(user.getString("deptName"));
        cardUser.setEnterpriseName(user.getString("enterpriseName"));
        cardUser.setEntryTime(user.getTimestamp("entryTime"));

        JSONArray userIdList = cardObjectResult.getJSONArray("userIdList");
        List<Integer> integerList = new ArrayList<>();
        for (int i = 0; i < userIdList.size(); i++) {
            integerList.add(userIdList.getInteger(i));
        }
        cardUser.setUserIdList(integerList);

        return cardUser;
    }
}
