package com.ssb.approve.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Sets;
import com.ssb.approve.common.constants.Enum.*;
import com.ssb.approve.common.kafka.KafkaProducer;
import com.ssb.approve.dao.*;
import com.ssb.approve.entity.*;
import com.ssb.approve.model.FollowUpModel;
import com.ssb.approve.model.QueryAppResumeFollowUpList;
import com.ssb.approve.model.QueryFollowUpModel;
import com.ssb.approve.model.dto.FollowRecommendDTO;
import com.ssb.approve.model.dto.FollowRecommendListDTO;
import com.ssb.approve.model.vo.*;
import com.ssb.approve.service.*;
import com.ssb.approve.service.client.ProjectService;
import com.ssb.approve.service.client.ResumeService;
import com.ssb.approve.service.client.UserService;
import com.ssb.approve.utils.MapUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletRequest;
import java.sql.Timestamp;
import java.util.*;

/**
 * @ClassName: FollowUpServiceImpl
 * @Description: 跟进记录 impl
 * @Author: YZK
 * @Date: 2019年12月20日09:29:36
 **/
@Slf4j
@Service
public class FollowUpServiceImpl implements FollowUpService {

    @Autowired
    private FollowUpDAO followUpDAO;

    @Autowired
    private InterviewRecordDAO interviewRecordDAO;

    @Autowired
    private FollowLogDAO followLogDAO;

    @Autowired
    private EntryRecordDAO entryRecordDAO;

    @Autowired
    private SettleDetailsDAO settleDetailsDAO;

    @Autowired
    private UserService userService;

    @Autowired
    private ProjectService projectService;

    @Autowired
    private InterviewService interviewService;

    @Autowired
    private FollowCommissionService followCommissionService;

    @Autowired
    private CommonService commonService;

    @Autowired
    private ResumeService resumeService;

    @Autowired
    private NoticeRecordService noticeRecordService;

    @Autowired
    private EnterpriseNoticeRecordService enterpriseNoticeRecordService;

    @Autowired
    private FollowRecommendDAO followRecommendDAO;
    @Autowired
    private ContactDAO contactDAO;

    @Autowired
    private KafkaProducer kafkaProducer;

    @Autowired
    private HttpServletRequest request;

    /**
     * 检测求职用户是否正在关联项目
     *
     * @param contactId
     * @param projectId
     * @return
     */
    @Override
    public Map checkFollowUp(Integer contactId, Integer projectId) {
        // 是否有进行中项目跟进
        List<FollowUpVO> followUps = followUpDAO.getCheckFollowUp(contactId, projectId, 0);
        if (followUps.size() > 0) {
            //为了兼容后续接口   这个取第一个
            FollowUpVO followUp = followUps.get(0);
            return MapUtils.create("no", 0, "msg", "当前用户已存在该项目的意向，跟进人：" + followUp.getDeptName() + "-" + followUp.getUserName());
        }
        //已完成
        int finish = followUpDAO.checkFollowUp(contactId, projectId, 1);
        if (finish > 0) {
            return MapUtils.create("no", 0, "display", true, "msg", "当前用户已有过该项目的意向，请注意是否重复创建");
        }
        return MapUtils.create("no", 1, "msg", "检测通过");
    }

    @Override
    public Map saveFollowRecommend(FollowRecommendDTO dto) {

        // 90天内不能重复推荐
        int existsDataWithin90Days = followRecommendDAO.existsDataWithin90Days(dto.getContactId(), dto.getProjectId());
        if(existsDataWithin90Days > 0){
            return MapUtils.create("no", 0, "msg", "简历已为企业推荐，90天内不可再次推荐。");
        }

        CrmFollowRecommend followRecommend = new CrmFollowRecommend();
        followRecommend.setContactId(dto.getContactId());
        followRecommend.setResumeId(dto.getResumeId());
        followRecommend.setStatus(1);
        followRecommend.setAuth(0);
        followRecommend.setEnterpriseId(dto.getEnterpriseId());
        followRecommend.setCustomerEnterpriseId(dto.getCustomerEnterpriseId());
        followRecommend.setProjectId(dto.getProjectId());
        followRecommend.setCreateBy(dto.getCreateBy());
        followRecommend.setCreateTime(new Timestamp(System.currentTimeMillis()));
        followRecommend.setUpdateBy(dto.getCreateBy());
        followRecommend.setUpdateTime(new Timestamp(System.currentTimeMillis()));
        followRecommend.setRecommendBy(dto.getCreateBy());
        followRecommend.setRecommendTime(new Timestamp(System.currentTimeMillis()));
        followRecommendDAO.insertFollowRecommend(followRecommend);

        //给企业生成待办事项
        String contactName = contactDAO.getContactName(dto.getContactId());
        CrmEnterpriseNoticeRecord crmEnterpriseNoticeRecord = new CrmEnterpriseNoticeRecord(dto.getCustomerEnterpriseId(),
                dto.getProjectId(), null, 3, "简历推荐审核",
                String.format("有新简历（%s）待审核，请及时处理",contactName),
                1, 3, 0, 0, 0,
                1, 1, dto.getCreateBy(),
                new Timestamp(System.currentTimeMillis()), 0,
                new Timestamp(System.currentTimeMillis()));

        List<CrmEnterpriseNoticeRecord> records = new ArrayList<>();
        records.add(crmEnterpriseNoticeRecord);
        enterpriseNoticeRecordService.insertNoticeRecord(records);
        return MapUtils.create("no", 1, "msg", "推荐成功。");
    }

    @Override
    public Map getFollowRecommendList(FollowRecommendListDTO dto) {

        Integer pageBegin = (dto.getPageNum() - 1) * dto.getPageSize();
        dto.setPageBegin(pageBegin);

        List<FollowRecommendListVO> list = followRecommendDAO.getFollowRecommendList(dto);
        int count = followRecommendDAO.getFollowRecommendListCount(dto);
        return MapUtils.create("no", 1, "list", list, "count", count);
    }

    /**
     * 保存跟进记录
     *
     * @param followUpModel
     * @return
     */
    @Override
    @Transactional
    public Map save(FollowUpModel followUpModel) {
        //当前时间
        Timestamp nowTime = new Timestamp(System.currentTimeMillis());

        // 同步到app的面试数据
        Map<String, Object> ssbInterviewMap = new HashMap<>();

        // 约面
        if (followUpModel.getFollowUpStatus() == 1) {
            //约面时间 是否已满
            Integer code = interviewService.checkInterviewLimit(followUpModel.getProjectId(), followUpModel.getBehaviorTime());
            if (code.equals(500)) {
                return MapUtils.create("no", 0, "msg", "项目异常，请稍后重试。");
            }
            //已达到上限
            if (code.equals(201)) {
                return MapUtils.create("no", 0, "msg", "您选择的时间已约满，请重新预约时间");
            }
        }
        //校验是否添加
        List<FollowUpVO> followUps = followUpDAO.getCheckFollowUp(followUpModel.getContactId(), followUpModel.getProjectId(), 0);
        if (followUps.size() > 0) {
            //为了兼容后续接口   这个取第一个
            FollowUpVO followUp = followUps.get(0);
            return MapUtils.create("no", 0, "msg", "当前用户已存在该项目的意向，跟进人：" + followUp.getDeptName() + "-" + followUp.getUserName());
        }

        List<FollowUpVO> otherFollowUps = followUpDAO.getCheckFollowUpByPhone(followUpModel.getContactId(), followUpModel.getProjectId(), 0);
        if (otherFollowUps.size() > 0) {
            //为了兼容后续接口   这个取第一个
            FollowUpVO followUp = otherFollowUps.get(0);
            return MapUtils.create("no", 0, "msg", "当前用户已存在该项目的意向，跟进人：" + followUp.getDeptName() + "-" + followUp.getUserName());
        }

        //合同开始时间没到不能创建跟进
        Object projectResult = projectService.getProjectInfo(followUpModel.getProjectId());
        JSONObject jsonObjectResult = JSONObject.parseObject(JSON.toJSON(projectResult).toString());
        Integer activation = 0;
        //判断返回参数是否正常
        if (jsonObjectResult.getInteger("no") != null && jsonObjectResult.getInteger("no").equals(200)) {
            JSONObject jsonObject = jsonObjectResult.getJSONObject("obj");

            Timestamp contractStartTime = jsonObject.getTimestamp("contractStartTime");

            if(contractStartTime == null){
                return MapUtils.create("no", 0, "msg", "项目缺少开始时间，请联系项目负责人。");
            }

            if(nowTime.before(contractStartTime)){
                return MapUtils.create("no", 0, "msg", "没到合同开始时间，不能创建跟进。");
            }

            if (jsonObject.getInteger("position2First") == null || jsonObject.getInteger("position2Second") == null) {
                return MapUtils.create("no", 0, "msg", "项目缺少岗位，请联系项目负责人。");
            }

            activation = jsonObject.getInteger("activation") == null ? 0 : jsonObject.getInteger("activation");

            ssbInterviewMap.put("positionFirst", jsonObject.getInteger("position2Zeroth"));
            ssbInterviewMap.put("positionFirstName", jsonObject.getString("position2ZerothName"));
            ssbInterviewMap.put("positionSecond", jsonObject.getInteger("position2First"));
            ssbInterviewMap.put("positionSecondName", jsonObject.getString("positionFirstName"));
            ssbInterviewMap.put("positionThird", jsonObject.getInteger("position2Second"));
            ssbInterviewMap.put("positionThirdName", jsonObject.getString("positionSecondName"));
            ssbInterviewMap.put("salaryMin", jsonObject.getInteger("salaryMin"));
            ssbInterviewMap.put("salaryMax", jsonObject.getInteger("salaryMax"));
            ssbInterviewMap.put("interviewAddress", jsonObject.getString("interviewPositionDetailedAddress"));
            ssbInterviewMap.put("enterpriseName", jsonObject.getString("fullName"));
        } else {
            return MapUtils.create("no", 0, "msg", "项目异常，请稍后重试。");
        }

        FollowUp followUp = new FollowUp();
        followUp.setEnterpriseId(followUpModel.getEnterpriseId());
        followUp.setContactId(followUpModel.getContactId());
        followUp.setProjectId(followUpModel.getProjectId());
        followUp.setStatus(followUpModel.getFollowUpStatus());
        // 当前状态
        if (followUpModel.getFollowUpStatus() == 0) {
            followUp.setCurrentStatus(-1);
        }
        if (followUpModel.getFollowUpStatus() == 1) {
            followUp.setCurrentStatus(0);
        }
        followUp.setCreateBy(followUpModel.getUserId());
        followUp.setUpdateBy(followUpModel.getUserId());
        //insert
        int result = followUpDAO.save(followUp);
        if (result == 0) {
            log.error("项目跟进insert失败：" + followUp.toString());
            return MapUtils.create("no", 0, "msg", "简历注册失败");
        }

        // 保存跟进提成
        // 分为内部和外部
        Object user = userService.getLoginUserDetailsForLogin(followUpModel.getUserId(), followUpModel.getEnterpriseId());
        JSONObject userJsonObject = JSONObject.parseObject(JSON.toJSON(user).toString());
        if (userJsonObject.getInteger("no") != null && userJsonObject.getInteger("no").equals(200)) {
            JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(userJsonObject.get("details")));
            if(Objects.equals(jsonObject.get("type"), 0)){
                followCommissionService.saveFollowCommission(followUp.getId(), followUpModel.getProjectId(), jsonObjectResult);
            }else if(Objects.equals(jsonObject.get("type"), 1)){
                followCommissionService.savePartTimeJobFollowCommission(followUp.getId(), jsonObjectResult, jsonObject);
            }
        }else{
            return MapUtils.create("no", 0, "msg", "数据异常，请稍后重试");
        }

        //约面
        if (followUpModel.getFollowUpStatus() == 1) {
            //面试记录
            InterviewRecord interviewRecord = new InterviewRecord();
            interviewRecord.setEnterpriseId(followUpModel.getEnterpriseId());
            interviewRecord.setFollowId(followUp.getId());
            interviewRecord.setNode(1);//直接创建面试 默认 首轮
            interviewRecord.setAuthType(activation == 0 ? 0 : 1);
            interviewRecord.setBehaviorTime(followUpModel.getBehaviorTime());
            interviewRecord.setCreateBy(followUpModel.getUserId());
            interviewRecord.setCreateTime(nowTime);
            interviewRecord.setUpdateBy(followUpModel.getUserId());
            interviewRecord.setUpdateTime(nowTime);
            interviewRecord.setRemark(followUpModel.getRemark());
            interviewRecordDAO.save(interviewRecord);

            // 同步到app
            ssbInterviewMap.put("interviewTime", followUpModel.getBehaviorTime());
            ssbInterviewMap.put("followId", followUp.getId());
            String phone = contactDAO.getPhone(followUpModel.getContactId());
            if(phone != null){
                ssbInterviewMap.put("phone", phone);
                kafkaProducer.syncInterviewFromCRM(ssbInterviewMap);
            }
        }

        //跟进操作记录
        FollowLog followLog = new FollowLog();
        followLog.setEnterpriseId(followUpModel.getEnterpriseId());
        followLog.setFollowId(followUp.getId());
        followLog.setType(followUpModel.getFollowUpStatus());
        // 意象
        if (followUpModel.getFollowUpStatus() == 0) {
            followLog.setStatus(-1);
        }
        if (followUpModel.getFollowUpStatus() == 1) {
            followLog.setStatus(0);
        }

        followLog.setBehaviorTime(followUpModel.getBehaviorTime());
        followLog.setCreateBy(followUpModel.getUserId());
        followLog.setCreateTime(nowTime);
        followLog.setRemark(followUpModel.getRemark());
        followLogDAO.save(followLog);

        // app用户跟进状态
        int contactId = followUpDAO.getContactIdByFollowId(followUp.getId());
        resumeService.updateAppResume(contactId, 1, 1);

        // 同步猎头服务反馈
        resumeService.updateHeadhuntingServiceFeedback(followUpModel.getContactId());
        //处理待办-邀约面试
        CrmNoticeRecord record = new CrmNoticeRecord();
        record.setNoticeType(39);
        record.setTargetId(followUp.getProjectId());
        record.setTargetType(9);
        record.setStatus(NoticeStatusEnum.PROCESSED.getValue());
        record.setRead(NoticeReadEnum.READ.getValue());
        record.setUpdateBy(followUpModel.getUserId());
        record.setUpdateTime(new Timestamp(System.currentTimeMillis()));
        noticeRecordService.updateNoticeRecord(record);

        return MapUtils.create("no", 1, "msg", "跟进成功");
    }

    /**
     * 获取我的意象列表 tab数量
     *
     * @param queryFollowUpModel
     * @return
     */
    @Override
    public Map getImageCount(QueryFollowUpModel queryFollowUpModel) {
        //未完成状态
        queryFollowUpModel.setFinish(0);
        //待约面
        queryFollowUpModel.setStatus(0);
        //int appointmentCount = followUpDAO.getFollowUpCount(queryFollowUpModel);
        int recommendCount = followRecommendDAO.getApprovedThroughReviewCount(queryFollowUpModel.getUserId());
        //待面试
        queryFollowUpModel.setInterviewStatus(1);
        int interviewCount = followUpDAO.getInterviewCount(queryFollowUpModel);
        // 发offer
        int onlyOffer = followUpDAO.getOnlyOfferCount(queryFollowUpModel);
        //待入职
        queryFollowUpModel.setEntryStatus(3);
        int entryCount = followUpDAO.getEntryRecordCount(queryFollowUpModel);
        //待结算
        int settlementCount = followUpDAO.getSettleDetailsCount(queryFollowUpModel);
        //历史跟进
        int historyCount = followUpDAO.getHistoryCount(queryFollowUpModel);

        return MapUtils.create("no", 1, "appointmentCount", recommendCount, "interviewCount", interviewCount, "onlyOffer", onlyOffer
                , "entryCount", entryCount, "settlementCount", settlementCount, "historyCount", historyCount);
    }

    /**
     * 获取审核数量
     *
     * @param queryFollowUpModel
     * @return
     */
    @Override
    public Map getAuthCount(QueryFollowUpModel queryFollowUpModel) {
        //面试审核数量
        queryFollowUpModel.setAuthStatus(0);
        queryFollowUpModel.setQueryType(0);
        queryFollowUpModel.setAuthType(0);
        int interviewAuthCount = interviewRecordDAO.getInterviewAuthCount(queryFollowUpModel);
        //入职审核数量
        int entryAuthCount = entryRecordDAO.getEntryAuthCount(queryFollowUpModel);
        //结算审核数量
        queryFollowUpModel.setSettleStatus(1);
        int settleAuthCount = settleDetailsDAO.getSettleAuthCount(queryFollowUpModel);

        return MapUtils.create("no", 1, "interviewAuthCount", interviewAuthCount, "entryAuthCount", entryAuthCount, "settleAuthCount", settleAuthCount, "msg", "获取成功");
    }

    /**
     * 获取招聘跟进列表
     *
     * @param queryFollowUpModel
     * @return
     */
    @Override
    public Map getAppointmentList(QueryFollowUpModel queryFollowUpModel) {
        // 完成状态 (四个状态 都为)
        queryFollowUpModel.setFinish(0);
        Integer pageBegin = (queryFollowUpModel.getPageNum() - 1) * queryFollowUpModel.getPageSize();
        queryFollowUpModel.setPageBegin(pageBegin);
        //列表信息
        List<FollowUpVO> followUpVOList = followUpDAO.getFollowUpList(queryFollowUpModel);
        //装配 项目 预计 明细
        commonService.handleProjectExpectDetail(followUpVOList);
        //总数量
        int count = followUpDAO.getFollowUpCount(queryFollowUpModel);
        return MapUtils.create("no", 1, "msg", "成功", "results", followUpVOList, "count", count);
    }

    /**
     * 更新约面状态
     *
     * @param followUpModel
     * @return
     */
    @Override
    public Map updateAppointment(FollowUpModel followUpModel) {
        //当前时间
        Timestamp timestamp = new Timestamp(System.currentTimeMillis());
        //校验数据正确性
        FollowUp followUp = followUpDAO.getFollowUpById(followUpModel.getId());
        if (followUp != null) {
            //本人创建数据才可以修改
            if (followUp.getCreateBy().equals(followUpModel.getUserId())) {
                //约面添加每日限制
                if (followUpModel.getAppointmentStatus() == 0) {
                    //约面时间 是否已满
                    Integer code = interviewService.checkInterviewLimit(followUp.getProjectId(), followUpModel.getBehaviorTime());
                    if (code.equals(500)) {
                        return MapUtils.create("no", 0, "msg", "项目异常，请稍后重试。");
                    }
                    //已达到上限
                    if (code.equals(201)) {
                        return MapUtils.create("no", 0, "msg", "您选择的时间已约满，请重新预约时间");
                    }
                }

                // 处理待办消息
                CrmNoticeRecord record = new CrmNoticeRecord();
                record.setNoticeType(NoticeTypeEnum.RECRUITMENT_FOLLOW_UP_INTERVIEW_REMINDER.getValue());
                record.setTargetId(followUpModel.getId());
                record.setTargetType(4);
                record.setStatus(NoticeStatusEnum.PROCESSED.getValue());
                record.setRead(NoticeReadEnum.READ.getValue());
                record.setUpdateBy(followUpModel.getUserId());
                record.setUpdateTime(new Timestamp(System.currentTimeMillis()));
                noticeRecordService.updateNoticeRecord(record);

                FollowUp followUpUpdate = new FollowUp();
                followUpUpdate.setId(followUp.getId());
                followUpUpdate.setUpdateBy(followUpModel.getUserId());
                followUpUpdate.setUpdateTime(timestamp);
                // 预约成功 --
                if (followUpModel.getAppointmentStatus() == 0) {
                    //更新成已约待面试状态
                    followUpUpdate.setStatus(1);
                } else {
                    //约面失败
                    followUpUpdate.setFinish(1);// 结束状态
                }
                followUpUpdate.setCurrentStatus(followUpModel.getAppointmentStatus());
                //更新状态
                followUpDAO.updateFollowUp(followUpUpdate);
                //新增面试记录表
                if (followUpModel.getAppointmentStatus() == 0) {
                    //强制校验  反向打补丁   恶心的不要不要ode
                    InterviewRecord interviewRecord = interviewRecordDAO.getInterviewRecordByFollowUpId(followUp.getId());
                    if (interviewRecord == null) {

                        Object projectResult = projectService.getProjectInfo(followUp.getProjectId());
                        JSONObject jsonObjectResult = JSONObject.parseObject(JSON.toJSON(projectResult).toString());
                        Integer activation = 0;
                        //判断返回参数是否正常
                        if (jsonObjectResult.getInteger("no") != null && jsonObjectResult.getInteger("no").equals(200)) {
                            JSONObject jsonObject = jsonObjectResult.getJSONObject("obj");
                            activation = jsonObject.getInteger("activation") == null ? 0 : jsonObject.getInteger("activation");
                        } else {
                            return MapUtils.create("no", 0, "msg", "项目异常，请稍后重试。");
                        }

                        interviewRecord = new InterviewRecord();
                        interviewRecord.setEnterpriseId(followUp.getEnterpriseId());
                        interviewRecord.setFollowId(followUp.getId());
                        interviewRecord.setNode(1);
                        interviewRecord.setAuthType(activation == 0 ? 0 : 1);
                        interviewRecord.setBehaviorTime(followUpModel.getBehaviorTime());
                        interviewRecord.setCreateBy(followUpModel.getUserId());
                        interviewRecord.setCreateTime(timestamp);
                        interviewRecord.setUpdateBy(followUpModel.getUserId());
                        interviewRecord.setUpdateTime(timestamp);
                        interviewRecord.setRemark(followUpModel.getRemark());
                        interviewRecordDAO.save(interviewRecord);
                    }
                }

                //添加操作记录
                FollowLog followLog = new FollowLog();
                followLog.setEnterpriseId(followUpModel.getEnterpriseId());
                followLog.setFollowId(followUp.getId());
                followLog.setType(0);
                followLog.setStatus(followUpModel.getAppointmentStatus());
                followLog.setBehaviorTime(followUpModel.getBehaviorTime());
                followLog.setCreateBy(followUpModel.getUserId());
                followLog.setCreateTime(timestamp);
                followLog.setRemark(followUpModel.getRemark());
                followLogDAO.save(followLog);

                return MapUtils.create("no", 1, "msg", "更新成功");
            }
            return MapUtils.create("no", 0, "msg", "更新失败，数据异常");
        }
        return MapUtils.create("no", 0, "msg", "更新失败");
    }

    /**
     * 获取历史跟进列表
     *
     * @param queryFollowUpModel
     * @return
     */
    @Override
    public Map getHistoryList(QueryFollowUpModel queryFollowUpModel) {
        Integer pageBegin = (queryFollowUpModel.getPageNum() - 1) * queryFollowUpModel.getPageSize();
        queryFollowUpModel.setPageBegin(pageBegin);
        List<FollowUpVO> followUpVOList = followUpDAO.getHistoryList(queryFollowUpModel);
        commonService.handleProjectExpectDetail(followUpVOList);
        int count = followUpDAO.getHistoryCount(queryFollowUpModel);
        return MapUtils.create("no", 1, "msg", "成功", "results", followUpVOList, "count", count);
    }

    /**
     * 检测用户未完结跟进记录
     *
     * @param queryFollowUpModel
     * @return
     */
    @Override
    public Map checkQuitFollowUp(QueryFollowUpModel queryFollowUpModel) {
        //未完成
        queryFollowUpModel.setFinish(0);
        int noFinishCount = followUpDAO.getFollowUpCountByCreateByAndFinish(queryFollowUpModel);
        //完成
        queryFollowUpModel.setFinish(1);
        int finishCount = followUpDAO.getFollowUpCountByCreateByAndFinish(queryFollowUpModel);
        return MapUtils.create("no", 1, "msg", "成功", "noFinishCount", noFinishCount, "finishCount", finishCount);
    }

    /**
     * 获取项目跟进详情
     *
     * @param queryFollowUpModel
     * @return
     */
    @Override
    public Map getProjectFollowUpDetails(QueryFollowUpModel queryFollowUpModel) {

        Integer pageBegin = (queryFollowUpModel.getPageNum() - 1) * queryFollowUpModel.getPageSize();
        queryFollowUpModel.setPageBegin(pageBegin);
        //列表信息
        List<FollowUpVO> followUpVOList = followUpDAO.getProjectFollowUpDetails(queryFollowUpModel);

        // 当前用户身份
        if(userService.isAdmin(queryFollowUpModel.getUserId()) == 0){
            followUpVOList.forEach(followUpVO -> {
                if((followUpVO.getIsAdministrators() != null && followUpVO.getIsAdministrators() == 1)
                        || (followUpVO.getUserId() != null && followUpVO.getUserId().equals(queryFollowUpModel.getUserId()))
                        || (followUpVO.getMaster() != null && followUpVO.getMaster().equals(queryFollowUpModel.getUserId()))
                        || (followUpVO.getSigningUserId() != null && followUpVO.getSigningUserId().equals(queryFollowUpModel.getUserId()))){

                }else{
                    String phone = followUpVO.getContactPhone();
                    if(phone != null && phone.length() == 11){
                        String phone1 = phone.substring(0, 3);
                        String phone2 = phone.substring(7, 11);
                        followUpVO.setContactPhone(phone1 + "****" + phone2);
                    }
                }
            });
        }

        //总数量
        int count = followUpDAO.getProjectFollowUpDetailsCount(queryFollowUpModel);
        return MapUtils.create("no", 1, "msg", "成功", "results", followUpVOList, "count", count);
    }

    @Override
    public Map getAppResumeFollowUpList(QueryAppResumeFollowUpList queryAppResumeFollowUpList) {

        Integer pageBegin = (queryAppResumeFollowUpList.getPageNum() - 1) * queryAppResumeFollowUpList.getPageSize();
        queryAppResumeFollowUpList.setPageBegin(pageBegin);

        List<AppResumeFollowUpListVO> list = followUpDAO.getAppResumeFollowUpList(queryAppResumeFollowUpList);
        Integer count = followUpDAO.getAppResumeFollowUpListCount(queryAppResumeFollowUpList);

        return MapUtils.create("no", 1, "details", list, "count", count);
    }

    /**
     * app简历跟进结果图表
     *
     * @param queryAppResumeFollowUpList
     * @return
     */
    @Override
    public Map getAppResumeFollowUpChart(QueryAppResumeFollowUpList queryAppResumeFollowUpList) {

        Map<String, Object> conversionRatio = MapUtils.create();
        Map<String, Object> followUpEfficiency = MapUtils.create();
        Map<String, Object> deliveryStatus = MapUtils.create();

        // 转化比例
        // 未跟进
        queryAppResumeFollowUpList.setType(0);
        Integer notFollowedUpCount = followUpDAO.countConversionRatio(queryAppResumeFollowUpList);
        conversionRatio.put("notFollowedUpCount", notFollowedUpCount);

        // 跟进未转化
        queryAppResumeFollowUpList.setType(1);
        Integer followUpNotConvertedCount = followUpDAO.countConversionRatio(queryAppResumeFollowUpList);
        conversionRatio.put("followUpNotConvertedCount", followUpNotConvertedCount);

        // 跟进已转化
        queryAppResumeFollowUpList.setType(2);
        Integer followUpConvertedCount = followUpDAO.countConversionRatio(queryAppResumeFollowUpList);
        conversionRatio.put("followUpConvertedCount", followUpConvertedCount);


        // 跟进效率
        // 未跟进
        queryAppResumeFollowUpList.setType(0);
        Integer followUpEfficiencyNotFollowedUpCount = followUpDAO.countFollowUpEfficiency(queryAppResumeFollowUpList);
        followUpEfficiency.put("followUpEfficiencyNotFollowedUpCount", followUpEfficiencyNotFollowedUpCount);

        // 半小时内
        queryAppResumeFollowUpList.setType(1);
        Integer halfHourCount = followUpDAO.countFollowUpEfficiency(queryAppResumeFollowUpList);
        followUpEfficiency.put("halfHourCount", halfHourCount);

        // 半小时到一小时
        queryAppResumeFollowUpList.setType(2);
        Integer oneHourCount = followUpDAO.countFollowUpEfficiency(queryAppResumeFollowUpList);
        followUpEfficiency.put("oneHourCount", oneHourCount);

        // 一到两小时
        queryAppResumeFollowUpList.setType(3);
        Integer twoHourCount = followUpDAO.countFollowUpEfficiency(queryAppResumeFollowUpList);
        followUpEfficiency.put("twoHourCount", twoHourCount);

        // 大于两小时
        queryAppResumeFollowUpList.setType(4);
        Integer moreThenTwoHourCount = followUpDAO.countFollowUpEfficiency(queryAppResumeFollowUpList);
        followUpEfficiency.put("moreThenTwoHourCount", moreThenTwoHourCount);


        // 交付状态
        List<DeliveryStatusVO> countDeliveryStatusList = followUpDAO.countDeliveryStatus(queryAppResumeFollowUpList);
        int intention = 0, prepareForTheInterview = 0, appointmentInterviewFailed = 0, didNotAttendTheInterview = 0, interviewFailed = 0,
                offer = 0, toBeSettled = 0, toBeEmployed = 0, refuseEntry = 0, settled = 0, quit = 0, notFollowedUp = 0;
        for (DeliveryStatusVO deliveryStatusVO : countDeliveryStatusList) {
            switch (deliveryStatusVO.getFollowStatus()){
                case -1:
                    // 有意向
                    intention += deliveryStatusVO.getCount();
                    break;
                case 0:
                    // 待面试
                    prepareForTheInterview += deliveryStatusVO.getCount();
                    break;
                case 1:
                    // 约面失败
                    appointmentInterviewFailed += deliveryStatusVO.getCount();
                    break;
                case 2:
                    // 未到面
                    didNotAttendTheInterview += deliveryStatusVO.getCount();
                    break;
                case 3:
                    // 待面试
                    prepareForTheInterview += deliveryStatusVO.getCount();
                    break;
                case 4:
                    // 面试未通过
                    interviewFailed += deliveryStatusVO.getCount();
                    break;
                case 5:
                    // 待面试
                    prepareForTheInterview += deliveryStatusVO.getCount();
                    break;
                case 6:
                    // 发offer
                    offer += deliveryStatusVO.getCount();
                    break;
                case 7:
                    // 待结算
                    toBeSettled += deliveryStatusVO.getCount();
                    break;
                case 8:
                    // 待入职
                    toBeEmployed += deliveryStatusVO.getCount();
                    break;
                case 9:
                    // 拒绝入职
                    refuseEntry += deliveryStatusVO.getCount();
                    break;
                case 10:
                    // 已结算
                    settled += deliveryStatusVO.getCount();
                    break;
                case 11:
                    // 离职
                    quit += deliveryStatusVO.getCount();
                    break;
                default:
                    // 未跟进
                    notFollowedUp += deliveryStatusVO.getCount();
                    break;
            }
        }
        deliveryStatus.put("intention", intention);
        deliveryStatus.put("prepareForTheInterview", prepareForTheInterview);
        deliveryStatus.put("appointmentInterviewFailed", appointmentInterviewFailed);
        deliveryStatus.put("didNotAttendTheInterview", didNotAttendTheInterview);
        deliveryStatus.put("interviewFailed", interviewFailed);
        deliveryStatus.put("offer", offer);
        deliveryStatus.put("toBeSettled", toBeSettled);
        deliveryStatus.put("toBeEmployed", toBeEmployed);
        deliveryStatus.put("refuseEntry", refuseEntry);
        deliveryStatus.put("settled", settled);
        deliveryStatus.put("quit", quit);
        deliveryStatus.put("notFollowedUp", notFollowedUp);

        return MapUtils.create("no", 1, "conversionRatio", conversionRatio, "followUpEfficiency", followUpEfficiency, "deliveryStatus", deliveryStatus);
    }

    @Override
    public Map getCooperationProjectFollowUpList(QueryFollowUpModel queryFollowUpModel) {

        List<CooperationProjectFollowUpInfoVO> list = followUpDAO.getCooperationProjectFollowUpList(queryFollowUpModel);
        int count = followUpDAO.getCooperationProjectFollowUpCount(queryFollowUpModel);

        return MapUtils.create("no", 1, "list", list, "count", count);
    }

    @Override
    public Map stopFollowUp(Integer userId, Integer projectId) {
        List<FollowUpVO> list = followUpDAO.getFollowUpIdByProjectId(projectId);
        if(CollectionUtils.isEmpty(list)){
            return MapUtils.create("no", 1);
        }
        list.forEach(followUpVO -> {
            FollowUp followUp = new FollowUp();
            followUp.setId(followUpVO.getId());
            followUp.setStatus(FollowUpStatusEnum.INTERRUPTED.getCode());
            followUp.setCurrentStatus(FollowUpCurrentStatusEnum.INTERRUPTED.getCode());
            followUp.setFinish(1);
            followUp.setUpdateBy(userId);
            followUp.setUpdateTime(new Timestamp(System.currentTimeMillis()));
            followUpDAO.updateFollowUp(followUp);

            FollowLog followLog = new FollowLog();
            followLog.setEnterpriseId(followUpVO.getEnterpriseId());
            followLog.setFollowId(followUpVO.getId());
            followLog.setType(FollowLogTypeEnum.INTERRUPTED.getCode());
            followLog.setStatus(FollowLogStatusEnum.INTERRUPTED.getCode());
            followLog.setBehaviorTime(new Timestamp(System.currentTimeMillis()));
            followLog.setCreateBy(userId);
            followLog.setCreateTime(new Timestamp(System.currentTimeMillis()));
            followLogDAO.save(followLog);

            InterviewRecord interviewRecord = new InterviewRecord();
            interviewRecord.setFollowId(followUpVO.getId());
            interviewRecord.setStatus(InterviewRecordEnum.INTERRUPTED.getCode());
            interviewRecord.setUpdateBy(userId);
            interviewRecord.setUpdateTime(new Timestamp(System.currentTimeMillis()));
            interviewRecordDAO.updateInterviewRecordByFollowId(interviewRecord);

            EntryRecord entryRecord = new EntryRecord();
            entryRecord.setFollowId(followUpVO.getId());
            entryRecord.setStatus(EntryRecordEnum.INTERRUPTED.getCode());
            entryRecord.setUpdateBy(userId);
            entryRecord.setUpdateTime(new Timestamp(System.currentTimeMillis()));
            entryRecordDAO.updateEntryRecordByFollowId(entryRecord);

            SettleDetails settleDetails = new SettleDetails();
            settleDetails.setFollowId(followUpVO.getId());
            settleDetails.setStatus(SettleDetailsStatusEnum.INTERRUPTED.getCode());
            settleDetails.setUpdateBy(userId);
            settleDetails.setUpdateTime(new Timestamp(System.currentTimeMillis()));
            settleDetailsDAO.updateSettleDetailsByFollowId(settleDetails);
        });

        return MapUtils.create("no", 1);
    }

    @Override
    public Map getShareProjectFollowUpCount(Integer projectId) {
        int waitingForInterviewCount = followUpDAO.getShareProjectFollowUpCount(1, projectId);
        int waitingForEntryCount = followUpDAO.getShareProjectFollowUpCount(2, projectId);
        int waitingForSettleCount = followUpDAO.getShareProjectFollowUpCount(3, projectId);
        return MapUtils.create("no", 1, "waitingForInterviewCount", waitingForInterviewCount,
                "waitingForEntryCount", waitingForEntryCount, "waitingForSettleCount", waitingForSettleCount);
    }

    @Override
    public Object getFollowRecommendListByEnterprise(FollowRecommendListDTO dto) {
        Integer pageBegin = (dto.getPageNum() - 1) * dto.getPageSize();
        dto.setPageBegin(pageBegin);

        List<FollowRecommendListVO> list = followRecommendDAO.getFollowRecommendListByEnterprise(dto);
        int count = followRecommendDAO.getFollowRecommendCountByEnterprise(dto);

        return MapUtils.create("no", 1, "list", list, "count", count);
    }


    @Transactional
    @Override
    public Object auditFollowRecommendById(CrmFollowRecommend dto) {
        dto.setCheckedTime(new Timestamp(System.currentTimeMillis()));

        if (Objects.equals(dto.getStatus(), 2) || Objects.equals(dto.getStatus(), 3)) {
            dto.setAuth(1);
        }

        int i = followRecommendDAO.updateFollowRecommend(dto);
        CrmFollowRecommend followRecommend = followRecommendDAO.getFollowRecommendById(dto.getId());
        String contactName = contactDAO.getContactName(followRecommend.getContactId());
        //创建邀约面试-待办
        List<CrmNoticeRecord> records = new ArrayList<>();
        CrmNoticeRecord crmNoticeRecord = new CrmNoticeRecord(followRecommend.getRecommendBy(),
                followRecommend.getProjectId(), followRecommend.getContactId(), 1, "邀约面试",
                contactName + "候选人简历已审核通过，请进行面试邀约",
                1, 3, 0, followRecommend.getProjectId(), 9,
                12, 39, dto.getCheckedBy(),
                new Timestamp(System.currentTimeMillis()), dto.getCheckedBy(),
                new Timestamp(System.currentTimeMillis()));
        records.add(crmNoticeRecord);
        noticeRecordService.noticeCancelShareProject(records);

        //企业完成待办事项
        CrmEnterpriseNoticeRecord record = new CrmEnterpriseNoticeRecord();
        record.setContactId(followRecommend.getContactId());
        record.setProjectId(followRecommend.getProjectId());
        record.setNoticeType(1);
        record.setType(1);

        record.setStatus(4);
        record.setRead(1);
        record.setUpdateBy(dto.getCheckedBy());
        record.setUpdateTime(new Timestamp(System.currentTimeMillis()));
        enterpriseNoticeRecordService.updateNoticeRecord(record);

        return MapUtils.create("no", i, "msg", i > 0 ? "修改成功" : "修改失败");
    }
}
