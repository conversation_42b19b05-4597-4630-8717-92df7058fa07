package com.ssb.approve.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.ssb.approve.common.constants.Enum.NoticeReadEnum;
import com.ssb.approve.common.constants.Enum.NoticeStatusEnum;
import com.ssb.approve.common.constants.Enum.NoticeTypeEnum;
import com.ssb.approve.common.constants.Enum.RechargeLogTransactionTypeEnum;
import com.ssb.approve.common.kafka.KafkaProducer;
import com.ssb.approve.dao.*;
import com.ssb.approve.entity.*;
import com.ssb.approve.model.FollowUpModel;
import com.ssb.approve.model.QueryFollowUpModel;
import com.ssb.approve.model.dto.FundDTO;
import com.ssb.approve.model.vo.FollowUpVO;
import com.ssb.approve.model.vo.SettleDetailsVO;
import com.ssb.approve.service.*;
import com.ssb.approve.service.client.ProjectService;
import com.ssb.approve.service.client.UserService;
import com.ssb.approve.utils.MapUtils;
import com.ssb.approve.utils.RedisLockUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @ClassName: SettleDetailsService
 * @Description: 结算明细记录 service
 * @Author: YZK
 * @Date: 2019年12月27日16:15:42
 **/
@Slf4j
@Service
public class SettleDetailsServiceImpl implements SettleDetailsService {

    @Autowired
    private FollowUpDAO followUpDAO;

    @Autowired
    private SettleDetailsDAO settleDetailsDAO;

    @Autowired
    private SettleLogDAO settleLogDAO;

    @Autowired
    private FollowLogDAO followLogDAO;

    @Autowired
    private RechargeLogDAO rechargeLogDAO;

    @Autowired
    private RedisLockUtils redisLockUtils;

    @Autowired
    private CommissionGaveLogService commissionGaveLogService;

    @Autowired
    private CommonService commonService;

    @Autowired
    private ProjectService projectService;

    @Autowired
    private NoticeRecordService noticeRecordService;

    @Autowired
    private EnterpriseNoticeRecordService enterpriseNoticeRecordService;

    @Autowired
    private KafkaProducer kafkaProducer;

    @Autowired
    private DataStatisticService dataStatisticService;

    /**
     * 获取入职待结算列表
     *
     * @param queryFollowUpModel
     * @return
     */
    @Override
    public Map getSettleDetailsList(QueryFollowUpModel queryFollowUpModel) {
        Integer pageBegin = (queryFollowUpModel.getPageNum() - 1) * queryFollowUpModel.getPageSize();
        queryFollowUpModel.setPageBegin(pageBegin);

        List<Integer> userIdList = dataStatisticService.getUserIds(queryFollowUpModel.getUserId(), queryFollowUpModel.getEnterpriseId());
        if(CollectionUtils.isNotEmpty(userIdList)){
            queryFollowUpModel.setUserIdList(userIdList);
        }

        List<FollowUpVO> followUps = followUpDAO.getSettleDetailsList(queryFollowUpModel);
        // 单节点处理数据
        commonService.singleNodeHandleProjectExpectDetail(followUps);
        int count = followUpDAO.getSettleDetailsCount(queryFollowUpModel);
        return MapUtils.create("no", 1, "msg", "获取成功", "results", followUps, "count", count);
    }

    /**
     * 获取结算确认列表
     *
     * @param queryFollowUpModel
     * @return
     */
    @Override
    public Map getSettleAuthList(QueryFollowUpModel queryFollowUpModel) {
        Integer pageBegin = (queryFollowUpModel.getPageNum() - 1) * queryFollowUpModel.getPageSize();
        queryFollowUpModel.setPageBegin(pageBegin);
        List<SettleDetailsVO> settleDetails = Lists.newArrayList();

        settleDetails = settleDetailsDAO.getSettleAuthList(queryFollowUpModel);
        int count = settleDetailsDAO.getSettleAuthCount(queryFollowUpModel);
        return MapUtils.create("no", 1, "msg", "获取成功", "results", settleDetails, "count", count);
    }

    /**
     * 结算确认 -- 结算、离职操作
     *
     * @param followUpModel
     * @return
     */
    @Override
    @Transactional
    public Map updateSettleAuth(FollowUpModel followUpModel) {
        //数据加锁  防止出现同时操作情况
        if (!redisLockUtils.lock(3, followUpModel.getId(), followUpModel.getUserId(), 1)) {
            return MapUtils.create("no", 0, "msg", "数据异常，请刷新后再操作！");
        }
        //时间
        Timestamp timestamp = new Timestamp(System.currentTimeMillis());
        //结算明细
        SettleDetails settleDetails = settleDetailsDAO.getSettleDetailsById(followUpModel.getId());
        if (settleDetails != null) {
            // 已结算状态
            if (settleDetails.getStatus() != 0) {
                return MapUtils.create("no", 0, "msg", "已结算，请稍后重试");
            }

            // 按结算顺序结算   前面顺序没有结算不能结算
            if (settleDetails.getNode() != 1) {
                int count = settleDetailsDAO.countSettledByFollowId(settleDetails.getFollowId(), settleDetails.getNode());
                if (count > 0) {
                    // 解锁
                    redisLockUtils.lock(3, followUpModel.getId(), followUpModel.getUserId(), 2);
                    return MapUtils.create("no", 0, "msg", "请按顺序完成之前节点的结算。");
                }
            }

            // 是否修改实际结算时间
            // 实际结算时间，第一个节点必须在入职时间之后。其他节点必须在上个节点的实际结算时间之后。
            if (followUpModel.getSettleStatus() == 1 && followUpModel.getActualSettleTime() != null) {
                if (settleDetails.getNode() == 1 && settleDetails.getEntryTime().after(followUpModel.getActualSettleTime())) {
                    // 解锁
                    redisLockUtils.lock(3, followUpModel.getId(), followUpModel.getUserId(), 2);
                    return MapUtils.create("no", 0, "msg", "实际结算时间必须在入职时间之后。");
                } else if (settleDetails.getNode() > 1) {
                    Timestamp prevActualSettleTime = settleDetailsDAO.getPrevActualSettleTime(settleDetails.getFollowId(), settleDetails.getNode());
                    if (prevActualSettleTime.after(followUpModel.getActualSettleTime())) {
                        // 解锁
                        redisLockUtils.lock(3, followUpModel.getId(), followUpModel.getUserId(), 2);
                        return MapUtils.create("no", 0, "msg", "实际结算时间必须在上个节点的实际结算时间之后。");
                    }
                }
            }

            // 处理待办消息
            CrmNoticeRecord record = new CrmNoticeRecord();
            record.setNoticeType(NoticeTypeEnum.HEADHUNTER_REVIEW_SETTLEMENT_RESULT_CONFIRM.getValue());
            record.setTargetId(followUpModel.getId());
            record.setTargetType(3);
            record.setStatus(NoticeStatusEnum.PROCESSED.getValue());
            record.setRead(NoticeReadEnum.READ.getValue());
            record.setUpdateBy(followUpModel.getUserId());
            record.setUpdateTime(new Timestamp(System.currentTimeMillis()));
            noticeRecordService.updateNoticeRecord(record);

            FollowUp followUp = followUpDAO.getFollowUpById(settleDetails.getFollowId());

            //处理企业端待办消息
            CrmEnterpriseNoticeRecord recordEnterprise = new CrmEnterpriseNoticeRecord();
            recordEnterprise.setContactId(followUp.getContactId());
            recordEnterprise.setProjectId(followUp.getProjectId());
            recordEnterprise.setNoticeType(4);
            recordEnterprise.setType(1);
            recordEnterprise.setStatus(4);
            recordEnterprise.setRead(1);
            recordEnterprise.setUpdateBy(followUpModel.getUserId());
            recordEnterprise.setUpdateTime(new Timestamp(System.currentTimeMillis()));
            enterpriseNoticeRecordService.updateNoticeRecord(recordEnterprise);

            // 实际结算金额
            BigDecimal salary = followUpModel.getPayment();

            // 合同信息 获取客户id 合同id 付费类型
            JSONObject jsonObjectContract = null;
            Object contractInfoResult = projectService.getContractInfo(followUp.getProjectId());
            JSONObject jsonObjectResult = JSONObject.parseObject(JSON.toJSON(contractInfoResult).toString());
            if (jsonObjectResult.getInteger("no") != null && jsonObjectResult.getInteger("no").equals(200)) {
                jsonObjectContract = jsonObjectResult.getJSONObject("detail");
            }

            if(jsonObjectContract != null){
                Integer paymentType = jsonObjectContract.getInteger("paymentType");
                Integer source = jsonObjectContract.getInteger("source");
                BigDecimal rate = BigDecimal.ZERO;
                if(source == 3 && paymentType != null){
                    rate = jsonObjectContract.getBigDecimal("rate");
                    salary = followUpModel.getPayment().multiply(rate).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);
                }
            }

            SettleLog settleLog = new SettleLog();
            settleLog.setEnterpriseId(settleDetails.getEnterpriseId());
            settleLog.setFollowId(settleDetails.getFollowId());
            settleLog.setSettleId(settleDetails.getId());
            settleLog.setBehaviorTime(followUpModel.getBehaviorTime());
            settleLog.setPayment(salary);
            settleLog.setPerformance(followUpModel.getPerformance());
            settleLog.setCreateBy(followUpModel.getUserId());
            settleLog.setCreateTime(timestamp);
            settleLog.setRemark(followUpModel.getRemark());
            //保存
            settleLogDAO.save(settleLog);
            //更新结算明细
            SettleDetails settleDetailsUpdate = new SettleDetails();
            settleDetailsUpdate.setId(settleDetails.getId());
            settleDetailsUpdate.setStatus(followUpModel.getSettleStatus());
            settleDetailsUpdate.setActualSettleTime(followUpModel.getActualSettleTime());
            settleDetailsUpdate.setUpdateBy(followUpModel.getUserId());
            settleDetailsUpdate.setUpdateTime(timestamp);
            settleDetailsUpdate.setReviewUserId(followUpModel.getUserId());
            settleDetailsUpdate.setReviewTime(timestamp);
            settleDetailsDAO.updateSettleDetailsStatus(settleDetailsUpdate);

            // 实际结算时间是否顺延
            if(followUpModel.getDelay() !=null && followUpModel.getDelay() == 1 && followUpModel.getActualSettleTime() != null){
                // 获取当前节点后的所有节点
                List<SettleDetails> theRestDetailsList = settleDetailsDAO.getTheRestDetailsList(settleDetails.getFollowId(), settleDetails.getNode());
                if(theRestDetailsList.size()>0){
                    long intervalTime = followUpModel.getActualSettleTime().getTime() - settleDetails.getSettleTime().getTime();

                    for (SettleDetails settleDetail : theRestDetailsList) {
                        long settleTime = settleDetail.getSettleTime().getTime();
                        settleTime += intervalTime;
                        settleDetailsDAO.updateSettleTime(settleDetail.getId(), new Timestamp(settleTime), followUpModel.getUserId());
                    }
                }
            }

            // 发放提成
            Integer userType = followUpDAO.getFollowUpUserCommissionInfoById(settleDetails.getFollowId());
            if(userType == 0){
                commissionGaveLogService.gaveCommission(settleDetails.getId(), salary);
            }else if (userType == 1){
                commissionGaveLogService.gavePartTimeJobCommission(settleDetails.getId(), salary);
            }


            //添加操作记录
            FollowLog followLog = new FollowLog();
            followLog.setEnterpriseId(followUpModel.getEnterpriseId());
            followLog.setSettleLogId(settleLog.getId());
            followLog.setFollowId(settleDetails.getFollowId());
            followLog.setType(4);
            // settleStatus 1 结算 2 离职  (没办法做到统一  手动匹配)
            if (followUpModel.getSettleStatus() == 1) {
                followLog.setStatus(10);
            } else if (followUpModel.getSettleStatus() == 2) {
                followLog.setStatus(11);
            }
            followLog.setBehaviorTime(followUpModel.getBehaviorTime());
            followLog.setCreateBy(followUpModel.getUserId());
            followLog.setCreateTime(timestamp);
            followLog.setRemark(followUpModel.getRemark());
            followLogDAO.save(followLog);


            // 创建充值记录
            RechargeLog rechargeLog = new RechargeLog();
            rechargeLog.setEnterpriseId(settleDetails.getEnterpriseId());
            rechargeLog.setProjectId(followUp.getProjectId());
            rechargeLog.setCreateBy(followUpModel.getUserId());
            rechargeLog.setCreateTime(timestamp);
            if (followUpModel.getSettleStatus() == 1) {
                rechargeLog.setRemark("结算扣款");
                rechargeLog.setTransactionType(RechargeLogTransactionTypeEnum.SETTLEMENT.getCode());
            } else {
                rechargeLog.setRemark("离职扣款");
                rechargeLog.setTransactionType(RechargeLogTransactionTypeEnum.RESIGNATION_DEDUCTION.getCode());
            }

            if(jsonObjectContract != null){
                Integer paymentType = jsonObjectContract.getInteger("paymentType");
                rechargeLog.setCustomerId(jsonObjectContract.getInteger("customerId"));
                rechargeLog.setContractId(jsonObjectContract.getInteger("id"));
                if(paymentType != null){
                    rechargeLog.setType(paymentType);
                    rechargeLog.setSalary(paymentType == 1 ? followUpModel.getPayment().negate() : followUpModel.getPayment());
                }else{
                    rechargeLog.setType(1);
                    rechargeLog.setSalary(followUpModel.getPayment().negate());
                }
            }else{
                rechargeLog.setType(1);
                rechargeLog.setSalary(followUpModel.getPayment().negate());
            }

            rechargeLogDAO.save(rechargeLog);
            if(rechargeLog.getCustomerId() != null) {
                if (rechargeLog.getType() != null) {
                    if (rechargeLog.getType() == 1) {
                        rechargeLogDAO.updataBalance(rechargeLog.getSalary(), rechargeLog.getCustomerId(), timestamp);
                    } else {
                        rechargeLogDAO.updataPayment(rechargeLog.getSalary(), rechargeLog.getCustomerId(), timestamp);
                    }
                } else {
                    rechargeLogDAO.updataBalance(rechargeLog.getSalary(), rechargeLog.getCustomerId(), timestamp);
                }
            }



            //离职情况  所有后面未结算的记录，都变成离职
            if (followUpModel.getSettleStatus() == 2) {
                //查询当前节点后的节点
                List<SettleDetails> settleDetailsList = settleDetailsDAO.getSettleDetailsListById(settleDetails.getFollowId(), settleDetails.getNode());
                if (!settleDetailsList.isEmpty()) {
                    for (SettleDetails settleDetail : settleDetailsList) {

                        //保存
                        SettleLog resignSettleLog = new SettleLog();
                        resignSettleLog.setEnterpriseId(settleDetail.getEnterpriseId());
                        resignSettleLog.setFollowId(settleDetail.getFollowId());
                        resignSettleLog.setSettleId(settleDetail.getId());
                        resignSettleLog.setBehaviorTime(followUpModel.getBehaviorTime());
                        resignSettleLog.setPayment(BigDecimal.ZERO);
                        resignSettleLog.setPerformance(BigDecimal.ZERO);
                        resignSettleLog.setCreateBy(followUpModel.getUserId());
                        resignSettleLog.setCreateTime(timestamp);
                        resignSettleLog.setRemark(followUpModel.getRemark());
                        settleLogDAO.save(resignSettleLog);

                        //更新结算明细
                        SettleDetails updateSettleDetails = new SettleDetails();
                        updateSettleDetails.setId(settleDetail.getId());
                        updateSettleDetails.setStatus(2);
                        updateSettleDetails.setUpdateBy(followUpModel.getUserId());
                        updateSettleDetails.setUpdateTime(timestamp);
                        updateSettleDetails.setReviewUserId(followUpModel.getUserId());
                        updateSettleDetails.setReviewTime(timestamp);
                        settleDetailsDAO.updateSettleDetailsStatus(updateSettleDetails);

                        // 发放提成
                        commissionGaveLogService.gaveCommission(settleDetail.getId(), BigDecimal.ZERO);

                        //添加操作记录
                        FollowLog resignFollowLog = new FollowLog();
                        resignFollowLog.setEnterpriseId(followUpModel.getEnterpriseId());
                        resignFollowLog.setSettleLogId(settleLog.getId());
                        resignFollowLog.setFollowId(settleDetail.getFollowId());
                        resignFollowLog.setType(4);
                        resignFollowLog.setStatus(11);
                        resignFollowLog.setBehaviorTime(followUpModel.getBehaviorTime());
                        resignFollowLog.setCreateBy(followUpModel.getUserId());
                        resignFollowLog.setCreateTime(timestamp);
                        resignFollowLog.setRemark(followUpModel.getRemark());
                        followLogDAO.save(resignFollowLog);


                        // 创建充值记录
                        RechargeLog resignRechargeLog = new RechargeLog();
                        resignRechargeLog.setEnterpriseId(settleDetail.getEnterpriseId());
                        resignRechargeLog.setProjectId(followUp.getProjectId());
                        resignRechargeLog.setSalary(BigDecimal.ZERO);
                        resignRechargeLog.setCreateBy(followUpModel.getUserId());
                        resignRechargeLog.setCreateTime(timestamp);
                        resignRechargeLog.setRemark("离职扣款");
                        resignRechargeLog.setTransactionType(RechargeLogTransactionTypeEnum.RESIGNATION_DEDUCTION.getCode());
                        if(jsonObjectContract != null){
                            Integer paymentType = jsonObjectContract.getInteger("paymentType");
                            resignRechargeLog.setCustomerId(jsonObjectContract.getInteger("customerId"));
                            resignRechargeLog.setContractId(jsonObjectContract.getInteger("id"));
                            resignRechargeLog.setType(paymentType == null ? 1 : paymentType);
                        }else {
                            resignRechargeLog.setType(1);
                        }
                        rechargeLogDAO.save(resignRechargeLog);
                    }
                }
            }

            //未结算数量
            int unSettleCount = settleDetailsDAO.unSettleCount(settleDetails.getFollowId());
            //更新主数据
            FollowUp followUpUpdate = new FollowUp();
            followUpUpdate.setId(settleDetails.getFollowId());
            //全部结算 变成完成状态
            if (unSettleCount == 0) {
                followUpUpdate.setFinish(1);
                //离职数量
                int resignedCount = settleDetailsDAO.getSettleCountByFollowIdStatus(settleDetails.getFollowId(), 2);
                // 需求规定  有一个离职 都算离职
                if (resignedCount > 0) {
                    followUpUpdate.setCurrentStatus(11);
                } else {
                    followUpUpdate.setCurrentStatus(10);
                }
            }
            followUpUpdate.setUpdateBy(followUpModel.getUserId());
            followUpUpdate.setUpdateTime(timestamp);
            followUpDAO.updateFollowUp(followUpUpdate);

            // 结算后处理项目紧急状态
            projectService.updateProjectDangerBySurplusQuota(followUp.getProjectId(), followUpModel.getUserId());

            //解锁   解锁操作  理论上下边 else 异常也要解锁   但是能到else 肯定是非法操作  强制锁5秒钟数据 起到安全保证作用
            redisLockUtils.lock(3, followUpModel.getId(), followUpModel.getUserId(), 2);
            return MapUtils.create("no", 1, "msg", "操作成功", "projectId", followUp.getProjectId());
        }
        return MapUtils.create("no", 0, "msg", "数据异常，请稍后重试");
    }

    /**
     * 更新临时结算状态（猎头操作）
     *
     * @param followUpModel
     * @return
     */
    @Override
    public Map updateSettleDetails(FollowUpModel followUpModel) {
        //查询状态
        SettleDetails settleDetails = settleDetailsDAO.getSettleDetailsById(followUpModel.getSettleId());
        // 判断数据
        if (settleDetails == null) {
            return MapUtils.create("no", 0, "msg", "数据异常，请联系开发人员");
        }

        if (settleDetails.getTmpStatus() != 0) {
            return MapUtils.create("no", 0, "msg", "该结算节点已标志状态");
        }
        //当前时间
        Timestamp timestamp = new Timestamp(System.currentTimeMillis());
        settleDetails.setTmpStatus(followUpModel.getTmpStatus());
        //离职
        if (followUpModel.getTmpStatus() == 2) {
            settleDetails.setLeaveTime(followUpModel.getLeaveTime());
            settleDetails.setLeaveCause(followUpModel.getLeaveCause());
        }
        settleDetails.setUpdateBy(followUpModel.getUserId());
        settleDetails.setUpdateTime(timestamp);

        settleDetailsDAO.updateSettleDetailsTmpStatus(settleDetails);

        //添加操作记录
        FollowLog followLog = new FollowLog();
        followLog.setEnterpriseId(followUpModel.getEnterpriseId());
        followLog.setSettleLogId(followUpModel.getSettleId());
        followLog.setFollowId(settleDetails.getFollowId());
        followLog.setType(4);
        // 临时状态0 默认 1 结算  2 离职
        if (followUpModel.getTmpStatus() == 1) {
            followLog.setStatus(12);
        } else if (followUpModel.getTmpStatus() == 2) {
            followLog.setStatus(13);
            followLog.setBehaviorTime(followUpModel.getLeaveTime());
            followLog.setFailure(followUpModel.getLeaveCause());
        }

        followLog.setCreateBy(followUpModel.getUserId());
        followLog.setCreateTime(timestamp);

        followLogDAO.save(followLog);

        return MapUtils.create("no", 1, "msg", "修改成功");
    }

    @Override
    public Map getEnterpriseProjectSettleAuthList(QueryFollowUpModel queryFollowUpModel) {
        List<SettleDetailsVO> settleDetails = settleDetailsDAO.getEnterpriseProjectSettleAuthList(queryFollowUpModel);
        int count = settleDetailsDAO.getEnterpriseProjectSettleAuthCount(queryFollowUpModel);
        return MapUtils.create("no", 1, "msg", "获取成功", "results", settleDetails, "count", count);
    }

    /**
     * 结算确认 -- 结算、离职操作
     *
     * @param followUpModel
     * @return
     */
    @Override
    @Transactional
    public Map updateEnterpriseSettleAuth(FollowUpModel followUpModel) {
        //数据加锁  防止出现同时操作情况
        if (!redisLockUtils.lock(3, followUpModel.getId(), followUpModel.getUserId(), 1)) {
            return MapUtils.create("no", 0, "msg", "数据异常，请刷新后再操作！");
        }
        //时间
        Timestamp timestamp = new Timestamp(System.currentTimeMillis());
        //结算明细
        SettleDetails settleDetails = settleDetailsDAO.getSettleDetailsById(followUpModel.getId());
        if (settleDetails != null) {
            // 已结算状态
            if (settleDetails.getStatus() != 0) {
                return MapUtils.create("no", 0, "msg", "已结算，请稍后重试");
            }

            // 按结算顺序结算   前面顺序没有结算不能结算
            if (settleDetails.getNode() != 1) {
                int count = settleDetailsDAO.countSettledByFollowId(settleDetails.getFollowId(), settleDetails.getNode());
                if (count > 0) {
                    // 解锁
                    redisLockUtils.lock(3, followUpModel.getId(), followUpModel.getUserId(), 2);
                    return MapUtils.create("no", 0, "msg", "请按顺序完成之前节点的结算。");
                }
            }

            // 是否修改实际结算时间
            // 实际结算时间，第一个节点必须在入职时间之后。其他节点必须在上个节点的实际结算时间之后。
            if (followUpModel.getSettleStatus() == 1 && followUpModel.getActualSettleTime() != null) {
                if (settleDetails.getNode() == 1 && settleDetails.getEntryTime().after(followUpModel.getActualSettleTime())) {
                    // 解锁
                    redisLockUtils.lock(3, followUpModel.getId(), followUpModel.getUserId(), 2);
                    return MapUtils.create("no", 0, "msg", "实际结算时间必须在入职时间之后。");
                } else if (settleDetails.getNode() > 1) {
                    Timestamp prevActualSettleTime = settleDetailsDAO.getPrevActualSettleTime(settleDetails.getFollowId(), settleDetails.getNode());
                    if (prevActualSettleTime.after(followUpModel.getActualSettleTime())) {
                        // 解锁
                        redisLockUtils.lock(3, followUpModel.getId(), followUpModel.getUserId(), 2);
                        // return MapUtils.create("no", 0, "msg", "实际结算时间必须在上个节点的实际结算时间之后。");
                        return MapUtils.create("no", 0, "msg", "未到节点结算时间，暂不可结算");
                    }
                }
            }

            // 处理待办消息
            CrmNoticeRecord record = new CrmNoticeRecord();
            record.setNoticeType(NoticeTypeEnum.HEADHUNTER_REVIEW_SETTLEMENT_RESULT_CONFIRM.getValue());
            record.setTargetId(followUpModel.getId());
            record.setTargetType(3);
            record.setStatus(NoticeStatusEnum.PROCESSED.getValue());
            record.setRead(NoticeReadEnum.READ.getValue());
            record.setUpdateBy(followUpModel.getUserId());
            record.setUpdateTime(new Timestamp(System.currentTimeMillis()));
            noticeRecordService.updateNoticeRecord(record);

            SettleLog settleLog = new SettleLog();
            settleLog.setEnterpriseId(settleDetails.getEnterpriseId());
            settleLog.setFollowId(settleDetails.getFollowId());
            settleLog.setSettleId(settleDetails.getId());
            settleLog.setBehaviorTime(followUpModel.getBehaviorTime());
            settleLog.setPayment(followUpModel.getSettleStatus() == 2 ? BigDecimal.ZERO : settleDetails.getSalary());
            settleLog.setPerformance(followUpModel.getSettleStatus() == 2 ? BigDecimal.ZERO : settleDetails.getSalary());
            settleLog.setCreateBy(followUpModel.getUserId());
            settleLog.setCreateTime(timestamp);
            settleLog.setRemark(followUpModel.getRemark());
            //保存
            settleLogDAO.save(settleLog);
            //更新结算明细
            SettleDetails settleDetailsUpdate = new SettleDetails();
            settleDetailsUpdate.setId(settleDetails.getId());
            settleDetailsUpdate.setStatus(followUpModel.getSettleStatus());
            settleDetailsUpdate.setActualSettleTime(followUpModel.getActualSettleTime());
            settleDetailsUpdate.setUpdateBy(followUpModel.getUserId());
            settleDetailsUpdate.setUpdateTime(timestamp);
            settleDetailsUpdate.setReviewTime(timestamp);
            settleDetailsUpdate.setReviewUserId(followUpModel.getUserId());
            settleDetailsDAO.updateSettleDetailsStatus(settleDetailsUpdate);

            // 发放提成
            Integer userType = followUpDAO.getFollowUpUserCommissionInfoById(settleDetails.getFollowId());
            if(userType == 0){
                commissionGaveLogService.gaveCommission(settleDetails.getId(), followUpModel.getSettleStatus() == 2 ? BigDecimal.ZERO : settleDetails.getSalary());
            }else if (userType == 1){
                commissionGaveLogService.gavePartTimeJobCommission(settleDetails.getId(), followUpModel.getSettleStatus() == 2 ? BigDecimal.ZERO : settleDetails.getSalary());
            }

            //添加操作记录
            FollowLog followLog = new FollowLog();
            followLog.setEnterpriseId(followUpModel.getEnterpriseId());
            followLog.setSettleLogId(settleLog.getId());
            followLog.setFollowId(settleDetails.getFollowId());
            followLog.setType(4);
            // settleStatus 1 结算 2 离职  (没办法做到统一  手动匹配)
            if (followUpModel.getSettleStatus() == 1) {
                followLog.setStatus(10);
            } else if (followUpModel.getSettleStatus() == 2) {
                followLog.setStatus(11);
            }
            followLog.setBehaviorTime(followUpModel.getBehaviorTime());
            followLog.setCreateBy(followUpModel.getUserId());
            followLog.setCreateTime(timestamp);
            followLog.setRemark(followUpModel.getRemark());
            followLogDAO.save(followLog);

            FollowUp followUp = followUpDAO.getFollowUpById(settleDetails.getFollowId());

            //处理企业端待办消息
            CrmEnterpriseNoticeRecord recordEnterprise = new CrmEnterpriseNoticeRecord();
            recordEnterprise.setContactId(followUp.getContactId());
            recordEnterprise.setProjectId(followUp.getProjectId());
            recordEnterprise.setNoticeType(4);
            recordEnterprise.setType(1);
            recordEnterprise.setStatus(4);
            recordEnterprise.setRead(1);
            recordEnterprise.setUpdateBy(followUpModel.getUserId());
            recordEnterprise.setUpdateTime(new Timestamp(System.currentTimeMillis()));
            enterpriseNoticeRecordService.updateNoticeRecord(recordEnterprise);

            // 合同信息 获取客户id 合同id 付费类型
            JSONObject jsonObjectContract = null;
            Object contractInfoResult = projectService.getContractInfo(followUp.getProjectId());
            JSONObject jsonObjectResult = JSONObject.parseObject(JSON.toJSON(contractInfoResult).toString());
            if (jsonObjectResult.getInteger("no") != null && jsonObjectResult.getInteger("no").equals(200)) {
                jsonObjectContract = jsonObjectResult.getJSONObject("detail");
            }

            // 创建充值记录
            RechargeLog rechargeLog = new RechargeLog();
            rechargeLog.setEnterpriseId(settleDetails.getEnterpriseId());
            rechargeLog.setProjectId(followUp.getProjectId());
            //rechargeLog.setSalary(followUpModel.getSettleStatus() == 2 ? 0 : - settleDetails.getSalary());
            rechargeLog.setCreateBy(followUpModel.getUserId());
            rechargeLog.setCreateTime(timestamp);
            if (followUpModel.getSettleStatus() == 1) {
                rechargeLog.setRemark("结算扣款");
                rechargeLog.setTransactionType(RechargeLogTransactionTypeEnum.SETTLEMENT.getCode());
            } else {
                rechargeLog.setRemark("离职扣款");
                rechargeLog.setTransactionType(RechargeLogTransactionTypeEnum.RESIGNATION_DEDUCTION.getCode());
            }

            if(jsonObjectContract != null){
                Integer paymentType = jsonObjectContract.getInteger("paymentType");
                Integer source = jsonObjectContract.getInteger("source");
                BigDecimal rate = BigDecimal.ZERO;
                if(source == 3){
                    rate = jsonObjectContract.getBigDecimal("rate");
                }
                rechargeLog.setCustomerId(jsonObjectContract.getInteger("customerId"));
                rechargeLog.setContractId(jsonObjectContract.getInteger("id"));
                if (paymentType != null) {
                    rechargeLog.setType(paymentType);
                    if(source == 3){
                        BigDecimal salary = settleDetails.getSalary().divide(rate, 10, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));
                        rechargeLog.setSalary(followUpModel.getSettleStatus() == 2 ? BigDecimal.ZERO : paymentType == 1 ? salary.negate() : salary);
                    } else {
                        rechargeLog.setSalary(followUpModel.getSettleStatus() == 2 ? BigDecimal.ZERO : paymentType == 1 ? settleDetails.getSalary().negate() : settleDetails.getSalary());
                    }
                } else {
                    rechargeLog.setSalary(followUpModel.getSettleStatus() == 2 ? BigDecimal.ZERO : settleDetails.getSalary().negate());
                }
            }else{
                rechargeLog.setType(1);
                rechargeLog.setSalary(followUpModel.getSettleStatus() == 2 ? BigDecimal.ZERO : settleDetails.getSalary().negate());
            }

            rechargeLogDAO.save(rechargeLog);
            if(rechargeLog.getCustomerId() != null) {
                if (rechargeLog.getType() != null) {
                    if (rechargeLog.getType() == 1) {
                        rechargeLogDAO.updataBalance(rechargeLog.getSalary(), rechargeLog.getCustomerId(), timestamp);
                    } else {
                        rechargeLogDAO.updataPayment(rechargeLog.getSalary(), rechargeLog.getCustomerId(), timestamp);
                    }
                } else {
                    rechargeLogDAO.updataBalance(rechargeLog.getSalary(), rechargeLog.getCustomerId(), timestamp);
                }
            }

            //离职情况  所有后面未结算的记录，都变成离职
            if (followUpModel.getSettleStatus() == 2) {
                //查询当前节点后的节点
                List<SettleDetails> settleDetailsList = settleDetailsDAO.getSettleDetailsListById(settleDetails.getFollowId(), settleDetails.getNode());
                if (!settleDetailsList.isEmpty()) {
                    for (SettleDetails settleDetail : settleDetailsList) {

                        //保存
                        SettleLog resignSettleLog = new SettleLog();
                        resignSettleLog.setEnterpriseId(settleDetail.getEnterpriseId());
                        resignSettleLog.setFollowId(settleDetail.getFollowId());
                        resignSettleLog.setSettleId(settleDetail.getId());
                        resignSettleLog.setBehaviorTime(followUpModel.getBehaviorTime());
                        resignSettleLog.setPayment(BigDecimal.ZERO);
                        resignSettleLog.setPerformance(BigDecimal.ZERO);
                        resignSettleLog.setCreateBy(followUpModel.getUserId());
                        resignSettleLog.setCreateTime(timestamp);
                        resignSettleLog.setRemark(followUpModel.getRemark());
                        settleLogDAO.save(resignSettleLog);

                        //更新结算明细
                        SettleDetails updateSettleDetails = new SettleDetails();
                        updateSettleDetails.setId(settleDetail.getId());
                        updateSettleDetails.setStatus(2);
                        updateSettleDetails.setUpdateBy(followUpModel.getUserId());
                        updateSettleDetails.setUpdateTime(timestamp);
                        settleDetailsDAO.updateSettleDetailsStatus(updateSettleDetails);

                        // 发放提成
                        commissionGaveLogService.gaveCommission(settleDetail.getId(), BigDecimal.ZERO);

                        //添加操作记录
                        FollowLog resignFollowLog = new FollowLog();
                        resignFollowLog.setEnterpriseId(followUpModel.getEnterpriseId());
                        resignFollowLog.setSettleLogId(settleLog.getId());
                        resignFollowLog.setFollowId(settleDetail.getFollowId());
                        resignFollowLog.setType(4);
                        resignFollowLog.setStatus(11);
                        resignFollowLog.setBehaviorTime(followUpModel.getBehaviorTime());
                        resignFollowLog.setCreateBy(followUpModel.getUserId());
                        resignFollowLog.setCreateTime(timestamp);
                        resignFollowLog.setRemark(followUpModel.getRemark());
                        followLogDAO.save(resignFollowLog);

                        // 创建充值记录
                        RechargeLog resignRechargeLog = new RechargeLog();
                        resignRechargeLog.setEnterpriseId(settleDetail.getEnterpriseId());
                        resignRechargeLog.setProjectId(followUp.getProjectId());
                        resignRechargeLog.setSalary(BigDecimal.ZERO);
                        resignRechargeLog.setCreateBy(followUpModel.getUserId());
                        resignRechargeLog.setCreateTime(timestamp);
                        resignRechargeLog.setRemark("离职扣款");
                        resignRechargeLog.setTransactionType(RechargeLogTransactionTypeEnum.RESIGNATION_DEDUCTION.getCode());
                        if(jsonObjectContract != null){
                            Integer paymentType = jsonObjectContract.getInteger("paymentType");
                            resignRechargeLog.setCustomerId(jsonObjectContract.getInteger("customerId"));
                            resignRechargeLog.setContractId(jsonObjectContract.getInteger("id"));
                            resignRechargeLog.setType(paymentType == null ? 1 : paymentType);
                        }else {
                            resignRechargeLog.setType(1);
                        }
                        rechargeLogDAO.save(resignRechargeLog);
                    }
                }
            }

            //未结算数量
            int unSettleCount = settleDetailsDAO.unSettleCount(settleDetails.getFollowId());
            //更新主数据
            FollowUp followUpUpdate = new FollowUp();
            followUpUpdate.setId(settleDetails.getFollowId());
            //全部结算 变成完成状态
            if (unSettleCount == 0) {
                followUpUpdate.setFinish(1);
                //离职数量
                int resignedCount = settleDetailsDAO.getSettleCountByFollowIdStatus(settleDetails.getFollowId(), 2);
                // 需求规定  有一个离职 都算离职
                if (resignedCount > 0) {
                    followUpUpdate.setCurrentStatus(11);
                } else {
                    followUpUpdate.setCurrentStatus(10);
                }
            }
            followUpUpdate.setUpdateBy(followUpModel.getUserId());
            followUpUpdate.setUpdateTime(timestamp);
            followUpDAO.updateFollowUp(followUpUpdate);

            // 结算后处理项目紧急状态
            projectService.updateProjectDangerBySurplusQuota(followUp.getProjectId(), followUpModel.getUserId());

            //解锁   解锁操作  理论上下边 else 异常也要解锁   但是能到else 肯定是非法操作  强制锁5秒钟数据 起到安全保证作用
            redisLockUtils.lock(3, followUpModel.getId(), followUpModel.getUserId(), 2);
            return MapUtils.create("no", 1, "msg", "操作成功", "projectId", followUp.getProjectId());
        }
        return MapUtils.create("no", 0, "msg", "数据异常，请稍后重试");
    }
}
