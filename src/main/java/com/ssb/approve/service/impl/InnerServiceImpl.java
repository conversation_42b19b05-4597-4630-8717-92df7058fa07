package com.ssb.approve.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;
import com.ssb.approve.dao.FollowRecommendDAO;
import com.ssb.approve.dao.FollowUpDAO;
import com.ssb.approve.dao.QuitRecordDAO;
import com.ssb.approve.entity.CrmFollowRecommend;
import com.ssb.approve.model.vo.FollowUpVO;
import com.ssb.approve.service.InnerService;
import com.ssb.approve.utils.MapUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * @ClassName: InnerServiceImpl
 * @Description: 内部Service impl
 * @Author: YZK
 * @Date: 2020年02月20日09:44:03
 **/
@Slf4j
@Service
public class InnerServiceImpl implements InnerService {

    @Autowired
    private QuitRecordDAO quitRecordDAO;

    @Autowired
    private FollowUpDAO followUpDAO;

    @Resource
    private FollowRecommendDAO followRecommendDAO;

    /**
     * 获取联系人集合
     * 招聘跟进查询
     *
     * @param fromUserId
     * @return
     */
    @Override
    public Map getContactsFromFollowUp(Integer fromUserId) {

        List<Integer> handoverIds = Lists.newArrayList();
        // 未完结状态跟进 or 完结状态 结算完成 30天内的数据
        List<FollowUpVO> followUps = followUpDAO.getFollowUpNoFinishByCreateBy(fromUserId, 30);
        for (FollowUpVO followUp : followUps) {
            handoverIds.add(followUp.getContactId());
        }

        return MapUtils.create("no", 200, "msg", "获取成功", "handoverIds", handoverIds);
    }

    @Override
    public Map getProjectsNotInvolvedFollowCount(List<Map<String, Object>> list) {

        list.forEach(map -> {
            Integer projectId = Integer.parseInt(map.get("id") + "");
            Integer source = Integer.parseInt(map.get("source") + "");

            // 查询待约面数量
            int invitationCount = 0;
            if (source == 0) {
                invitationCount = followUpDAO.countWaitingForAgreedInterview(projectId);
            } else if (source == 3) {
                invitationCount = followUpDAO.countWaitingForAgreedInterviewForShared(projectId);
            }
            map.put("invitationCount", invitationCount);

            // 查询待面试数量
            int interviewCount = 0;
            if (source == 0) {
                interviewCount = followUpDAO.countWaitingForInterview(projectId);
            } else if (source == 3) {
                interviewCount = followUpDAO.countWaitingForInterviewForShared(projectId);
            }
            map.put("interviewCount", interviewCount);

            // 查询待入职数量
            int entryCount = 0;
            if (source == 0) {
                entryCount = followUpDAO.countWaitingForEntry(projectId);
            } else if (source == 3) {
                entryCount = followUpDAO.countWaitingForEntryForShared(projectId);
            }
            map.put("entryCount", entryCount);

            // 查询结算数量
            int settlementCount = 0;
            if (source == 0) {
                settlementCount = followUpDAO.countSettlement(projectId);
            } else if (source == 3) {
                settlementCount = followUpDAO.countSettlementForShared(projectId);
            }
            map.put("settlementCount", settlementCount);
        });

        return MapUtils.create("no", 200, "msg", "获取成功", "list", list);
    }

    @Override
    public Map getProjectsInvolvedFollowCount(List<Map<String, Object>> list) {

        list.forEach(map -> {
            Integer projectId = Integer.parseInt(map.get("id") + "");
            Integer userId = Integer.parseInt(map.get("userId") + "");

            // 查询待约面数量
            int invitationCount = followUpDAO.getInvitationCountByUserIdAndProjectId(projectId, userId);
            map.put("invitationCount", invitationCount);

            // 查询待面试数量
            int interviewCount = followUpDAO.getInterviewCountByUserIdAndProjectId(projectId, userId);
            map.put("interviewCount", interviewCount);

            // 查询待入职数量
            int entryCount = followUpDAO.getEntryCountByUserIdAndProjectId(projectId, userId);
            map.put("entryCount", entryCount);

            // 查询结算数量
            int settlementCount = followUpDAO.getSettlementCountByUserIdAndProjectId(projectId, userId);
            map.put("settlementCount", settlementCount);
        });

        return MapUtils.create("no", 200, "msg", "获取成功", "list", list);
    }

    @Override
    public Map getResponsibleProjectFollowCount(List<Map<String, Object>> list) {

        list.forEach(map -> {
            Integer projectId = Integer.parseInt(map.get("id") + "");

            // 查询待约面数量
            int invitationCount = followUpDAO.countWaitingForAgreedInterview(projectId);
            map.put("invitationCount", invitationCount);

            // 查询待面试数量
            int interviewCount = followUpDAO.countWaitingForInterview(projectId);
            map.put("interviewCount", interviewCount);

            // 查询待入职数量
            int entryCount = followUpDAO.countWaitingForEntry(projectId);
            map.put("entryCount", entryCount);

            // 查询结算数量
            int settlementCount = followUpDAO.countSettlement(projectId);
            map.put("settlementCount", settlementCount);
        });

        return MapUtils.create("no", 200, "msg", "获取成功", "list", list);
    }

    @Override
    public Map getExternalOrdersFollowCount(List<Map<String, Object>> list) {

        list.forEach(map -> {
            Integer projectId = Integer.parseInt(map.get("id") + "");

            // 查询待约面数量
            int invitationCount = followUpDAO.countWaitingForAgreedInterviewForSharedByShareProjectId(projectId);
            map.put("invitationCount", invitationCount);

            // 查询待面试数量
            int interviewCount = followUpDAO.countWaitingForInterviewForSharedByShareProjectId(projectId);
            map.put("interviewCount", interviewCount);

            // 查询待入职数量
            int entryCount = followUpDAO.countWaitingForEntryForSharedByShareProjectId(projectId);
            map.put("entryCount", entryCount);

            // 查询结算数量
            int settlementCount = followUpDAO.countSettlementForSharedByShareProjectId(projectId);
            map.put("settlementCount", settlementCount);
        });

        return MapUtils.create("no", 200, "msg", "获取成功", "list", list);
    }

    @Override
    public Map getProjectsRecommendCount(Map<String, Object> params) {
        List<Integer> projectIds = JSONArray.parseArray(JSONArray.toJSONString(params.get("projectIds")), Integer.class);
        int customerEnterpriseId = Integer.parseInt(params.get("customerEnterpriseId").toString());
        List<Map<String, Integer>> list = followRecommendDAO.getProjectsRecommendCount(projectIds, customerEnterpriseId);
        return MapUtils.create("no", 200, "details", list);
    }

    @Override
    public List<CrmFollowRecommend> getFollowUpRecRemark(Integer projectId, Integer customerEnterpriseId, Integer resumeId) {
        return followRecommendDAO.getFollowUpRecRemark(projectId, customerEnterpriseId, resumeId);
    }
}
