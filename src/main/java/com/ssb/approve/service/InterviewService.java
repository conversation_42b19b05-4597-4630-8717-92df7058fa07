package com.ssb.approve.service;

import com.ssb.approve.model.FollowUpModel;
import com.ssb.approve.model.QueryFollowUpModel;

import java.sql.Timestamp;
import java.util.Map;

/**
 * @ClassName: InterviewService
 * @Description: 面试 Service
 * @Author: YZK
 * @Date: 2019年12月23日15:16:14
 **/
public interface InterviewService {

    /**
     * 获取已约待面试列表
     *
     * @param queryFollowUpModel
     * @return
     */
    Map getInterviewList(QueryFollowUpModel queryFollowUpModel);

    /**
     * 更新面试结果状态
     *
     * @param followUpModel
     * @return
     */
    Map updateInterview(FollowUpModel followUpModel);

    /**
     * 获取面试确认列表
     *
     * @param queryFollowUpModel
     * @return
     */
    Map getInterviewAuthList(QueryFollowUpModel queryFollowUpModel);

    /**
     * 面试确认 -- 审核操作
     *
     * @param id 面试记录表id
     * @param userId 操作人id
     * @return
     */
    Map updateInterviewAuth(Integer id, Integer userId);

    /**
     * 面试审核 跟进操作
     *
     * @param followUpModel
     * @return
     */
    Map interviewAuthFollowUp(FollowUpModel followUpModel);

    /**
     * 获取面试每日上线数
     *
     * @param projectId
     * @param days
     * @return
     */
    Map getInterviewDailyLimit(Integer projectId, Integer days);

    /**
     * 检测面试上限
     * 指定日期
     * @param projectId         项目
     * @param timestamp      时间
     * @return 200 正常  201 已达到上限  500 请求异常
     */
    Integer checkInterviewLimit(Integer projectId, Timestamp timestamp);

    /**
     * 企业端获取面试确认列表
     * @param queryFollowUpModel
     * @return
     */
    Map getEnterpriseProjectInterviewAuthList(QueryFollowUpModel queryFollowUpModel);

    /**
     * 企业端 面试审核 跟进操作
     *
     * @param followUpModel
     * @return
     */
    Map interviewEnterpriseAuthFollowUp(FollowUpModel followUpModel);

    Map copyLink(Integer projectId, Integer interviewRecordId, Integer userId, Integer enterpriseId);
}
