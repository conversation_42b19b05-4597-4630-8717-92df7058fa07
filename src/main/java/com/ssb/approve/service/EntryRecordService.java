package com.ssb.approve.service;

import com.ssb.approve.model.FollowUpModel;
import com.ssb.approve.model.QueryFollowUpModel;

import java.util.Map;

/**
 * @ClassName: EntryRecordService
 * @Description: 入职记录 service
 * @Author: YZK
 * @Date: 2019年12月25日14:24:01
 **/
public interface EntryRecordService {

    /**
     * 获取发offer列表
     *
     * @param queryFollowUpModel
     * @return
     */
    Map getOnlyOfferList(QueryFollowUpModel queryFollowUpModel);

    /**
     * 更新发offer 操作
     *
     * @param followUpModel
     * @return
     */
    Map updateOnlyOffer(FollowUpModel followUpModel);

    /**
     * 获取待入职列表
     *
     * @param queryFollowUpModel
     * @return
     */
    Map getEntryRecordList(QueryFollowUpModel queryFollowUpModel);

    /**
     * 更新入职结果
     *
     * @param followUpModel
     * @return
     */
    Map updateEntryRecord(FollowUpModel followUpModel);

    /**
     * 获取入职确认列表
     *
     * @param queryFollowUpModel
     * @return
     */
    Map getEntryAuthList(QueryFollowUpModel queryFollowUpModel);

    /**
     * 入职确认 -- 审核操作更新
     *
     * @param id
     * @param userId
     * @return
     */
    Map updateEntryAuth(Integer id, Integer userId);

    /**
     * 入职确认 -- 跟进操作
     *
     * @return
     */
    Map entryAuthFollowUp(FollowUpModel followUpModel);

    /**
     * 企业端 获取入职确认列表
     *
     * @param queryFollowUpModel
     * @return
     */
    Map getEnterpriseProjectEntryAuthList(QueryFollowUpModel queryFollowUpModel);

    /**
     * 企业端 入职确认 -- 审核操作更新
     *
     * @return
     */
    Map entryEnterpriseAuthFollowUp(FollowUpModel followUpModel);
}
