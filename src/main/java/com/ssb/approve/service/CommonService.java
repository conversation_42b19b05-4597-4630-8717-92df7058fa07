package com.ssb.approve.service;

import com.ssb.approve.model.QueryFollowUpModel;
import com.ssb.approve.model.vo.CardUserVO;
import com.ssb.approve.model.vo.FollowUpVO;

import java.util.List;
import java.util.Map;

/**
 * @ClassName: CommonService
 * @Description: 公共 service
 * @Author: YZK
 * @Date: 2020年01月14日14:53:563
 **/
public interface CommonService {


    /**
     * 检测项目暂停状态
     *
     * @param queryFollowUpModel
     * @return
     */
    Map checkSuspend(QueryFollowUpModel queryFollowUpModel);

    /**
     * 处理项目预计明细
     *
     * @param followUpVOList
     * @return
     */
    List<FollowUpVO> handleProjectExpectDetail(List<FollowUpVO> followUpVOList);

    /**
     * 单节点处理项目预计明细
     *
     * @param followUpVOList
     * @return
     */
    List<FollowUpVO> singleNodeHandleProjectExpectDetail(List<FollowUpVO> followUpVOList);

    /**
     * 返回用户类型以及相关信息
     * @param type 1/个人 2/团队
     */
    CardUserVO getCardUserInfo(Integer userId, Integer eid, Integer deptId, Integer type);
}
