package com.ssb.approve.service;

import com.ssb.approve.entity.CommissionStrategy;
import com.ssb.approve.entity.CommissionStrategyDifficulty;
import com.ssb.approve.model.CommissionStrategyModel;
import com.ssb.approve.model.CommissionStrategyRatioModel;
import com.ssb.approve.model.QueryCommissionStrategyModel;

import java.util.List;
import java.util.Map;

/**
 * @Author: WangShiyong
 * @Date: 2020/12/22 9:22 AM
 * @Description:
 */
public interface CommissionStrategyService {

//    /**
//     * 提成策略列表
//     * @param queryCommissionStrategyModel
//     * @return
//     */
//    Map getStrategyList(QueryCommissionStrategyModel queryCommissionStrategyModel);
//
//    /**
//     * 新建提成策略
//     * @param commissionStrategy
//     * @return
//     */
//    Map insertStrategy(CommissionStrategy commissionStrategy);
//
//    /**
//     * 更新策略名称
//     * @param commissionStrategyModel
//     * @return
//     */
//    Map updateStrategy(CommissionStrategyModel commissionStrategyModel);
//
//    /**
//     * 管理提成级别列表
//     * @return
//     */
//    Map getCommissionLevelList(Integer enterpriseId);
//
//    /**
//     * 包含猎头与销售
//     * @param enterpriseId
//     * @return
//     */
//    Map getCommissionLevelListV2(Integer enterpriseId);
//
//    /**
//     * 新增/修改提成策略
//     * @param list
//     * @param userId
//     * @return
//     */
//    Map saveStrategyRatio(List<CommissionStrategyRatioModel> list, Integer userId);
//
//    /**
//     * 新增/修改行业难度
//     * @param difficulty
//     * @return
//     */
//    Map saveStrategyDifficulty(CommissionStrategyDifficulty difficulty);
//
//    /**
//     * 策略详情
//     * @param id
//     * @return
//     */
//    Map getStrategyInfo(Integer id);
//
//    /**
//     * 删除策略
//     * @param id
//     * @param userId
//     * @return
//     */
//    Map deleteStrategy(Integer id, Integer userId);
//
//    /**
//     * 新增销售削减比例
//     * @param id
//     * @param userId
//     * @param commission
//     * @return
//     */
//    Map saveSaleCommissionCut(Integer id, Integer userId, Integer enterpriseId, String commission);
//
//    /**
//     * 行业岗位难度列表
//     * @param id
//     * @param userId
//     * @param industryId
//     * @param positionId
//     * @return
//     */
//    Map getStrategyDifficultyList(Integer id, Integer userId, Integer industryId, Integer positionId);
//
//    /**
//     * 行业岗位修改 对应提成策略难度修改
//     * @param map
//     * @return
//     */
//    Map updateStrategyDifficultyList(Map<String,Object> map);
}
