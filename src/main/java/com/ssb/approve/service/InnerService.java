package com.ssb.approve.service;

import com.ssb.approve.entity.CrmFollowRecommend;

import java.util.List;
import java.util.Map;

/**
 * @ClassName: InnerService
 * @Description: 内部Service
 * @Author: YZK
 * @Date: 2020年02月20日09:44:03
 **/
public interface InnerService {

    /**
     * 获取联系人集合
     * 招聘跟进查询
     *
     * @param fromUserId
     * @return
     */
    Map getContactsFromFollowUp(Integer fromUserId);

    /**
     * 项目公海相关数量
     * @param list
     * @return
     */
    Map getProjectsNotInvolvedFollowCount(List<Map<String, Object>> list);

    /**
     * 我参与的跟进数量
     * @param list
     * @return
     */
    Map getProjectsInvolvedFollowCount(List<Map<String, Object>> list);

    /**
     * 我负责的跟进数量
     * @param list
     * @return
     */
    Map getResponsibleProjectFollowCount(List<Map<String, Object>> list);

    /**
     * 未合作的跟进数量
     * @param list
     * @return
     */
    Map getExternalOrdersFollowCount(List<Map<String, Object>> list);


    /**
     * 企业端项目推荐简历人数
     *
     * @param params
     * @return
     */
    Map getProjectsRecommendCount(Map<String, Object> params);

    List<CrmFollowRecommend> getFollowUpRecRemark(Integer projectId, Integer customerEnterpriseId, Integer resumeId);
}
