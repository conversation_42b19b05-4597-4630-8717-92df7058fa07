package com.ssb.approve.service.client;

import com.ssb.approve.model.dto.RecruitmentDataDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;


/**
 * @ClassName: UserService
 * @Description: 用户 client service
 * @Author: YZK
 * @Date: 2019年12月26日18:48:03
 **/
@Component
@FeignClient(name = "crm-user", fallbackFactory = UserServiceFallback.class)
public interface UserService {

    /**
     * 获取用户组织架构  ids
     *
     * @param userId
     * @param eid
     * @return
     */
    @GetMapping("/api/user/getOrganizationUserId/{userId}/{eid}")
    Object getOrganizationUserId(@PathVariable("userId") Integer userId, @PathVariable("eid")Integer eid);

    /**
     * 获取部门及其子部门
     *
     * @return
     */
    @GetMapping("/api/dept/getDeptDetails")
    Object getDeptDetails();

    /**
     * 获取当前用户组织架构及以上的成员id
     * 返回整个组织架构上的全部成员信息
     *
     * @param userId
     * @param eid
     * @return
     */
    @GetMapping("/api/user/getAboveOrganizationUsers/{userId}/{eid}")
    Object getAboveOrganizationUsers(@PathVariable("userId") Integer userId, @PathVariable("eid") Integer eid);

    /**
     * 根据部门id查询所有子部门id
     * @param deptId
     * @return
     * */
    @GetMapping("/api/dept/getSubDept")
    public Object getSubDept(@RequestParam("eid") Integer eid, @RequestParam("deptId") Integer deptId);

    @GetMapping("/api/role/getUserRoleMenuDetailsForServerV2/{uid}")
    Object getUserRoleMenuDetailsForServerV2(@PathVariable("uid") Integer uid);

    @GetMapping("/api/user/isAdmin")
    int isAdmin(@RequestParam("userId") Integer userId);

    @RequestMapping(value = "/api/user/getLoginUserDetailsForLogin/{uid}/{eid}", method = RequestMethod.GET)
    Object getLoginUserDetailsForLogin(@PathVariable("uid") Integer uid, @PathVariable("eid") Integer eid);

    @GetMapping("/api/user/getCardUserInfo/{userId}/{eid}/{deptId}/{type}")
    Object getCardUserInfo(@PathVariable("userId") Integer userId, @PathVariable("eid") Integer eid, @PathVariable("deptId") Integer deptId, @PathVariable("type") Integer type);

    @GetMapping("/api/user/getUserInfo/{userId}/{eid}/{deptId}")
    Object getUserInfo(@PathVariable("userId") Integer userId, @PathVariable("eid") Integer eid, @PathVariable("deptId") Integer deptId);

    @PostMapping("/api/workload/getDataBoardWorkLoad")
    Object getDataBoardWorkLoad(@RequestBody RecruitmentDataDTO dto);
}
