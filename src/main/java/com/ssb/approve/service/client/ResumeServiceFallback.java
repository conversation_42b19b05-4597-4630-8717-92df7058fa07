package com.ssb.approve.service.client;

import com.ssb.approve.model.dto.DailyDataDTO;
import com.ssb.approve.model.dto.NewlyAddedDataDTO;
import com.ssb.approve.model.dto.NewlyAddedResumeDataListDTO;
import com.ssb.approve.utils.MapUtils;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @Author: WangShiyong
 * @Date: 2021/9/13 10:55 AM
 * @Description:
 */
@Slf4j
@Component
public class ResumeServiceFallback implements FallbackFactory<ResumeService> {

    private static final String ERR_MSG = "CRM-RESUME服务接口异常";


    @Override
    public ResumeService create(Throwable throwable) {
        String msg = throwable == null ? "" : throwable.getMessage();
        if (!"".equals(msg)) {
            log.error(ERR_MSG + msg);
        }

        return new ResumeService(){
            /**
             * 更新app用户
             *
             * @param contactId 简历用户id
             * @param type      0转化结果 1跟进状态
             * @param param     值
             * @return
             */
            @Override
            public Object updateAppResume(Integer contactId, Integer type, Integer param) {
                //理论上可以进行三次尝试
                log.error("pushMsgToCommon方法 updateAppResume 推送失败 contactId: " + contactId + ",type:" + type + ",param:" + param);
                return MapUtils.create("no", 500, "msg", "请求异常");
            }

            @Override
            public Object updateHeadhuntingServiceFeedback(Integer contactId) {
                //理论上可以进行三次尝试
                log.error("pushMsgToCommon方法 updateHeadhuntingServiceFeedback 推送失败 contactId: " + contactId);
                return MapUtils.create("no", 500, "msg", "请求异常");
            }

            @Override
            public Object getNewlyAddedResumeDate(NewlyAddedDataDTO dto) {
                return MapUtils.create("no", 500, "msg", "请求异常");
            }

            @Override
            public Object getNewlyAddedResumeList(NewlyAddedResumeDataListDTO dto) {
                return MapUtils.create("no", 500, "msg", "请求异常");
            }
            @Override
            public Object getDailyDataResumeCount(DailyDataDTO dto) {
                return MapUtils.create("no", 500, "msg", "请求异常");
            }
        };
    }
}
