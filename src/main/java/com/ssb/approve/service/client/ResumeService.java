package com.ssb.approve.service.client;

import com.ssb.approve.model.dto.DailyDataDTO;
import com.ssb.approve.model.dto.NewlyAddedDataDTO;
import com.ssb.approve.model.dto.NewlyAddedResumeDataListDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * @Author: WangShiyong
 * @Date: 2021/9/13 10:55 AM
 * @Description:
 */
@Component
@FeignClient(name = "crm-resume", fallbackFactory = ResumeServiceFallback.class)
public interface ResumeService {

    /**
     * 更新app用户
     * @param contactId  简历用户id
     * @param type       0转化结果 1跟进状态
     * @param param      值
     * @return
     */
    @PostMapping("/api/provide/updateAppResume")
    Object updateAppResume(@RequestParam("contactId")Integer contactId, @RequestParam("type") Integer type, @RequestParam("param") Integer param);

    @PostMapping("/api/provide/updateHeadhuntingServiceFeedback")
    Object updateHeadhuntingServiceFeedback(@RequestParam("contactId")Integer contactId);

    @PostMapping("/api/provide/getNewlyAddedResumeDate")
    Object getNewlyAddedResumeDate(@RequestBody NewlyAddedDataDTO dto);

    @PostMapping("/api/provide/getNewlyAddedResumeList")
    Object getNewlyAddedResumeList(@RequestBody NewlyAddedResumeDataListDTO dto);

    @PostMapping("/api/provide/getDailyDataResumeCount")
    Object getDailyDataResumeCount(@RequestBody DailyDataDTO dto);
}
