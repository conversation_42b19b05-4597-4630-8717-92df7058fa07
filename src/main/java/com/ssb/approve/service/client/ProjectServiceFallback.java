package com.ssb.approve.service.client;

import com.ssb.approve.model.dto.*;
import com.ssb.approve.utils.MapUtils;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.Map;

/**
 * @ClassName: ProjectServiceFallback
 * @Description: 项目 client service Fallback
 * @Author: YZK
 * @Date: 2019年12月26日18:48:58
 **/
@Slf4j
@Component
public class ProjectServiceFallback implements FallbackFactory<ProjectService> {

    private static final String ERR_MSG = "CRM-PROJECT服务接口异常";

    @Override
    public ProjectService create(Throwable throwable) {

        String msg = throwable == null ? "" : throwable.getMessage();
        if (!"".equals(msg)) {
            log.error(ERR_MSG + msg);
        }

        return new ProjectService() {
            @Override
            public Object getSettleDetails(Integer id) {
                //理论上可以进行三次尝试
                log.error("pushMsgToCommon方法 getSettleDetails 推送失败 acceptId " + id);
                return MapUtils.create("no", 500, "msg", "请求异常");
            }

            @Override
            public Object getProjectInfo(Integer projectId) {
                log.error("pushMsgToCommon方法 getProjectInfo 推送失败 acceptId " + projectId);
                return MapUtils.create("no", 500, "msg", "请求异常");
            }

            @Override
            public Object getContractInfo(Integer projectId) {
                log.error("getContractInfo 推送失败 projectId:" + projectId);
                return MapUtils.create("no", 500, "msg", "请求异常");
            }

            @Override
            public Object getCustomerCountAndProjectCount(Integer enterpriseId) {
                log.error("getCustomerCountAndProjectCount 推送失败 enterpriseId:" + enterpriseId);
                return MapUtils.create("no", 500, "msg", "请求异常");
            }

            @Override
            public Object updateProjectDanger(Integer projectId, Integer userId) {
                return MapUtils.create("no", 500, "msg", "请求异常");
            }

            @Override
            public Object updateProjectDangerBySurplusQuota(Integer projectId, Integer userId) {
                return MapUtils.create("no", 500, "msg", "请求异常");
            }

            @Override
            public Map<String, Object> checkSettleQuotaAndBalanceByEnterprise(Integer projectId) {
                return MapUtils.create("no", 500, "msg", "请求异常");
            }

            @Override
            public Object getProjectIdByShareStatusAndProjectId(Integer projectId) {
                return MapUtils.create("no", 500, "msg", "请求异常");
            }
        };
    }
}
