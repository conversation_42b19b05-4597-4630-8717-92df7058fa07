package com.ssb.approve.service.client;

import com.ssb.approve.model.dto.RecruitmentDataDTO;
import com.ssb.approve.utils.MapUtils;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @ClassName: ProjectServiceFallback
 * @Description: 项目 client service Fallback
 * @Author: YZK
 * @Date: 2019年12月26日18:48:58
 **/
@Slf4j
@Component
public class UserServiceFallback implements FallbackFactory<UserService> {

    private static final String ERR_MSG = "CRM-USER服务接口异常";

    @Override
    public UserService create(Throwable throwable) {
        String msg = throwable == null ? "" : throwable.getMessage();
        if (!"".equals(msg)) {
            log.error(ERR_MSG + msg);
        }

        return new UserService() {
            @Override
            public Object getOrganizationUserId(Integer userId, Integer eid) {
                //理论上可以进行三次尝试
                log.error("pushMsgToCommon方法 getOrganizationUserId 推送失败 userId " + userId + " eid" + eid);
                return MapUtils.create("no", 500, "msg", "请求异常");
            }

            @Override
            public Object getDeptDetails() {
                //理论上可以进行三次尝试
                log.error("pushMsgToCommon方法 getDeptDetails 异常 查询不到数据");
                return MapUtils.create("no", 500, "msg", "请求异常");
            }

            @Override
            public Object getAboveOrganizationUsers(Integer userId, Integer eid) {
                //理论上可以进行三次尝试
                log.error("pushMsgToCommon方法 getAboveOrganizationUsers 异常 查询不到数据");
                return MapUtils.create("no", 500, "msg", "请求异常");
            }

            @Override
            public Object getSubDept(Integer eid, Integer deptId) {
                //理论上可以进行三次尝试
                log.error("pushMsgToCommon方法 getSubDept 异常 查询不到数据");
                return MapUtils.create("no", 500, "msg", "请求异常");
            }

            @Override
            public Object getUserRoleMenuDetailsForServerV2(Integer uid) {
                log.error("pushMsgToCommon方法 getUserRoleMenuDetailsForServerV2 推送失败 userId " + uid);
                return MapUtils.create("no", 500, "msg", "请求异常");
            }

            @Override
            public int isAdmin(Integer userId) {
                return 0;
            }

            @Override
            public Object getLoginUserDetailsForLogin(Integer uid, Integer eid) {
                return MapUtils.create("no", 500, "msg", "请求异常");
            }

            @Override
            public Object getCardUserInfo(Integer userId, Integer eid, Integer deptId, Integer type) {
                return MapUtils.create("no", 500, "msg", "请求异常");
            }

            @Override
            public Object getUserInfo(Integer userId, Integer eid, Integer deptId) {
                return MapUtils.create("no", 500, "msg", "请求异常");
            }

            @Override
            public Object getDataBoardWorkLoad(RecruitmentDataDTO dto) {
                return MapUtils.create("no", 500, "msg", "请求异常");
            }
        };
    }


}
