package com.ssb.approve.service.client;

import com.ssb.approve.model.dto.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;

/**
 * @ClassName: ProjectService
 * @Description: 项目 client service
 * @Author: YZK
 * @Date: 2019年12月26日18:48:03
 **/
@Component
@FeignClient(name = "crm-project", fallbackFactory = ProjectServiceFallback.class)
public interface ProjectService {

    /**
     * 获取项目结算节点明细
     *
     * @param projectId 项目id
     * @return
     */
    @PostMapping("/api/provide/getProjectSettle")
    Object getSettleDetails(@RequestParam("projectId") Integer projectId);

    /**
     * 获取项目信息
     *
     * @param projectId 项目id
     * @return
     */
    @PostMapping("/api/provide/getProjectInfo")
    Object getProjectInfo(@RequestParam("projectId") Integer projectId);

    @PostMapping("/api/provide/getContractInfo")
    Object getContractInfo(@RequestParam("projectId") Integer projectId);

    @GetMapping("/api/provide/getCustomerCountAndProjectCount/{enterpriseId}")
    Object getCustomerCountAndProjectCount(@PathVariable("enterpriseId") Integer enterpriseId);

    @PostMapping("/api/provide/updateProjectDanger")
    Object updateProjectDanger(@RequestParam("projectId") Integer projectId, @RequestParam("userId")Integer userId);

    @PostMapping("/api/provide/updateProjectDangerBySurplusQuota")
    Object updateProjectDangerBySurplusQuota(@RequestParam("projectId") Integer projectId, @RequestParam("userId")Integer userId);
}
