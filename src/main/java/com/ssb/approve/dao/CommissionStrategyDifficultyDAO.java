package com.ssb.approve.dao;

import com.ssb.approve.entity.CommissionStrategyDifficulty;
import com.ssb.approve.model.PositionIndustryModel;
import com.ssb.approve.model.vo.CommissionStrategyDifficultyVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author: WangShiyong
 * @Date: 2020/12/24 9:45 AM
 * @Description:
 */
@Mapper
public interface CommissionStrategyDifficultyDAO {
//
//    int insertDifficulty(CommissionStrategyDifficulty difficulty);
//
//    int updateDifficulty(CommissionStrategyDifficulty difficulty);
//
//    List<CommissionStrategyDifficultyVO> getDifficultyList(@Param("strategyId") Integer strategyId, @Param("industryId")Integer industryId, @Param("positionId")Integer positionId);
//
//    /**
//     * 所有难度信息列表
//     * @return
//     */
//    List<PositionIndustryModel> getAllDifficultyList(@Param("industrySecondId") Integer industrySecondId);
//
//    int batchInsertDifficulty(@Param("difficultyList") List<CommissionStrategyDifficulty> difficultyList);
//
//    int batchInsertPositionIndustry(@Param("list")List<PositionIndustryModel> list);
//
//    int batchUpdatePositionIndustry(@Param("list")List<PositionIndustryModel> list, @Param("userId")Integer userId);
}
