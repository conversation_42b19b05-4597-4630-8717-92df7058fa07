package com.ssb.approve.dao;

import com.ssb.approve.entity.RechargeLog;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.math.BigDecimal;
import java.sql.Timestamp;

/**
 * @ClassName: RechargeLogDAO
 * @Description: 充值记录 dao
 * @Author: YZK
 * @Date: 2019年12月28日15:07:56
 **/
public interface RechargeLogDAO {

    /**
     * 保存充值记录
     *
     * @param rechargeLog
     * @return
     */
    int save(RechargeLog rechargeLog);

    /**
     * 获取剩余金额
     *
     * @param projectId 项目id
     * @return
     */
    int getCurrentAmount(@Param("projectId") Integer projectId);

    //更新账户余额到customer表
    @Update("UPDATE crm_customer SET account_balance = account_balance + #{salary}, update_time =#{updateTime} WHERE id = #{id}")
    int updataBalance(@Param("salary") BigDecimal salary, @Param("id") Integer id, @Param("updateTime") Timestamp updateTime);

    @Update("UPDATE crm_customer SET payment = payment + #{salary}, update_time =#{updateTime} WHERE id = #{id}")
    int updataPayment(@Param("salary") BigDecimal salary, @Param("id") Integer id, @Param("updateTime") Timestamp updateTime);
}
