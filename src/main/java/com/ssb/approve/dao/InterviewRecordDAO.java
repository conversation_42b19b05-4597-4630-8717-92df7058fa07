package com.ssb.approve.dao;

import com.ssb.approve.entity.InterviewRecord;
import com.ssb.approve.model.QueryFollowRevokeModel;
import com.ssb.approve.model.QueryFollowUpModel;
import com.ssb.approve.model.QuitRecordModel;
import com.ssb.approve.model.dto.BasicDataDTO;
import com.ssb.approve.model.dto.ConversionRateDataDTO;
import com.ssb.approve.model.dto.RecruitmentDataDTO;
import com.ssb.approve.model.dto.RecruitmentDataListDTO;
import com.ssb.approve.model.vo.FollowRevokeVO;
import com.ssb.approve.model.vo.InterviewRecordVO;
import com.ssb.approve.model.vo.RecruitmentDataListVO;
import com.ssb.approve.model.vo.StatisticalChartDataVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @ClassName: InterviewRecordDAO
 * @Description: 面试记录 dao
 * @Author: YZK
 * @Date: 2019年12月23日15:16:14
 **/
public interface InterviewRecordDAO {

    /**
     * 保存面试记录
     *
     * @param interviewRecord
     * @return
     */
    int save(InterviewRecord interviewRecord);

    /**
     * 获取面试记录
     * 通过id
     * @param id id
     * @return
     */
    InterviewRecord getInterviewRecordById(@Param("id") Integer id);

    /**
     * 更新面试记录
     *
     * @param interviewRecord
     * @return
     */
    int updateInterviewRecord(InterviewRecord interviewRecord);

    int updateInterviewRecordByFollowId(InterviewRecord interviewRecord);

    /**
     * 获取面试确认列表
     *
     * @param queryFollowUpModel
     * @return
     */
    List<InterviewRecordVO> getInterviewAuthList(QueryFollowUpModel queryFollowUpModel);


    /**
     * 获取面试确认数量
     *
     * @param queryFollowUpModel
     * @return
     */
    int getInterviewAuthCount(QueryFollowUpModel queryFollowUpModel);

    /**
     * 更新面试审核状态
     *
     * @param interviewRecord
     * @return
     */
    int updateInterviewAuth(InterviewRecord interviewRecord);

    /**
     * 获取待面试数量
     *
     * @param projectId
     * @return
     */
    int getWaitInterviewCount(@Param("projectId") Integer projectId);

    /**
     * 获取面试记录
     *
     * @param enterpriseId
     * @param followId
     * @param createBy
     * @return
     */
    List<InterviewRecord> getInterviewRecordByCreateBy(@Param("enterpriseId") Integer enterpriseId, @Param("followId") Integer followId, @Param("createBy") Integer createBy);

    /**
     * 离职 更新面试记录 对接人
     *
     * @param quitRecordModel
     * @return
     */
    int quitUpdateInterviewRecordCreateBy(QuitRecordModel quitRecordModel);

    /**
     * 获取面试每日上线数
     *
     * @param projectId     项目id
     * @param days          天数
     * @param beginTime     开始时间
     * @param endTime       结束时间
     * @return
     */
    List<String> getInterviewDailyLimit(@Param("projectId") Integer projectId, @Param("days") Integer days, @Param("beginTime") String beginTime, @Param("endTime") String endTime);

    /**
     * 获取面试数量
     * 时间范围内
     * @param projectId     项目id
     * @param beginTime     开始时间
     * @param endTime       结束时间
     * @return
     */
    int getInterviewCountByProjectId(@Param("projectId") Integer projectId, @Param("beginTime") String beginTime, @Param("endTime") String endTime);

    /**
     * 获取面试记录
     * 通过跟进id
     * @param followUpId    跟进id
     * @return
     */
    InterviewRecord getInterviewRecordByFollowUpId(@Param("followUpId") Integer followUpId);

// 个人数据 跟进展示 开始
    /**
     * 个人数据 邀约量
     *
     * @param queryFollowUpModel
     * @return
     */
    List<InterviewRecordVO> getIndividualInviteData(QueryFollowUpModel queryFollowUpModel);

    /**
     * 个人数据 邀约量 数量
     *
     * @param queryFollowUpModel
     * @return
     */
    int getIndividualInviteDataCount(QueryFollowUpModel queryFollowUpModel);

    /**
     * 个人数据 面试量
     *
     * @param queryFollowUpModel
     * @return
     */
    List<InterviewRecordVO> getIndividualInterviewData(QueryFollowUpModel queryFollowUpModel);

    /**
     * 个人数据 面试量 数量
     *
     * @param queryFollowUpModel
     * @return
     */
    int getIndividualInterviewDataCount(QueryFollowUpModel queryFollowUpModel);

    /**
     * 个人数据 offer量
     *
     * @param queryFollowUpModel
     * @return
     */
    List<InterviewRecordVO> getIndividualOfferData(QueryFollowUpModel queryFollowUpModel);

    /**
     * 个人数据 offer量 数量
     *
     * @param queryFollowUpModel
     * @return
     */
    int getIndividualOfferDataCount(QueryFollowUpModel queryFollowUpModel);

// 个人数据 跟进展示 结束


    /**
     * 数据校对-面试列表
     * @param model
     * @return
     */
    List<FollowRevokeVO> getRevokeInterviewList(QueryFollowRevokeModel model);

    /**
     * 数据校对-面试列表数量
     * @param model
     * @return
     */
    int getRevokeInterviewListCount(QueryFollowRevokeModel model);

    /**
     * 撤销面试记录
     * @param interviewRecord
     * @return
     */
    int updateRevokeInterviewRecord(InterviewRecord interviewRecord);

    /**
     * 获取企业项目面试确认列表
     *
     * @param queryFollowUpModel
     * @return
     */
    List<InterviewRecordVO> getEnterpriseProjectInterviewAuthList(QueryFollowUpModel queryFollowUpModel);

    /**
     * 获取企业项目面试确认列表数量
     *
     * @param queryFollowUpModel
     * @return
     */
    int getEnterpriseProjectInterviewAuthListCount(QueryFollowUpModel queryFollowUpModel);

    /**
     * 邀约量
     * @param dto
     * @return
     */
    List<StatisticalChartDataVO> getInvitationForInterviewData(RecruitmentDataDTO dto);

    /**
     * 转化率-邀约量
     * @param dto
     * @return
     */
    int getInvitationForInterviewCount(ConversionRateDataDTO dto);

    /**
     * 到面量
     * @param dto
     * @return
     */
    List<StatisticalChartDataVO> getInterviewData(RecruitmentDataDTO dto);

    /**
     * 转化率-到面量
     * @param dto
     * @return
     */
    int getInterviewCount(ConversionRateDataDTO dto);

    /**
     * 转化率-offer量
     * @param dto
     * @return
     */
    int getOfferCount(ConversionRateDataDTO dto);

    /**
     * 邀约量列表
     * @param dto
     * @return
     */
    List<RecruitmentDataListVO> getInvitationForInterviewDataList(RecruitmentDataListDTO dto);

    int getInvitationForInterviewDataCount(RecruitmentDataListDTO dto);

    /**
     * 到面量列表
     * @param dto
     * @return
     */
    List<RecruitmentDataListVO> getInterviewDataList(RecruitmentDataListDTO dto);

    int getInterviewDataCount(RecruitmentDataListDTO dto);

    /**
     * offer量列表
     * @param dto
     * @return
     */
    List<RecruitmentDataListVO> getOfferDataList(RecruitmentDataListDTO dto);

    int getOfferDataCount(RecruitmentDataListDTO dto);

    int getDataStatisticsInterviewCount(@Param("dto") BasicDataDTO dto, @Param("sourceType") Integer sourceType);

    int getEnterpriseDataStatisticsInterviewCount(@Param("dto") BasicDataDTO dto);
}
