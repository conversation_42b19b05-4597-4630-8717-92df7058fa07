package com.ssb.approve.dao;

import com.ssb.approve.entity.CheckSlip;
import com.ssb.approve.model.CheckSlipModel;
import com.ssb.approve.model.MarketWorkModel;
import com.ssb.approve.model.QueryFollowUpModel;
import com.ssb.approve.model.WorkWaiting;
import com.ssb.approve.model.vo.FollowUpVO;
import com.ssb.approve.model.vo.TeamFollowUpDataVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.util.List;


/**
 * @ClassName: DataStatisticDAO
 * @Description: 数据统计 dao
 * @Author: YZK
 * @Date: 2020年9月2日16:50:46
 **/
public interface DataStatisticDAO {

    /**
     * 获取团队跟进  数据
     *
     * @param queryFollowUpModel
     * @return
     */
    List<TeamFollowUpDataVO> getTeamFollowUpData(QueryFollowUpModel queryFollowUpModel);

    /**
     * 获取团队跟进  数据 数量
     *
     * @param queryFollowUpModel
     * @return
     */
    int getTeamFollowUpDataCount(QueryFollowUpModel queryFollowUpModel);

    /**
     * 获取团队跟进数据 总数
     *
     * @param queryFollowUpModel
     * @return
     */
    TeamFollowUpDataVO getTeamFollowUpDataTotal(QueryFollowUpModel queryFollowUpModel);

    /**
     * 获取个人团队数据 总数
     *
     * @param queryFollowUpModel
     * @return
     */
    TeamFollowUpDataVO getIndividualDataTotal(QueryFollowUpModel queryFollowUpModel);

    //在招项目数量
    @Select("SELECT COUNT(id) FROM crm_project WHERE SUSPEND = 0 AND STOP = 0 AND MASTER =#{uid}")
    int getResortProject(@Param("uid")Integer uid);

    /**
     * 待面试数量
     *
     * @param mk
     * @return
     */
    int getForInterviewCount(MarketWorkModel mk);

    /**
     * 待入职数量
     *
     * @param mk
     * @return
     */
    int getAwaitEntyCount(MarketWorkModel mk);

    /**
     * 待结算数量
     *
     * @param mk
     * @return
     */
    int getForTheCount(MarketWorkModel mk);
    /**
     * 待结点数量
     *
     * @param mk
     * @return
     */
    int getNodeCount(MarketWorkModel mk);

    /**
     * 待面试
     *
     * @param mk
     * @return
     */
    List<WorkWaiting> getForInterview(MarketWorkModel mk);

    /**
     * 待面试
     *
     * @param mk
     * @return
     */
    List<WorkWaiting> getAwaitEnty(MarketWorkModel mk);


    /**
     * 待结点
     *
     * @param mk
     * @return
     */
    List<WorkWaiting> getNode(MarketWorkModel mk);

       /**
     * 核对单
     *
     * @param cs
     * @return
     */
    List<CheckSlip> getCheckSlip(CheckSlipModel cs);
    /**
     * 核对单总数
     *
     * @param cs
     * @return
     */
    int getCheckSlipCount(CheckSlipModel cs);

    BigDecimal getSumCheckSlip(CheckSlipModel cs);

    /**
     * 核对单明细
     *
     * @param cs
     * @return
     */
    List<CheckSlip> getCheckSlipDetail(CheckSlipModel cs);
    /**
     * 核对单明细总数
     *
     * @param cs
     * @return
     */
    int getCheckSlipDetailCount(CheckSlipModel cs);

}

