package com.ssb.approve.dao;

import com.ssb.approve.model.dto.DailyDataDTO;
import com.ssb.approve.model.vo.UserEverydayWorkloadVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface UserEverydayWorkloadDAO {

    UserEverydayWorkloadVO getDailyData(@Param("userIdList") List<Integer> userIdList,@Param("yesterday") Integer yesterday);
}
