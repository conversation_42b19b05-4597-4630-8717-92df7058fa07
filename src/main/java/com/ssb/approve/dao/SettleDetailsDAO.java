package com.ssb.approve.dao;

import com.ssb.approve.entity.SettleDetails;
import com.ssb.approve.model.FollowUpModel;
import com.ssb.approve.model.QueryFollowRevokeModel;
import com.ssb.approve.model.QueryFollowUpModel;
import com.ssb.approve.model.QuitRecordModel;
import com.ssb.approve.model.dto.PerformanceDTO;
import com.ssb.approve.model.vo.*;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.List;

/**
 * @ClassName: SettleDetailsDAO
 * @Description: 结算明细  dao
 * @Author: YZK
 * @Date: 2019年12月27日11:45:51
 **/
public interface SettleDetailsDAO {

    /**
     * 保存结算明细
     *
     * @param settleDetails
     * @return
     */
    int save(SettleDetails settleDetails);

    /**
     * 批量保存结算明细
     *
     * @param settleDetails
     * @return
     */
    int saveBatch(@Param("settleDetails") List<SettleDetails> settleDetails);

    /**
     * 获取结算确认列表
     *
     * @param queryFollowUpModel
     * @return
     */
    List<SettleDetailsVO> getSettleAuthList(QueryFollowUpModel queryFollowUpModel);

    /**
     * 获取结算确认 数量
     *
     * @param queryFollowUpModel
     * @return
     */
    int getSettleAuthCount(QueryFollowUpModel queryFollowUpModel);

    /**
     * 获取结算明细
     *
     * @param id  id
     * @return
     */
    SettleDetails getSettleDetailsById(@Param("id") Integer id);

    /**
     * 更新明细状态
     *
     * @param settleDetails
     * @return
     */
    int updateSettleDetailsStatus(SettleDetails settleDetails);

    int updateSettleDetailsByFollowId(SettleDetails settleDetails);

    /**
     * 获取未结算数量
     *
     * @param followId 跟进id
     * @return
     */
    int unSettleCount(@Param("followId") Integer followId);

    /**
     * 获取结算数量
     *
     * @param followId 跟进id
     * @param status  通过状态 0 默认 1 结算  2 离职
     * @return
     */
    int getSettleCountByFollowIdStatus(@Param("followId") Integer followId, @Param("status") Integer status);

    /**
     * 获取结算明细
     * 通过跟进id
     * @param followId 跟进id
     * @return
     */
    List<SettleDetails> getSettleDetailsByFollowId(@Param("followId") Integer followId);

    /**
     * 获取待结算数量
     *
     * @param projectId
     * @return
     */
    int getWaitSettleCount(@Param("projectId") Integer projectId);

    /**
     * 获取结算明细
     * 通过跟进人
     * @param enterpriseId  企业id
     * @param followId      跟进id
     * @param createBy      跟进人
     * @return
     */
    List<SettleDetails> getSettleDetailsByCreateBy(@Param("enterpriseId") Integer enterpriseId, @Param("followId") Integer followId, @Param("createBy") Integer createBy);

    /**
     * 离职更新 结算明细 对接人
     *
     * @param quitRecordModel
     * @return
     */
    int quitUpdateSettleDetailsCreateBy(QuitRecordModel quitRecordModel);

    /**
     * 更新结算详情状态
     * @param settleDetails
     * @return
     */
    int updateSettleDetailsTmpStatus(SettleDetails settleDetails);

    /**
     * 个人数据 结算量
     *
     * @param queryFollowUpModel
     * @return
     */
    List<SettleDetailsVO> getIndividualSettleData(QueryFollowUpModel queryFollowUpModel);

    /**
     * 个人数据 结算量  数量
     *
     * @param queryFollowUpModel
     * @return
     */
    int getIndividualSettleDataCount(QueryFollowUpModel queryFollowUpModel);

    /**
     * 当前节点之前是否都已结算
     * @param followId
     * @param node
     * @return
     */
    int countSettledByFollowId(@Param("followId") Integer followId, @Param("node") Integer node);

    List<SettleDetails> getSettleDetailsListById(@Param("followId") Integer followId, @Param("node") Integer node);

    /**
     * 节点前上一个实际结算时间
     * @param followId
     * @param node
     * @return
     */
    Timestamp getPrevActualSettleTime(@Param("followId") Integer followId, @Param("node") Integer node);

    /**
     * 当前结算节点后的结算节点
     * @param followId
     * @param node
     * @return
     */
    List<SettleDetails> getTheRestDetailsList(@Param("followId") Integer followId, @Param("node") Integer node);

    /**
     * 更新预计提成时间
     * @param id
     * @param settleTime
     * @param userId
     * @return
     */
    int updateSettleTime(@Param("id")Integer id, @Param("settleTime")Timestamp settleTime, @Param("userId")Integer userId);


    /**
     * 数据校对-入职列表
     * @param model
     * @return
     */
    List<FollowRevokeVO> getRevokeSettleList(QueryFollowRevokeModel model);

    /**
     * 数据校对-入职列表数量
     * @param model
     * @return
     */
    int getRevokeSettleListCount(QueryFollowRevokeModel model);

    /**
     * 是否存在结算记录
     * @param followId
     * @return
     */
    int existSettleByFollowId(@Param("followId")Integer followId);

    /**
     * 删除结算记录
     * @param followId
     * @return
     */
    int deleteSettleDetailsByFollowId(@Param("userId")Integer userId, @Param("updateTime")Timestamp updateTime,@Param("followId")Integer followId);

    /**
     * 状态为已结算时 查询是否有晚于当前节点的已结算节点
     * 状态为离职时 查询是否有早于当前节点的离职节点
     * @param status
     * @param followId
     * @param node
     * @return
     */
    int countOtherSettles(@Param("status") Integer status, @Param("followId") Integer followId, @Param("node")Integer node);

    /**
     * 撤销结算
     * @param updateBy
     * @param updateTime
     * @return
     */
    int updateSettleDetailsRevoke(@Param("updateBy")Integer updateBy, @Param("updateTime")Timestamp updateTime, @Param("id")Integer id);

    /**
     * 未结算数量
     * @param followId
     * @return
     */
    int countUnProcessedSettle(@Param("followId") Integer followId);

    /**
     * 企业端 获取结算确认列表
     *
     * @param queryFollowUpModel
     * @return
     */
    List<SettleDetailsVO> getEnterpriseProjectSettleAuthList(QueryFollowUpModel queryFollowUpModel);

    /**
     * 企业端 获取结算确认 数量
     *
     * @param queryFollowUpModel
     * @return
     */
    int getEnterpriseProjectSettleAuthCount(QueryFollowUpModel queryFollowUpModel);

    @Select("select status from crm_settle_details where follow_id = #{followId} and del_flag = 0 order by id desc limit 1")
    Integer getSettleStatusByFollowId(@Param("followId") Integer followId);

}
