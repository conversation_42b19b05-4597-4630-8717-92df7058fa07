package com.ssb.approve.dao;

import com.ssb.approve.entity.FollowUp;
import com.ssb.approve.model.QueryAppResumeFollowUpList;
import com.ssb.approve.model.QueryCommissionStatisticModel;
import com.ssb.approve.model.QueryFollowUpModel;
import com.ssb.approve.model.QuitRecordModel;
import com.ssb.approve.model.dto.*;
import com.ssb.approve.model.vo.*;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;

/**
 * @ClassName: FollowUpDAO
 * @Description: 项目跟进审核 dao
 * @Author: YZK
 * @Date: 2019年12月20日10:33:01
 **/
public interface FollowUpDAO {

    /**
     * 获取跟进信息
     *
     * @param id    跟进记录id
     * @return
     */
    FollowUp getFollowUpById(@Param("id") Integer id);

    /**
     * 检测跟进
     *
     * @param contactId     求职用户id
     * @param projectId     项目id
     * @param finish        完成状态  0 进行中  1 已完成
     * @return
     */
    int checkFollowUp(@Param("contactId") Integer contactId, @Param("projectId") Integer projectId, @Param("finish") Integer finish);

    /**
     * 获取检测的跟进详情
     * @param contactId
     * @param projectId
     * @param finish
     * @return
     */
    List<FollowUpVO> getCheckFollowUp(@Param("contactId") Integer contactId, @Param("projectId") Integer projectId, @Param("finish") Integer finish);

    /**
     * 获取检测的跟进详情
     * @param phone
     * @param projectId
     * @param finish
     * @return
     */
    List<FollowUpVO> getCheckFollowUpByPhone(@Param("contactId") Integer contactId, @Param("projectId") Integer projectId, @Param("finish") Integer finish);

    /**
     * 保存项目跟进
     *
     * @param followUp
     * @return
     */
    int save(FollowUp followUp);

    /**
     * 获取跟进列表信息
     *
     * @param queryFollowUpModel
     * @return
     */
    List<FollowUpVO> getFollowUpList(QueryFollowUpModel queryFollowUpModel);

    /**
     * 获取列表总数量
     *
     * @param queryFollowUpModel
     * @return
     */
    int getFollowUpCount(QueryFollowUpModel queryFollowUpModel);

    /**
     * 更新跟进状态
     *
     * @param followUp
     * @return
     */
    int updateFollowUp(FollowUp followUp);

    /**
     * 获取已约待面试列表
     *
     * @return
     */
    List<FollowUpVO> getInterviewList(QueryFollowUpModel queryFollowUpModel);

    /**
     * 获取已约待面试 数量
     *
     * @param queryFollowUpModel
     * @return
     */
    int getInterviewCount(QueryFollowUpModel queryFollowUpModel);

    /**
     * 获取发offer 列表
     *
     * @param queryFollowUpModel
     * @return
     */
    List<FollowUpVO> getOnlyOfferList(QueryFollowUpModel queryFollowUpModel);

    /**
     * 获取发offer列表 数量
     *
     * @param queryFollowUpModel
     * @return
     */
    int getOnlyOfferCount(QueryFollowUpModel queryFollowUpModel);

    /**
     * 获取通过待入职列表
     *
     * @param queryFollowUpModel
     * @return
     */
    List<FollowUpVO> getEntryRecordList(QueryFollowUpModel queryFollowUpModel);

    /**
     * 获取通过待入职 数量
     *
     * @param queryFollowUpModel
     * @return
     */
    int getEntryRecordCount(QueryFollowUpModel queryFollowUpModel);

    /**
     * 获取入职待结算列表
     *
     * @param queryFollowUpModel
     * @return
     */
    List<FollowUpVO> getSettleDetailsList(QueryFollowUpModel queryFollowUpModel);

    /**
     * 获取入职待结算 数量
     *
     * @param queryFollowUpModel
     * @return
     */
    int getSettleDetailsCount(QueryFollowUpModel queryFollowUpModel);

    /**
     * 获取历史跟进列表
     *
     * @param queryFollowUpModel
     * @return
     */
    List<FollowUpVO> getHistoryList(QueryFollowUpModel queryFollowUpModel);

    /**
     * 获取历史跟数量
     * @param queryFollowUpModel
     * @return
     */
    int getHistoryCount(QueryFollowUpModel queryFollowUpModel);

    /**
     * 获取进行中跟进数量
     * 通过跟进人
     *
     * @param queryFollowUpModel
     * @return
     */
    int getFollowUpCountByCreateByAndFinish(QueryFollowUpModel queryFollowUpModel);


    /**
     * 获取未完成跟进 or 完成，已完结 30天以内的数据
     *
     * @param fromUserId
     * @param days
     * @return
     */
    List<FollowUpVO> getFollowUpNoFinishByCreateBy(@Param("fromUserId") Integer fromUserId, @Param("days") Integer days);

    /**
     * 离职更新 跟进记录  对接人
     *
     * @param quitRecordModel
     * @return
     */
    int quitUpdateFollowUpCreateBy(QuitRecordModel quitRecordModel);

    /**
     * 获取项目跟进详情
     *
     * @return
     */
    List<FollowUpVO> getProjectFollowUpDetails(QueryFollowUpModel queryFollowUpModel);

    /**
     * 获取项目跟进详情  数量
     * @param queryFollowUpModel
     * @return
     */
    int getProjectFollowUpDetailsCount(QueryFollowUpModel queryFollowUpModel);

    int getSettleCount(@Param("projectId") Integer projectId);

    /**
     * app简历跟进
     * @param queryAppResumeFollowUpList
     * @return
     */
    List<AppResumeFollowUpListVO> getAppResumeFollowUpList(QueryAppResumeFollowUpList queryAppResumeFollowUpList);

    /**
     * app跟进简历数量
     * @param queryAppResumeFollowUpList
     * @return
     */
    int getAppResumeFollowUpListCount(QueryAppResumeFollowUpList queryAppResumeFollowUpList);

    /**
     * 跟进负责人
     * @param resumeId
     * @return
     */
    String getFollowUpPersonInCharge(@Param("resumeId") Integer resumeId);

    int getContactIdByFollowId(@Param("followId") Integer followId);

    /**
     * 转化比例数量
     * @param queryAppResumeFollowUpList
     * @return
     */
    Integer countConversionRatio(QueryAppResumeFollowUpList queryAppResumeFollowUpList);

    /**
     * 跟进效率数量
     * @param queryAppResumeFollowUpList
     * @return
     */
    Integer countFollowUpEfficiency(QueryAppResumeFollowUpList queryAppResumeFollowUpList);

    /**
     * 交付状态数量
     * @return
     */
    List<DeliveryStatusVO> countDeliveryStatus(QueryAppResumeFollowUpList queryAppResumeFollowUpList);

    int updateRevokeFollowUp(FollowUp followUp);

    /**
     * 查询跟进状态
     * @param followId
     * @return
     */
    Integer getCurrentStatusById(@Param("followId") Integer followId);

    /**
     * 是否有其他人正在跟进
     * @param userId
     * @return
     */
    FollowUpVO getOnGoingFollowUp(@Param("followId")Integer followId);

    Integer getProjectIdById(@Param("followId")Integer followId);

    Integer getFollowUpUserCommissionInfoById(@Param("followId")Integer followId);

    List<CooperationProjectFollowUpInfoVO> getCooperationProjectFollowUpList(QueryFollowUpModel queryFollowUpModel);

    int getCooperationProjectFollowUpCount(QueryFollowUpModel queryFollowUpModel);

    @Select("select id, enterprise_id enterpriseId from crm_follow_up where project_id = #{projectId} and finish = 0 and del_flag = 0")
    List<FollowUpVO> getFollowUpIdByProjectId(@Param("projectId") Integer projectId);

    /**
     * 结算量
     * @param dto
     * @return
     */
    List<StatisticalChartDataVO> getSettleData(RecruitmentDataDTO dto);

    /**
     * 结算量列表
     * @param dto
     * @return
     */
    List<RecruitmentDataListVO> getSettleDataList(RecruitmentDataListDTO dto);

    int getSettleDataCount(RecruitmentDataListDTO dto);

    int getSettleCountByInterviewTime(ConversionRateDataDTO dto);

    int getShareProjectFollowUpCount(@Param("type") Integer type, @Param("projectId")Integer projectId);

    int getDataStatisticsInviteCount(@Param("dto") BasicDataDTO dto, @Param("sourceType") Integer sourceType);

    /**
     * 计算待约面数量
     */
    @Select("SELECT COUNT(*) FROM crm_follow_up WHERE current_status != -1 AND del_flag = 0 AND project_id IN (SELECT id FROM crm_project WHERE id = #{projectId} OR share_project_id = #{projectId})")
    int countWaitingForAgreedInterview(@Param("projectId") int projectId);

    /**
     * 计算共享项目待约面数量
     */
    @Select("SELECT COUNT(*) FROM crm_follow_up cfu INNER JOIN crm_project sub_cp ON sub_cp.id = cfu.project_id WHERE current_status != -1 AND cfu.del_flag = 0 AND sub_cp.share_project_id IN (SELECT share_project_id FROM crm_project WHERE id = #{projectId})")
    int countWaitingForAgreedInterviewForShared(@Param("projectId") int projectId);

    @Select("SELECT COUNT(*) FROM crm_follow_up cfu INNER JOIN crm_project sub_cp ON sub_cp.id = cfu.project_id WHERE current_status != -1 AND cfu.del_flag = 0 AND sub_cp.share_project_id = #{shareProjectId}")
    int countWaitingForAgreedInterviewForSharedByShareProjectId(@Param("shareProjectId") int shareProjectId);

    /**
     * 计算待面试数量
     */
    @Select("SELECT COUNT(*) FROM crm_follow_up cfu INNER JOIN crm_interview_record cir ON cfu.id = cir.follow_id WHERE cfu.del_flag = 0 AND cir.status IN (3, 5) AND cfu.project_id IN (SELECT id FROM crm_project WHERE id = #{projectId} OR share_project_id = #{projectId})")
    int countWaitingForInterview(@Param("projectId") int projectId);

    /**
     * 计算共享项目待面试数量
     */
    @Select("SELECT COUNT(*) FROM crm_follow_up cfu INNER JOIN crm_interview_record cir ON cfu.id = cir.follow_id INNER JOIN crm_project sub_cp ON sub_cp.id = cfu.project_id WHERE cfu.del_flag = 0 AND cir.STATUS IN (3, 5) AND sub_cp.share_project_id IN (SELECT share_project_id FROM crm_project WHERE id = #{projectId})")
    int countWaitingForInterviewForShared(@Param("projectId") int projectId);

    @Select("SELECT COUNT(*) FROM crm_follow_up cfu INNER JOIN crm_interview_record cir ON cfu.id = cir.follow_id INNER JOIN crm_project sub_cp ON sub_cp.id = cfu.project_id WHERE cfu.del_flag = 0 AND cir.STATUS IN (3, 5) AND sub_cp.share_project_id = #{shareProjectId}")
    int countWaitingForInterviewForSharedByShareProjectId(@Param("shareProjectId") int shareProjectId);

    /**
     * 计算入职确认数量
     */
    @Select("SELECT COUNT(*) FROM crm_follow_up cfu INNER JOIN crm_entry_record cer ON cfu.id = cer.follow_id WHERE cfu.del_flag = 0 AND cer.STATUS = 1 AND cfu.project_id IN (SELECT id FROM crm_project WHERE id = #{projectId} OR share_project_id = #{projectId})")
    int countWaitingForEntry(@Param("projectId") int projectId);

    /**
     * 计算共享项目入职确认数量
     */
    @Select("SELECT COUNT(*) FROM crm_follow_up cfu INNER JOIN crm_entry_record cer ON cfu.id = cer.follow_id INNER JOIN crm_project sub_cp ON sub_cp.id = cfu.project_id WHERE cfu.del_flag = 0 AND cer.STATUS = 1 AND sub_cp.share_project_id IN (SELECT share_project_id FROM crm_project WHERE id = #{projectId})")
    int countWaitingForEntryForShared(@Param("projectId") int projectId);

    @Select("SELECT COUNT(*) FROM crm_follow_up cfu INNER JOIN crm_entry_record cer ON cfu.id = cer.follow_id INNER JOIN crm_project sub_cp ON sub_cp.id = cfu.project_id WHERE cfu.del_flag = 0 AND cer.STATUS = 1 AND sub_cp.share_project_id = #{shareProjectId}")
    int countWaitingForEntryForSharedByShareProjectId(@Param("shareProjectId") int shareProjectId);

    /**
     * 计算结算数量
     */
    @Select("SELECT count(cfu.id) FROM crm_follow_up cfu WHERE EXISTS (SELECT id FROM crm_settle_details WHERE follow_id = cfu.id AND `status` = 1 AND del_flag = 0) AND cfu.del_flag = 0 AND cfu.project_id IN (SELECT id FROM crm_project WHERE id = #{projectId} OR share_project_id = #{projectId})")
    int countSettlement(@Param("projectId") int projectId);

    /**
     * 计算共享项目结算数量
     */
    @Select("SELECT count(cfu.id) FROM crm_follow_up cfu INNER JOIN crm_project sub_cp ON sub_cp.id = cfu.project_id WHERE EXISTS (SELECT id FROM crm_settle_details WHERE follow_id = cfu.id AND `status` = 1 AND del_flag = 0) AND cfu.del_flag = 0 AND sub_cp.share_project_id IN (SELECT share_project_id FROM crm_project WHERE id = #{projectId})")
    int countSettlementForShared(@Param("projectId") int projectId);

    @Select("SELECT count(cfu.id) FROM crm_follow_up cfu INNER JOIN crm_project sub_cp ON sub_cp.id = cfu.project_id WHERE EXISTS (SELECT id FROM crm_settle_details WHERE follow_id = cfu.id AND `status` = 1 AND del_flag = 0) AND cfu.del_flag = 0 AND sub_cp.share_project_id = #{shareProjectId}")
    int countSettlementForSharedByShareProjectId(@Param("shareProjectId") int shareProjectId);

    @Select("SELECT COUNT(*) FROM crm_follow_up WHERE current_status != -1 AND del_flag = 0 AND project_id = #{projectId} and create_by = #{userId}")
    int getInvitationCountByUserIdAndProjectId(@Param("projectId") int projectId, @Param("userId")Integer userId);

    @Select("SELECT COUNT(*) FROM crm_follow_up cfu INNER JOIN crm_interview_record cir ON cfu.id = cir.follow_id WHERE cfu.del_flag = 0 AND cir.status IN (3, 5) AND cfu.project_id = #{projectId} and cfu.create_by = #{userId}")
    int getInterviewCountByUserIdAndProjectId(@Param("projectId") int projectId, @Param("userId")Integer userId);

    @Select("SELECT COUNT(*) FROM crm_follow_up cfu INNER JOIN crm_entry_record cer ON cfu.id = cer.follow_id WHERE cfu.del_flag = 0 AND cer.STATUS = 1 AND cfu.project_id = #{projectId} and cfu.create_by = #{userId}")
    int getEntryCountByUserIdAndProjectId(@Param("projectId") int projectId, @Param("userId")Integer userId);

    @Select("SELECT count(cfu.id) FROM crm_follow_up cfu WHERE EXISTS (SELECT id FROM crm_settle_details WHERE follow_id = cfu.id AND `status` = 1 AND del_flag = 0) AND cfu.del_flag = 0 AND cfu.project_id = #{projectId} and cfu.create_by = #{userId}")
    int getSettlementCountByUserIdAndProjectId(@Param("projectId") int projectId, @Param("userId")Integer userId);
    //企业端邀约数量
    int getEnterpriseDataStatisticsInviteCount(@Param("dto") BasicDataDTO dto);
    //企业端结算
    int getEnterpriseSettleCount(@Param("dto") BasicDataDTO dto);

    /**
     * 邀约
     */
    List<Integer> listWaitingForInterviewWithTimeRange(@Param("projectIds") List<Integer> projectIds,
                                                       @Param("startTime") Date startTime,
                                                       @Param("endTime") Date endTime);

    List<Integer> listWaitingForAgreedInterviewWithTimeRange(@Param("projectIds") List<Integer> projectIds,
                                                             @Param("startTime") Date startTime,
                                                             @Param("endTime") Date endTime);

    List<Integer> listWaitingForEntryWithTimeRange(@Param("projectIds") List<Integer> projectIds,
                                                   @Param("startTime") Date startTime,
                                                   @Param("endTime") Date endTime);

    int getSettleCountByUserIdAndTimeRange(@Param("userId")Integer userId, @Param("startTime")Date startTime, @Param("endTime")Date endTime);

    List<Integer> listSettlementWithTimeRange(@Param("projectIds") List<Integer> projectIds,
                                              @Param("startTime") Date startTime,
                                              @Param("endTime") Date endTime);
}
