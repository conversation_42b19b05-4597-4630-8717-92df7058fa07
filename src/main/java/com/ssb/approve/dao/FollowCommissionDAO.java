package com.ssb.approve.dao;

import com.ssb.approve.entity.FollowCommission;
import org.apache.ibatis.annotations.Param;

/**
 * 跟进提成dao
 *
 * <AUTHOR>
 * @time 2021年1月4日11:16:12
 */
public interface FollowCommissionDAO {

    /**
     * 保存跟进提成
     *
     * @param followCommission
     * @return
     */
    int save(FollowCommission followCommission);

    /**
     * 获取跟进提成
     *
     * @param followId
     * @return
     */
    FollowCommission getFollowCommission(@Param("followId") Integer followId);

    /**
     * 项目跟进完成数
     * @param projectId
     * @return
     */
    int getProjectCompleteCount(@Param("projectId")Integer projectId);
}
