package com.ssb.approve.dao;

import com.ssb.approve.entity.EntryRecord;
import com.ssb.approve.model.QueryFollowRevokeModel;
import com.ssb.approve.model.QueryFollowUpModel;
import com.ssb.approve.model.QuitRecordModel;
import com.ssb.approve.model.dto.BasicDataDTO;
import com.ssb.approve.model.dto.ConversionRateDataDTO;
import com.ssb.approve.model.dto.RecruitmentDataDTO;
import com.ssb.approve.model.dto.RecruitmentDataListDTO;
import com.ssb.approve.model.vo.EntryRecordVO;
import com.ssb.approve.model.vo.FollowRevokeVO;
import com.ssb.approve.model.vo.RecruitmentDataListVO;
import com.ssb.approve.model.vo.StatisticalChartDataVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @ClassName: EntryRecordDAO
 * @Description: 入职记录 dao
 * @Author: YZK
 * @Date: 2019年12月25日16:30:12
 **/
public interface EntryRecordDAO {

    /**
     * 保存入职记录
     *
     * @param entryRecord
     * @return
     */
    int save(EntryRecord entryRecord);

    /**
     * 获取入职记录
     * 通过id 入职id
     *
     * @return
     */
    EntryRecord getEntryRecordById(@Param("id") Integer id);

    /**
     * 更新入职记录
     *
     * @param entryRecord
     * @return
     */
    int updateEntryRecord(EntryRecord entryRecord);

    int updateEntryRecordByFollowId(EntryRecord entryRecord);

    /**
     * 获取入职确认列表
     *
     * @param queryFollowUpModel
     * @return
     */
    List<EntryRecordVO> getEntryAuthList(QueryFollowUpModel queryFollowUpModel);

    /**
     * 获取入职确认数量
     *
     * @param queryFollowUpModel
     * @return
     */
    int getEntryAuthCount(QueryFollowUpModel queryFollowUpModel);

    /**
     * 更新入职审核状态
     *
     * @param entryRecord
     * @return
     */
    int updateEntryAuth(EntryRecord entryRecord);

    /**
     * 待入职数量
     *
     * @param projectId
     * @return
     */
    int getWaitEntryCount(@Param("projectId") Integer projectId);

    /**
     * 获取入职记录
     * 通过跟进人
     *
     * @param enterpriseId 公司id
     * @param followId     跟进id
     * @param createBy     跟进人
     * @return
     */
    List<EntryRecord> getEntryRecordByCreateBy(@Param("enterpriseId") Integer enterpriseId, @Param("followId") Integer followId, @Param("createBy") Integer createBy);

    /**
     * 离职更新 入职记录  对接人
     *
     * @param quitRecordModel
     * @return
     */
    int quitUpdateEntryRecordCreateBy(QuitRecordModel quitRecordModel);

    /**
     * 获取入职记录
     * 通过跟进id
     * @param followUpId  跟进项目id
     * @return
     */
    EntryRecord getEntryRecordByFollowUpId(@Param("followUpId") Integer followUpId);

    /**
     * 个人数据 入职量
     *
     * @param queryFollowUpModel
     * @return
     */
    List<EntryRecordVO> getIndividualEntryData(QueryFollowUpModel queryFollowUpModel);

    /**
     * 个人数据 入职量 数量
     *
     * @param queryFollowUpModel
     * @return
     */
    int getIndividualEntryDataCount(QueryFollowUpModel queryFollowUpModel);

    /**
     * 数据校对-入职列表
     * @param model
     * @return
     */
    List<FollowRevokeVO> getRevokeEntryList(QueryFollowRevokeModel model);

    /**
     * 数据校对-入职列表数量
     * @param model
     * @return
     */
    int getRevokeEntryListCount(QueryFollowRevokeModel model);

    /**
     * 是否存在入职记录
     * @param followId
     * @return
     */
    int existEntryRecordByFollowId(@Param("followId") Integer followId);

    /**
     * 删除入职记录
     * @param followId
     * @return
     */
    int deleteEntryRecordByFollowId(@Param("followId") Integer followId);

    /**
     * 撤销入职
     * @param entryRecord
     * @return
     */
    int updateRevokeEntryRecord(EntryRecord entryRecord);

    /**
     * 企业端 获取入职确认列表
     *
     * @param queryFollowUpModel
     * @return
     */
    List<EntryRecordVO> getEnterpriseProjectEntryAuthList(QueryFollowUpModel queryFollowUpModel);

    /**
     * 企业端 获取入职确认数量
     *
     * @param queryFollowUpModel
     * @return
     */
    int getEnterpriseProjectEntryAuthCount(QueryFollowUpModel queryFollowUpModel);

    /**
     * 入职量
     * @param dto
     * @return
     */
    List<StatisticalChartDataVO> getEntryData(RecruitmentDataDTO dto);

    /**
     * 转化率-入职量
     * @param dto
     * @return
     */
    int getEntryCount(ConversionRateDataDTO dto);

    /**
     * 入职量列表
     * @param dto
     * @return
     */
    List<RecruitmentDataListVO> getEntryDataList(RecruitmentDataListDTO dto);

    int getEntryDataCount(RecruitmentDataListDTO dto);

    int getDataStatisticsEntryCount(@Param("dto") BasicDataDTO dto, @Param("sourceType") Integer sourceType);

    int getEnterpriseDataStatisticsEntryCount(@Param("dto") BasicDataDTO dto);
}
