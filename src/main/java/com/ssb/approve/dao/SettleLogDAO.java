package com.ssb.approve.dao;

import com.ssb.approve.entity.SettleLog;
import com.ssb.approve.model.QuitRecordModel;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @ClassName: SettleLogDAO
 * @Description: 结算日志 dao
 * @Author: YZK
 * @Date: 2019年12月27日20:35:24
 **/
public interface SettleLogDAO {

    /**
     * 保存结算日志
     *
     * @param settleLog
     * @return
     */
    int save(SettleLog settleLog);

    /**
     * 获取结算日志
     *
     * @param id
     * @return
     */
    SettleLog getSettleLogById(@Param("id") Integer id);

    /**
     * 获取总金额
     *
     * @param followId  跟进id
     * @return
     */
    int getAllPerformanceByFollowId(@Param("followId") Integer followId);

    /**
     * 获取结算记录
     * 通过跟进人
     * @param enterpriseId  企业id
     * @param followId      跟进id
     * @param createBy      跟进人id
     * @return
     */
    List<SettleLog> getSettleLogByCreateBy(Integer enterpriseId, Integer followId, Integer createBy);

    /**
     * 离职更新  结算记录 对接人
     *
     * @param quitRecordModel
     * @return
     */
    int quitUpdateSettleLogCreateBy(QuitRecordModel quitRecordModel);

    /**
     * 获取结算日志
     * 通过结算明细id
     * @param settleId
     * @return
     */
    SettleLog getSettleLogBySettleId(@Param("settleId") Integer settleId);

    /**
     * 删除
     * @param settleId
     * @return
     */
    int deleteSettleLog(@Param("settleId")Integer settleId);
}
