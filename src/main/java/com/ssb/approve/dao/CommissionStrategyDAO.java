package com.ssb.approve.dao;

import com.ssb.approve.entity.CommissionStrategy;
import com.ssb.approve.entity.ManageCommissionLevel;
import com.ssb.approve.model.CommissionStrategyModel;
import com.ssb.approve.model.QueryCommissionStrategyModel;
import com.ssb.approve.model.vo.CommissionStrategyVO;
import org.apache.ibatis.annotations.*;

import java.util.List;
import java.util.Map;

/**
 * @Author: Wang<PERSON>hiyong
 * @Date: 2020/12/22 9:21 AM
 * @Description:
 */
@Mapper
public interface CommissionStrategyDAO {

    /**
     * 策略列表
     * @param query
     * @return
     */
    List<CommissionStrategyVO> getStrategyList(QueryCommissionStrategyModel query);

    Integer getStrategyListCount(QueryCommissionStrategyModel query);


    /**
     * 新建提成策略
     * @param commissionStrategy
     * @return
     */
    Integer insertStrategy(CommissionStrategy commissionStrategy);

    /**
     * 更新提成策略
     * @param commissionStrategyModel
     * @return
     */
    Integer updateStrategy(CommissionStrategyModel commissionStrategyModel);

    /**
     * 策略标题是否存在
     * @param title
     * @return
     */
    @Select("select id from crm_commission_strategy where title = #{title} and del_flag = 0 and enterprise_id = #{eid}")
    Integer judgeStrategyTitle(@Param("title")String title, @Param("eid") Integer enterpriseId);

    @Select("select id,level,level_name levelName from crm_manage_commission_level where enterprise_id = #{eid} and level > 1")
    List<ManageCommissionLevel> getCommissionLevelList(@Param("eid") Integer enterpriseId);

    List<ManageCommissionLevel> getCommissionLevelListV2(@Param("eid") Integer enterpriseId);

    /**
     * 策略详情
     * @param id
     * @return
     */
    CommissionStrategyVO getStrategyInfo(Integer id);

    /**
     * 获取行业分类id
     * @return
     */
    @Select("select id from crm_project_classify")
    List<Integer> getProjectClassifyId();

    /**
     * 删除策略
     * @param id
     * @return
     */
    @Update("update crm_commission_strategy set del_flag = 1,update_by = #{userId}, update_time = now() where id = #{id}")
    Integer deleteStrategyById(@Param("id") Integer id, @Param("userId") Integer userId);

    /**
     * 猎头的各难度提成比例和合同开始时间
     * @param projectId
     * @return
     */
    Map<String, Object> getStrategyRatioAndContractStartTime(@Param("projectId")Integer projectId);

    List<Integer> getAllStrategyId();
}
