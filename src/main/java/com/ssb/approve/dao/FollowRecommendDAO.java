package com.ssb.approve.dao;

import com.ssb.approve.entity.CrmFollowRecommend;
import com.ssb.approve.model.dto.BasicDataDTO;
import com.ssb.approve.model.dto.FollowRecommendListDTO;
import com.ssb.approve.model.vo.FollowRecommendListVO;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface FollowRecommendDAO {

    int existsDataWithin90Days(@Param("contactId") Integer contactId, @Param("projectId") Integer projectId);

    int insertFollowRecommend(CrmFollowRecommend followRecommend);

    int updateFollowRecommend(CrmFollowRecommend followRecommend);

    List<FollowRecommendListVO> getFollowRecommendList(FollowRecommendListDTO dto);

    int getFollowRecommendListCount(FollowRecommendListDTO dto);

    int getApprovedThroughReviewCount(@Param("userId")Integer userId);

    // @MapKey("projectId")
    List<Map<String, Integer>> getProjectsRecommendCount(@Param("projectIds") List<Integer> projectIds,
                                                         @Param("customerEnterpriseId") Integer customerEnterpriseId,
                                                         @Param("startTime") Date startTime,
                                                         @Param("endTime") Date endTime);

    List<FollowRecommendListVO> getFollowRecommendListByEnterprise(FollowRecommendListDTO dto);

    int getFollowRecommendCountByEnterprise(FollowRecommendListDTO dto);

    int getFollowRecommendCountByEnterpriseId(@Param("dto") BasicDataDTO dto);

    List<CrmFollowRecommend> getFollowUpRecRemark(@Param("projectId") Integer projectId,
                                                  @Param("customerEnterpriseId") Integer customerEnterpriseId,
                                                  @Param("resumeId") Integer resumeId);

    CrmFollowRecommend getFollowRecommendById(@Param("id") Integer id);

    List<FollowRecommendListVO> getRecommendInfoListByEnterprise(FollowRecommendListDTO dto);

    List<FollowRecommendListVO> getRecommendListForProjectInfo(FollowRecommendListDTO dto);

    int getRecommendListForProjectInfoCount(FollowRecommendListDTO dto);

    @Select("SELECT count(*) FROM crm_follow_recommend WHERE create_by = #{userId} and recommend_time between #{startTime} and #{endTime}")
    int getFollowRecommendCountByUserIdAndTimeRange(@Param("userId")Integer userId, @Param("startTime")Date startTime, @Param("endTime")Date endTime);
}
