package com.ssb.approve.dao;

import com.ssb.approve.entity.PerformanceTarget;
import com.ssb.approve.model.dto.PerformanceTargetDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;

@Mapper
public interface PerformanceTargetDAO {
    /**
     * 保存业绩目标
     * @param performanceTarget 业绩目标
     * @return 影响行数
     */
    int save(PerformanceTarget performanceTarget);

    /**
     * 根据用户ID、年份、月份查询业绩目标
     * @param userId 用户ID
     * @param year 年份
     * @param month 月份
     * @return 业绩目标
     */
    PerformanceTarget getByUserIdAndYearAndMonth(@Param("userId") Integer userId, @Param("year") Integer year, @Param("month") Integer month);

    /**
     * 更新业绩目标
     * @param performanceTarget 业绩目标
     * @return 影响行数
     */
    int update(PerformanceTarget performanceTarget);

    BigDecimal getPerformanceTarget(PerformanceTargetDTO dto);
} 