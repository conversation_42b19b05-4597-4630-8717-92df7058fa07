package com.ssb.approve.dao;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

/**
 * @<PERSON><PERSON>
 */
@Repository
@Mapper
public interface ProjectDAO {

    @Select("select title from crm_project where id = #{projectId}")
    String getTitleByProjectId(@Param("projectId") Integer projectId);

}
