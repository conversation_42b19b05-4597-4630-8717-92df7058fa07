package com.ssb.approve.dao;

import com.ssb.approve.entity.FollowRevokeLog;
import com.ssb.approve.model.QueryFollowRevokeStatisticsModel;
import com.ssb.approve.model.vo.FollowRevokeStatisticsVO;

import java.util.List;

/**
 * @Author: WangShiyong
 * @Date: 2021/10/18 2:02 PM
 * @Description:跟进撤销
 */
public interface FollowRevokeLogDAO {

    /**
     * @param followRevokeLog
     * @return
     */
    int insertFollowRevokeLog(FollowRevokeLog followRevokeLog);

    /**
     * 撤销列表
     * @param model
     * @return
     */
    List<FollowRevokeStatisticsVO> getFollowRevokeStatisticsList(QueryFollowRevokeStatisticsModel model);

    /**
     * 撤销列表数量
     * @param model
     * @return
     */
    int getFollowRevokeStatisticsCount(QueryFollowRevokeStatisticsModel model);
}
