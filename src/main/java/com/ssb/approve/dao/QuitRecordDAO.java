package com.ssb.approve.dao;

import com.ssb.approve.entity.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @ClassName: QuitRecordDAO
 * @Description: 离职记录 dao   离职相关 dao 都在这里边
 * @Author: YZK
 * @Date: 2019年12月23日15:16:14
 **/
public interface QuitRecordDAO {

    /**
     * 获取离职记录
     *
     * @param fromUserId
     * @return
     */
    QuitRecord getQuitRecordByFromUserId(@Param("fromUserId") Integer fromUserId);

    /**
     * 保存离职 -- 跟进记录
     *
     * @param followUpQuitRecords
     * @return
     */
    int batchSaveFollowUpQuitRecord(@Param("followUpQuitRecords") List<FollowUpQuitRecord> followUpQuitRecords);

    /**
     * 保存离职 -- 面试记录
     *
     * @param interviewQuitRecords
     * @return
     */
    int batchSaveInterviewQuitRecord(@Param("interviewQuitRecords") List<InterviewQuitRecord> interviewQuitRecords);

    /**
     * 保存离职 -- 入职记录
     *
     * @param entryQuitRecords
     * @return
     */
    int batchSaveEntryQuitRecord(@Param("entryQuitRecords") List<EntryQuitRecord> entryQuitRecords);

    /**
     * 保存离职 -- 结算明细
     *
     * @param settleQuitDetails
     * @return
     */
    int batchSaveSettleQuitDetail(@Param("settleQuitDetails") List<SettleQuitDetails> settleQuitDetails);

    /**
     * 保存离职 -- 结算记录
     *
     * @param settleQuitLogs
     * @return
     */
    int batchSaveSettleQuitLog(@Param("settleQuitLogs") List<SettleQuitLog> settleQuitLogs);

    /**
     * 更新离职状态 完成态
     *
     * @param quitRecord
     * @return
     */
    int updateQuitRecord(QuitRecord quitRecord);
}


