package com.ssb.approve.dao;

import com.ssb.approve.entity.CommissionGaveLog;
import com.ssb.approve.model.QueryCommissionStatisticModel;
import com.ssb.approve.model.QueryFollowUpModel;
import com.ssb.approve.model.dto.BasicDataDTO;
import com.ssb.approve.model.dto.TheChartsDTO;
import com.ssb.approve.model.vo.CardVO;
import com.ssb.approve.model.vo.CommissionDateVO;
import com.ssb.approve.model.vo.CommissionStatisticVO;
import com.ssb.approve.model.vo.TheChartsVO;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Author: WangShiyong
 * @Date: 2020/12/31 11:54 AM
 * @Description:
 */
public interface CommissionGaveLogDAO {


    int save(CommissionGaveLog commissionGaveLog);

    /**
     * 保存提成发方记录
     *
     * @param commissionGaveLogs
     * @return
     */
    int saveBatch(@Param("commissionGaveLogs") List<CommissionGaveLog> commissionGaveLogs);

    /**
     * 月金额
     *
     * @param projectId         项目id
     * @param commissionUser    提成人
     * @param type              类型
     * @return
     */
    BigDecimal getMonthAmount(@Param("projectId") Integer projectId, @Param("commissionUser") Integer commissionUser, @Param("type") Integer type);

    /**
     * 删除提成
     * @param settleId
     * @return
     */
    int deleteCommissionGaveLogBySettleId(@Param("settleId")Integer settleId);

    BigDecimal getPerformanceByUserIdList(CardVO cardVO);

    BigDecimal getShareProjectPerformance(@Param("enterpriseId") Integer enterpriseId, @Param("userIdList") List<Integer> userIdList);

    List<TheChartsVO> getTheCharts(TheChartsDTO dto);

    int getTheChartsCount(TheChartsDTO dto);

    /**
     * 数据统计-业绩
     * @param dto
     * @param sourceType 查询数据类型 1/当前数据 2/对照数据
     * @return
     */
    BigDecimal getDataStatisticsPerformance(@Param("dto") BasicDataDTO dto, @Param("sourceType") Integer sourceType);
}
