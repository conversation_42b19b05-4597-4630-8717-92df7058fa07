package com.ssb.approve.dao;

import com.ssb.approve.entity.SalesCommissionCut;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author: WangShiyong
 * @Date: 2021/5/29 5:11 PM
 * @Description:销售削减比例
 */
public interface SalesCommissionCutDAO {

    /**
     * 新增削减比例
     * @param list
     * @return
     */
    int insertSalesCommissionCut(List<SalesCommissionCut> list);

    /**
     * 删除项目削减比例
     * @param id
     * @return
     */
    int deleteSalesCommissionCut(@Param("id") Integer id, @Param("userId") Integer userId);

    /**
     * 销售削减比例列表
     * @param id
     * @return
     */
    List<SalesCommissionCut> getSalesCommissionCutList(Integer id);

}
