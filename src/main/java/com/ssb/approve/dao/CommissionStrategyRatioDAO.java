package com.ssb.approve.dao;

import com.ssb.approve.entity.CommissionStrategyRatio;
import com.ssb.approve.model.CommissionStrategyRatioModel;
import com.ssb.approve.model.vo.CommissionStrategyRatioVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.List;
import java.util.Map;

/**
 * @Author: Wang<PERSON>hiyon<PERSON>
 * @Date: 2020/12/23 11:34 AM
 * @Description:
 */
@Mapper
public interface CommissionStrategyRatioDAO {


    int batchInsert(@Param("ratioList") List<CommissionStrategyRatioModel> ratioList, @Param("userId") Integer userId);

    @Update("update crm_commission_strategy_ratio set del_flag = 1,update_time = now(),update_by = #{userId} where strategy_id = #{strategyId}")
    int deleteStrategyRatio(@Param("strategyId") Integer strategyId, @Param("userId") Integer userId);

    List<CommissionStrategyRatioVO> getRatioList(@Param("strategyId") Integer strategyId);

    /**
     * 获取最新提成比例
     * @param strategyId
     * @return
     */
    List<CommissionStrategyRatio> getLastCommissionStrategyRatio(@Param("strategyId") Integer strategyId);
}
