package com.ssb.approve.dao;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @<PERSON><PERSON>
 */
@Repository
@Mapper
public interface ProjectParticipantDAO {

    @Select("select participant_id from crm_project_participant where project_id = #{projectId}")
    List<Integer> getProjectParticipant(@Param("projectId") Integer projectId);
}
