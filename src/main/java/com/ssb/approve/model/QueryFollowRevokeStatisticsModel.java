package com.ssb.approve.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.sql.Timestamp;

/**
 * @Author: WangShiyong
 * @Date: 2021/10/21 2:57 PM
 * @Description:已撤销
 */
@Data
public class QueryFollowRevokeStatisticsModel {

    @ApiModelProperty(value = "每页多少数据")
    private Integer pageSize = 10;

    @ApiModelProperty(value = "页码")
    private Integer pageNum;

    @ApiModelProperty(value = "起始数量")
    private Integer pageBegin;

    @ApiModelProperty(value = "当前用户id")
    private Integer userId;

    @ApiModelProperty(value = "企业id")
    private Integer enterpriseId;

    @ApiModelProperty(value = "撤销操作人")
    private String revokeUser;

    @ApiModelProperty(value = "查询类型 手机号/contactPhone  项目标题/title  求职者姓名/contactName")
    private String searchType;

    @ApiModelProperty(value = "查询参数")
    private String search;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Timestamp beginTime; //开始时间

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Timestamp endTime; //结束时间
}
