package com.ssb.approve.model;

import lombok.Data;

import java.sql.Timestamp;

/**
 * @Description: 销售工作台
 * @Author: zhangzeng
 * @date: 2021/7/2
 */
@Data
public class MarketWorkModel  {
    private Integer page;

    private Integer offset;

    private Integer pageSize = 5;

    private Timestamp beginTime;//开始时间

    private Timestamp endTime;//结束时间

    private Integer status;//1待面试2待入职3结算

    private Integer uid;//登录用户id

    private Integer eid;//登录用户所属公司

}
