package com.ssb.approve.model;

import lombok.Data;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020/12/22 9:11 AM
 * @Description:
 */
@Data
public class QueryCommissionStrategyModel {

    private Integer id; //数据id

    private Integer userId;//登录用户id

    private Integer enterpriseId;//登录用户所属公司

    private Integer pageSize = 10;//每页多少数据

    private Integer pageNum;//页码

    private Integer pageBegin;//起始数量

    private Integer cityLevel;//发展水平   0/超一线  1/新一线  2/二线

    private String title;//城市名称
}
