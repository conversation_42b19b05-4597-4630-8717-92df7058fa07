package com.ssb.approve.model.dto;

import lombok.Data;

import java.sql.Timestamp;
import java.util.List;

@Data
public class RecruitmentDataListDTO {

    /**
     * 1/个人 2/团队
     */
    private Integer type;

    /**
     * 部门id
     */
    private Integer deptId;

    /**
     * 用户id
     */
    private Integer userId;
    private List<Integer> userIdList;

    /**
     * 企业id
     */
    private Integer enterpriseId;

    /**
     *
     */
    private String nameOrPhone;

    /**
     * 求职者姓名
     */
    private String userName;

    /**
     * 求职者电话
     */
    private String phone;

    /**
     * 项目名称
     */
    private String title;

    /**
     * 企业名称
     */
    private String customerName;

    /**
     * 面试结果
     * 邀约量列表 0/不限、1/待面试、2/未参加、3/未通过、4/已通过
     * 到面量列表 0/不限、1/未通过、2/已通过
     */
    private Integer interviewStatus;

    /**
     * 开始时间
     */
    private Timestamp timeStart;

    /**
     * 结束时间
     */
    private Timestamp timeEnd;

    /**
     * 邀约时间
     */
    private Timestamp inviteTimeStart;

    /**
     * 邀约时间
     */
    private Timestamp inviteTimeEnd;

    /**
     * 跟进用户
     */
    private String followUser;

    /**
     * 每页多少数据
     */
    private Integer pageSize = 10;

    /**
     * 页码
     */
    private Integer pageNum;

    /**
     * 起始数量
     */
    private Integer pageBegin;

    public String getNameOrPhone() {
        return nameOrPhone;
    }

    public void setNameOrPhone(String nameOrPhone) {
        this.nameOrPhone = nameOrPhone;

        // 判断 nameOrPhone 是否全是数字
        if (isNumeric(nameOrPhone)) {
            // 如果是数字，赋值给 phone
            this.phone = nameOrPhone;
            this.userName = null;  // 确保 name 字段为空
        } else {
            // 如果不是数字，赋值给 name
            this.userName = nameOrPhone;
            this.phone = null;  // 确保 phone 字段为空
        }
    }

    private boolean isNumeric(String str) {
        return str != null && str.matches("\\d+");  // 使用正则表达式匹配纯数字
    }
}
