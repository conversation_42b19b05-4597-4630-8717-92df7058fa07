package com.ssb.approve.model.dto;

import lombok.Data;

import java.sql.Timestamp;
import java.util.List;

@Data
public class CustomerDataDTO {

    /**
     * 1/个人 2/团队
     */
    private Integer type;

    /**
     * 部门id
     */
    private Integer deptId;

    /**
     * 用户id
     */
    private Integer userId;

    private List<Integer> userIdList;

    /**
     * 企业id
     */
    private Integer enterpriseId;

    /**
     * 开始时间
     */
    private Timestamp timeStart;

    /**
     * 结束时间
     */
    private Timestamp timeEnd;
}
