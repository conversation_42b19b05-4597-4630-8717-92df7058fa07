package com.ssb.approve.model.dto;

import lombok.Data;

import java.sql.Timestamp;

@Data
public class FollowRecommendListDTO {

    private Integer userId;

    private Integer enterpriseId;

    /**
     * 求职者姓名或手机号
     */
    private String jobSeekerKeyword;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 审核结果 1/审核中 2/通过 3/未通过
     */
    private Integer status;

    /**
     * 审核状态 0未审核，1已审核
     */
    private Integer auth;

    /**
     * 时间类型 1/推荐时间 2/审核时间
     */
    private Integer timeType;

    /**
     * 开始日期
     */
    private Timestamp startTime;

    /**
     * 结束日期
     */
    private Timestamp endTime;

    /**
     * 每页多少数据
     */
    private Integer pageSize = 10;

    /**
     * 页码
     */
    private Integer pageNum;

    /**
     * 起始数量
     */
    private Integer pageBegin;

    /**
     * 审核人姓名
     */
    private String recommendUserName;

    /**
     * 未通过原因
     */
    private String failureReason;

    private Integer checkedBy;
    private Timestamp checkedTime;
}
