package com.ssb.approve.model.dto;

import lombok.Data;

import java.sql.Timestamp;
import java.util.List;
import java.util.Calendar;
import java.time.LocalDateTime;
import java.time.ZoneId;

@Data
public class BasicDataDTO {

    /**
     * 1/个人 2/团队
     */
    private Integer type;

    /**
     * 部门id
     */
    private Integer deptId;

    /**
     * 用户id
     */
    private Integer userId;

    private List<Integer> userIdList;

    /**
     * 企业id
     */
    private Integer enterpriseId;

    /**
     * 时间类型 0/全部 1/日 2/周 3/月 4/年
     */
    private Integer timeType;

    /**
     * 开始时间
     */
    private Timestamp timeStart;

    /**
     * 结束时间
     */
    private Timestamp timeEnd;

    /**
     * 对比开始时间
     */
    private Timestamp compareTimeStart;

    /**
     * 对比结束时间
     */
    private Timestamp compareTimeEnd;

    /**
     * 根据时间类型设置时间范围
     * 时间类型 0/全部 1/日 2/周 3/月 4/年
     */
    public void setTimeRangeByType() {
        if (timeType == null || timeType == 0) {
            // 全部时间类型，不设置时间范围
            return;
        }

        Calendar calendar = Calendar.getInstance();
        LocalDateTime now = LocalDateTime.now();
        
        switch (timeType) {
            // 日
            case 1:
                // 设置今天时间范围
                calendar.set(Calendar.HOUR_OF_DAY, 0);
                calendar.set(Calendar.MINUTE, 0);
                calendar.set(Calendar.SECOND, 0);
                calendar.set(Calendar.MILLISECOND, 0);
                this.timeStart = new Timestamp(calendar.getTimeInMillis());
                
                this.timeEnd = new Timestamp(now.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
                
                // 设置昨天时间范围
                calendar.add(Calendar.DAY_OF_MONTH, -1);
                this.compareTimeStart = new Timestamp(calendar.getTimeInMillis());
                
                calendar.set(Calendar.HOUR_OF_DAY, 23);
                calendar.set(Calendar.MINUTE, 59);
                calendar.set(Calendar.SECOND, 59);
                this.compareTimeEnd = new Timestamp(calendar.getTimeInMillis());
                break;
            // 周
            case 2:
                // 设置本周时间范围
                calendar.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
                calendar.set(Calendar.HOUR_OF_DAY, 0);
                calendar.set(Calendar.MINUTE, 0);
                calendar.set(Calendar.SECOND, 0);
                calendar.set(Calendar.MILLISECOND, 0);
                this.timeStart = new Timestamp(calendar.getTimeInMillis());
                
                this.timeEnd = new Timestamp(now.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
                
                // 设置上周时间范围
                calendar.add(Calendar.WEEK_OF_YEAR, -1);
                this.compareTimeStart = new Timestamp(calendar.getTimeInMillis());
                
                calendar.add(Calendar.DAY_OF_WEEK, now.getDayOfWeek().getValue() - 1);
                calendar.set(Calendar.HOUR_OF_DAY, 23);
                calendar.set(Calendar.MINUTE, 59);
                calendar.set(Calendar.SECOND, 59);
                this.compareTimeEnd = new Timestamp(calendar.getTimeInMillis());
                break;
            // 月
            case 3:
                // 设置本月时间范围
                calendar.set(Calendar.DAY_OF_MONTH, 1);
                calendar.set(Calendar.HOUR_OF_DAY, 0);
                calendar.set(Calendar.MINUTE, 0);
                calendar.set(Calendar.SECOND, 0);
                calendar.set(Calendar.MILLISECOND, 0);
                this.timeStart = new Timestamp(calendar.getTimeInMillis());
                
                this.timeEnd = new Timestamp(now.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
                
                // 设置上月时间范围
                calendar.add(Calendar.MONTH, -1);
                this.compareTimeStart = new Timestamp(calendar.getTimeInMillis());
                
                calendar.set(Calendar.DAY_OF_MONTH, now.getDayOfMonth());
                calendar.set(Calendar.HOUR_OF_DAY, 23);
                calendar.set(Calendar.MINUTE, 59);
                calendar.set(Calendar.SECOND, 59);
                this.compareTimeEnd = new Timestamp(calendar.getTimeInMillis());
                break;
            // 年
            case 4:
                // 设置本年时间范围
                calendar.set(Calendar.DAY_OF_YEAR, 1);
                calendar.set(Calendar.HOUR_OF_DAY, 0);
                calendar.set(Calendar.MINUTE, 0);
                calendar.set(Calendar.SECOND, 0);
                calendar.set(Calendar.MILLISECOND, 0);
                this.timeStart = new Timestamp(calendar.getTimeInMillis());
                
                this.timeEnd = new Timestamp(now.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
                
                // 设置去年时间范围
                calendar.add(Calendar.YEAR, -1);
                this.compareTimeStart = new Timestamp(calendar.getTimeInMillis());
                
                calendar.set(Calendar.DAY_OF_YEAR, now.getDayOfYear());
                calendar.set(Calendar.HOUR_OF_DAY, 23);
                calendar.set(Calendar.MINUTE, 59);
                calendar.set(Calendar.SECOND, 59);
                this.compareTimeEnd = new Timestamp(calendar.getTimeInMillis());
                break;
        }
    }
}
