package com.ssb.approve.model.dto;

import lombok.Data;

import java.sql.Timestamp;
import java.util.List;

@Data
public class NewlyAddedProjectDataListDTO {

    /**
     * 1/个人 2/团队
     */
    private Integer type;

    /**
     * 部门id
     */
    private Integer deptId;

    /**
     * 用户id
     */
    private Integer userId;

    private List<Integer> userIdList;

    /**
     * 企业id
     */
    private Integer enterpriseId;

    /**
     * 开始时间
     */
    private Timestamp timeStart;

    /**
     * 结束时间
     */
    private Timestamp timeEnd;

    /**
     * 项目名称
     */
    private String title;

    /**
     * 企业名称
     */
    private String customerName;

    /**
     * 状态 不限(不传)、1/进行中、2/已暂停、3/已结束
     */
    private Integer status;

    /**
     * 负责人
     */
    private String masterName;


    /**
     * 每页多少数据
     */
    private Integer pageSize = 10;

    /**
     * 页码
     */
    private Integer pageNum;

    /**
     * 起始数量
     */
    private Integer pageBegin;
}
