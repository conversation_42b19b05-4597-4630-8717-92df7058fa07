package com.ssb.approve.model.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025/5/19 15:00
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserPerformanceDTO {

    private List<Integer> userIds;

    private Integer eid;

    private Date startTime;

    private Date endTime;
}
