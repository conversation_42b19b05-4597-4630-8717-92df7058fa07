package com.ssb.approve.model.dto;

import lombok.Data;

import java.sql.Timestamp;
import java.util.List;

@Data
public class CustomerDataListDTO {

    /**
     * 1/个人 2/团队
     */
    private Integer type;

    /**
     * 部门id
     */
    private Integer deptId;

    /**
     * 用户id
     */
    private Integer userId;

    private List<Integer> userIdList;

    /**
     * 0/未合作  1/合作中  2/合作终止  3/预警
     */
    private Integer status;

    /**
     * 企业id
     */
    private Integer enterpriseId;

    /**
     * 开始时间
     */
    private Timestamp timeStart;

    /**
     * 结束时间
     */
    private Timestamp timeEnd;

    /**
     * 企业名称
     */
    private String customerName;


    /**
     * 负责人
     */
    private String masterName;


    /**
     * 每页多少数据
     */
    private Integer pageSize = 10;

    /**
     * 页码
     */
    private Integer pageNum;

    /**
     * 起始数量
     */
    private Integer pageBegin;
}
