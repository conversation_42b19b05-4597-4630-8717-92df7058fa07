package com.ssb.approve.model.dto;

import lombok.Data;

import java.sql.Timestamp;
import java.util.List;

@Data
public class TheChartsDTO {

    /**
     *  部门id
     */
    private Integer deptId;

    /**
     * 用户id
     */
    private Integer userId;
    private List<Integer> userIdList;

    /**
     * 企业id
     */
    private Integer enterpriseId;

    /**
     *  用户名
     */
    private String userName;

    /**
     * 结算时间
     */
    private Timestamp settlementTimeStart;
    private Timestamp settlementTimeEnd;

    /**
     * 1/首页  2/全部
     */
    private Integer type;

    /**
     * 每页多少数据
     */
    private Integer pageSize = 10;

    /**
     * 首页数据
     */
    private Integer homePageSize = 5;

    /**
     * 页码
     */
    private Integer pageNum;

    /**
     * 起始数量
     */
    private Integer pageBegin;
}
