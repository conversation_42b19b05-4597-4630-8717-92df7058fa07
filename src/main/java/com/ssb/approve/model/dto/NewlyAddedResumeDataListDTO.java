package com.ssb.approve.model.dto;

import lombok.Data;

import java.sql.Timestamp;
import java.util.List;

@Data
public class NewlyAddedResumeDataListDTO {

    /**
     * 1/个人 2/团队
     */
    private Integer type;

    /**
     * 部门id
     */
    private Integer deptId;

    /**
     * 用户id
     */
    private Integer userId;

    private List<Integer> userIdList;

    /**
     * 企业id
     */
    private Integer enterpriseId;

    /**
     * 开始时间
     */
    private Timestamp timeStart;

    /**
     * 结束时间
     */
    private Timestamp timeEnd;

    /**
     * 名字或手机号
     */
    private String nameOrPhone;

    /**
     * 求职者姓名
     */
    private String name;

    /**
     * 求职者姓名
     */
    private String phone;

    /**
     * 期望职位
     */
    private Integer positionFirst;
    private Integer positionSecond;
    private Integer positionThird;

    /**
     * 期望地点
     */
    private Integer province;
    private Integer city;
    private Integer district;

    /**
     * 学历(不限（不传） 1初中以下 2高中 3中专技校 4大专 5本科 6硕士及以上)
     */
    private Integer degree;

    /**
     * 工作经验 0/应届生、1/1年、2/2年、3/3年、4/4年、5/5年、6/6年、7/7年、8/8年、9/9年、10/10年、11/10年以上
     */
    private Integer exp;


    /**
     * 每页多少数据
     */
    private Integer pageSize = 10;

    /**
     * 页码
     */
    private Integer pageNum;

    /**
     * 起始数量
     */
    private Integer pageBegin;

    public String getNameOrPhone() {
        return nameOrPhone;
    }

    public void setNameOrPhone(String nameOrPhone) {
        this.nameOrPhone = nameOrPhone;

        // 判断 nameOrPhone 是否全是数字
        if (isNumeric(nameOrPhone)) {
            // 如果是数字，赋值给 phone
            this.phone = nameOrPhone;
            this.name = null;  // 确保 name 字段为空
        } else {
            // 如果不是数字，赋值给 name
            this.name = nameOrPhone;
            this.phone = null;  // 确保 phone 字段为空
        }
    }

    private boolean isNumeric(String str) {
        return str != null && str.matches("\\d+");  // 使用正则表达式匹配纯数字
    }
}
