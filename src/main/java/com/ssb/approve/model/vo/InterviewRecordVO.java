package com.ssb.approve.model.vo;

import com.ssb.approve.entity.InterviewRecord;
import lombok.Data;

import java.sql.Timestamp;

/**
 * @ClassName: InterviewRecordVO
 * @Description: 面试记录 VO
 * @Author: YZK
 * @Date: 2019年12月23日20:44:59
 **/
@Data
public class InterviewRecordVO extends InterviewRecord {

    private String title;   //标题

    private Integer contactId; //求职用户id

    private Integer projectId; //项目id

    private String contactName; //求职者姓名

    private String contactPhone; // 求职者手机号

    private String userName; //跟进人

    private Integer interviewType; ////面试类型 0 单轮 1 多轮

    private Integer interviewUpperLimit; //每日约面上限 非0的3位以内正整数

    private Integer type; //用户类型  0/crm求职用户  1/app同步用户

    /**
     * 审核人
     */
    private String reviewUser;

    private Timestamp reviewTime;
}
