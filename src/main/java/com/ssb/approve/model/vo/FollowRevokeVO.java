package com.ssb.approve.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.sql.Timestamp;

/**
 * @Author: WangShiyong
 * @Date: 2021/10/15 3:15 PM
 * @Description:
 */
@Data
public class FollowRevokeVO {

    @ApiModelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value = "跟进id")
    private Integer followId;

    @ApiModelProperty(value = "简历用户id")
    private Integer contactId;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Timestamp behaviorTime; //时间  面试时间/入职时间/结算时间

    @ApiModelProperty(value = "结算节点")
    private Integer node;

    @ApiModelProperty(value = "项目标题")
    private String title;

    @ApiModelProperty(value = "求职者姓名")
    private String contactName;

    @ApiModelProperty(value = "手机号")
    private String contactPhone;

    @ApiModelProperty(value = "面试结果/入职结果")
    private String result;

    @ApiModelProperty(value = "结算结果")
    private String settleResult;

    @ApiModelProperty(value = "跟进人")
    private String userName;

    @ApiModelProperty(value = "用户类型  0/crm求职用户  1/app同步用户")
    private Integer type;

    /**
     * 项目id
     */
    private Integer projectId;
}
