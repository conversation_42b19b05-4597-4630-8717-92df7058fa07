package com.ssb.approve.model.vo;

import com.ssb.approve.entity.CommissionStrategy;
import com.ssb.approve.entity.SalesCommissionCut;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: WangShiyong
 * @Date: 2020/12/22 9:31 AM
 * @Description:
 */
@Data
public class CommissionStrategyVO extends CommissionStrategy {

    @ApiModelProperty(value = "创建人")
    private String userName;

    @ApiModelProperty(value = "相关项目总数")
    private Integer projectCount;

    @ApiModelProperty(value = "相关项目-在招数量")
    private Integer projectRecruitingCount;

    @ApiModelProperty(value = "相关项目-暂停数量")
    private Integer suspendCount;

    @ApiModelProperty(value = "相关项目-结束数量")
    private Integer stopCount;

    @ApiModelProperty(value = "策略难度")
    private List<CommissionStrategyDifficultyVO> difficultyList;

    @ApiModelProperty(value = "提成比例")
    private List<CommissionStrategyRatioVO> ratioList;

    @ApiModelProperty(value = "销售超一年削减比例")
    private List<SalesCommissionCut> salesCommissionCutList;
}
