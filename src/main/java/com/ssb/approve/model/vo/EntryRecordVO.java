package com.ssb.approve.model.vo;

import com.ssb.approve.entity.EntryRecord;
import lombok.Data;

/**
 * @ClassName: EntryRecordDAO
 * @Description: 入职记录 dao
 * @Author: YZK
 * @Date: 2019年12月25日16:30:12
 **/
@Data
public class EntryRecordVO extends EntryRecord {

    private String title;   //标题

    private Integer contactId; //求职者id

    private Integer projectId; //项目id

    private String contactName; //求职者姓名

    private String contactPhone; // 求职者手机号

    private String userName; //跟进人

    private Integer type; //用户类型  0/crm求职用户  1/app同步用户
}
