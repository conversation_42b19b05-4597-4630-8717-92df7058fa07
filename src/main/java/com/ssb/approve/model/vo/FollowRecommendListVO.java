package com.ssb.approve.model.vo;

import lombok.Data;

import java.sql.Timestamp;

@Data
public class FollowRecommendListVO {

    private Integer id;

    /**
     * 简历id
     */
    private Integer resumeId;

    /**
     * 简历用户id
     */
    private Integer contactId;

    /**
     * 求职者姓名
     */
    private String jobSeekerName;

    /**
     * 求职者手机号
     */
    private String jobSeekerPhone;

    /**
     * 项目id
     */
    private Integer projectId;

    /**
     * 项目名
     */
    private String projectName;

    /**
     * 推荐时间
     */
    private Timestamp recommendTime;

    /**
     * 审核结果 1/审核中 2/通过 3/未通过
     */
    private Integer status;

    /**
     * 审核状态 0未审核，1已审核
     */
    private Integer auth;

    /**
     * 未通过原因
     */
    private String failureReason;

    /**
     * 审核时间
     */
    private Timestamp checkedTime;

    /**
     * 审核人姓名
     */
    private String recommendUserName;

    /**
     * 跟进人姓名
     */
    private String followUserName;
}
