package com.ssb.approve.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ssb.approve.entity.EntryRecord;
import com.ssb.approve.entity.FollowUp;
import com.ssb.approve.entity.InterviewRecord;
import com.ssb.approve.entity.SettleDetails;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.List;
import java.util.Map;

/**
 * @ClassName: FollowUpVO
 * @Description: 项目跟进审核 VO
 * @Author: YZK
 * @Date: 2019年12月20日18:08:20
 **/
@Data
public class FollowUpVO extends FollowUp {

    private Integer followUpId; // 跟进id

    private String contactName; //求职用户名称

    private String contactPhone; //求职用户手机号

    private String title; //标题

    private String fullName; // 公司名称

    private String positionFirstName; //一级职位名称

    private String positionSecondName; // 二级职位名称

    private Integer userId; //跟进用户id

    private String userName; //跟进用户名称

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp behaviorTime; //行为时间 面试时间、入职时间等等。。。。

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp estimateTime; //估计入职时间

    private Integer node; //多轮面试  轮数

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp settleTime; //预估结算时间

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp entryTime; //入职时间

    private Integer salary; //金额

    private Integer performance; //业绩

    private Integer interviewType; //面试类型 0 单轮 1 多轮

    private Integer interviewUpperLimit; //每日约面上限 非0的3位以内正整数

    private Integer auth; //审核状态 0 未审核  1 审核

    private InterviewRecord interviewRecord;    // 面试记录

    private EntryRecord entryRecord;            // 入职记录

    private List<SettleDetails> settleDetails;  // 结算记录

    private String deptName; //团队名称

    private Integer date; //天数

    private Integer tmpStatus; //结算详情  临时状态0 默认 1 结算  2 离职

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Timestamp leaveTime; //离职时间

    private String leaveCause; // 错误信息 离职信息

    private BigDecimal expectCommission; // 预计提成

//    private Integer expectIntegral; // 预计积分

    private List<Map<String, Object>> expectCommissionDetail; // 预计提成明细

    private boolean newProject;// 是否是新项目

    private Integer type; // 用户类型  0/crm求职用户   1/app同步用户

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp interviewTime; //面试时间

    private Integer isAdministrators; //是否是管理员 0/否 1/是

    private Integer master;//负责人id

    private Integer signingUserId;//项目经理id
    private Integer customerId;//客户id
    private Integer sumNode;//结算共几次

    /**
     * 跟进类型 1/本公司跟进 2/接单方跟进
     */
    private Integer followType;

    /**
     * 项目类型 1/内部 2/外部
     */
    private Integer projectSource;

    /**
     * 是否跳转企业  1/跳  2/不跳
     */
    private Integer companyJump;

    /**
     * 备注
     */
    private String remark;
}
