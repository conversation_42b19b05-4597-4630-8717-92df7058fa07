package com.ssb.approve.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ssb.approve.common.constants.FollowUpConstants;
import lombok.Data;

import java.math.BigDecimal;
import java.sql.Timestamp;

/**
 * @Author: WangShiyong
 * @Date: 2021/5/6 1:58 PM
 * @Description:提成报表
 */
@Data
public class CommissionStatisticVO {

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp commissionTime;//提成时间

    private String title;//项目名称

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Timestamp contractStartTime;//合同开始时间

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Timestamp contractEndTime;//合同结束时间

    private Integer type;//长招项目  0 默认 1 长招项目

    private String signingUser;//签单销售

    private String followUser;//跟进销售

    private String name;//求职者

    private String fullName;//公司名称

    private String phone;//电话

    private Integer integral;//积分

    private Integer currentStatus;//结算状态

    private String currentStatusStr;//结算状态

    public String getCurrentStatusStr() {
        return FollowUpConstants.name(currentStatus);
    }

    public void setCurrentStatusStr(String currentStatusStr) {
        this.currentStatusStr = currentStatusStr;
    }

    private String cityName;//提成策略标题（城市）

    private String headhunter;//猎头
}
