package com.ssb.approve.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: Wang<PERSON>hiyong
 * @Date: 2020/12/24 2:15 PM
 * @Description:
 */
@Data
public class CommissionStrategyRatioVO {

    @ApiModelProperty(value = "id")
    private Integer id;
    @ApiModelProperty(value = "提成类型   0/招聘提成   1/销售提成")
    private Integer type;
    @ApiModelProperty(value = "提成级别id")
    private Integer commissionLevelId;
    @ApiModelProperty(value = "高")
    private String high;
    @ApiModelProperty(value = "中高")
    private String baseHigh;
    @ApiModelProperty(value = "基础")
    private String base;
    @ApiModelProperty(value = "中低")
    private String baseLow;
    @ApiModelProperty(value = "低")
    private String low;

}
