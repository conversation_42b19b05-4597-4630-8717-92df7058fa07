package com.ssb.approve.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author: WangShiyong
 * @Date: 2020/12/24 2:01 PM
 * @Description:
 */
@Data
public class CommissionStrategyDifficultyVO {

    @ApiModelProperty(value = "id")
    private Integer id;
    @ApiModelProperty(value = "行业分类id")
    private Integer classifyId;
    @ApiModelProperty(value = "到面结算率")
    private BigDecimal settlementRate;
    @ApiModelProperty(value = "高")
    private String high;
    @ApiModelProperty(value = "中高")
    private String baseHigh;
    @ApiModelProperty(value = "基础")
    private String base;
    @ApiModelProperty(value = "中低")
    private String baseLow;
    @ApiModelProperty(value = "低")
    private String low;

    @ApiModelProperty(value = "一级行业id")
    private Integer industryFirstId;
    @ApiModelProperty(value = "一级行业")
    private String industryFirstName;
    @ApiModelProperty(value = "二级行业id")
    private Integer industrySecondId;
    @ApiModelProperty(value = "二级行业")
    private String industrySecondName;
    @ApiModelProperty(value = "一级岗位id")
    private Integer positionFirstId;
    @ApiModelProperty(value = "一级岗位")
    private String positionFirstName;
    @ApiModelProperty(value = "二级岗位id")
    private Integer positionSecondId;
    @ApiModelProperty(value = "二级岗位")
    private String positionSecondName;
}
