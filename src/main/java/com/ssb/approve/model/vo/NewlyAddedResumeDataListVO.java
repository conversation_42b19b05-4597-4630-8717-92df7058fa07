package com.ssb.approve.model.vo;

import lombok.Data;

import java.sql.Timestamp;
import java.util.List;


@Data
public class NewlyAddedResumeDataListVO {

    /**
     * 简历用户id
     */
    private Integer contactId;

    /**
     * 求职者姓名
     */
    private String name;

    /**
     * 求职者手机号
     */
    private String phone;

    /**
     * 期望职位
     */
    private String positionName;

    /**
     * 期望地点
     */
    private List<String> address;

    private Integer age;

    /**
     * 学历(不限（不传） 1初中以下 2高中 3中专技校 4大专 5本科 6硕士及以上)
     */
    private Integer degree;

    /**
     * 工作经验 0/应届生、1/1年、2/2年、3/3年、4/4年、5/5年、6/6年、7/7年、8/8年、9/9年、10/10年、11/10年以上
     */
    private Integer exp;

    /**
     * 录入时间
     */
    private Timestamp createTime;

}
