package com.ssb.approve.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ssb.approve.common.constants.FollowUpConstants;
import lombok.Data;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.List;
import java.util.Map;

/**
 * @Author: WangShiyong
 * @Date: 2021/5/14 10:14 AM
 * @Description:预计提成列表
 */
@Data
public class EstimatedCommissionStatisticVO {

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp lastFollowTime;//最后跟进时间

    private String title;//项目名称

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Timestamp contractStartTime;//合同开始时间

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Timestamp contractEndTime;//合同结束时间

    private Integer type;//长招项目  0 默认 1 长招项目

    private String signingUser;//签单销售

    private String followUser;//跟进销售

    private String headhunter;//猎头

    private Integer headhunterId;//猎头id

    private Integer headhunterStatus;//猎头状态  0正常   1离职   2冻结

    private String sale;//销售

    private Integer saleId;//销售id

    private Integer saleStatus;//销售用户状态  0正常   1离职   2冻结

//    private Integer commissionType;//提成类型  0/猎头  1/销售
//
//    private String commissionUser;//提成人

    private Integer commissionUserId;//提成人id

    private String name;//求职者

    private String fullName;//公司名称

    private String phone;//电话

    private BigDecimal integral;//积分

//    private BigDecimal money;//金额

    private Integer currentStatus;//结算状态

    private String currentStatusStr;//结算状态

    private String rateContent;//结算信息

    private String settlementContent;//节点信息

    private Integer projectId;//项目id

    private Integer followId;//跟进id

    private List<Map<String, Object>> expectCommissionDetail; // 预计提成明细

    private Integer deptId;//处理后的提成人部门id

    private Integer saleDeptId;//签单销售部门id

    private Integer headhunterDeptId;//猎头部门id

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Timestamp firstContractStartTime;//第一期项目-合同开始时间

    private BigDecimal commission;//提成比例

    private String cityName;//城市名（提成策略标题）

    public String getCurrentStatusStr() {
        return FollowUpConstants.name(currentStatus);
    }

    public void setCurrentStatusStr(String currentStatusStr) {
        this.currentStatusStr = currentStatusStr;
    }
}
