package com.ssb.approve.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ssb.approve.common.constants.EntryConstants;
import com.ssb.approve.common.constants.InterviewConstants;
import com.ssb.approve.common.constants.SettleConstants;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.sql.Timestamp;

/**
 * @Author: WangShiyong
 * @Date: 2021/10/21 3:18 PM
 * @Description:
 */
@Data
public class FollowRevokeStatisticsVO {

    @ApiModelProperty(value = "撤销id")
    private Integer id;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Timestamp behaviorTime; //时间  面试时间/入职时间/结算时间

    @ApiModelProperty(value = "项目标题")
    private String title;

    @ApiModelProperty(value = "求职者姓名")
    private String contactName;

    @ApiModelProperty(value = "手机号")
    private String contactPhone;

    @ApiModelProperty(value = "撤销类型  0/面试  1/入职  2/结算")
    private Integer type;

    @ApiModelProperty(value = "撤销前状态根据type对应" +
                            "面试：状态0 默认 1 未参加 2 改约时间  3 未通过  4 下一轮面试  5 发offer" +
                            "入职：状态0 默认 1 已入职 2 更改入职时间  3 拒绝入职" +
                            "结算：状态0 默认 1 结算   2 离职")
    private Integer statusBeforeRevocation;

    @ApiModelProperty(value = "跟进人")
    private String userName;

    @ApiModelProperty(value = "撤销备注")
    private String revokeRemark;

    @ApiModelProperty(value = "撤销时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String revokeTime;

    @ApiModelProperty(value = "撤销人")
    private String revokeName;

    @ApiModelProperty(value = "状态")
    private String status;

    @ApiModelProperty(value = "跟进id")
    private Integer followId;

    @ApiModelProperty(value = "简历用户id")
    private Integer contactId;

    @ApiModelProperty(value = "结算节点")
    private Integer node;

    public String getStatus() {
        String info = null;
        if(type != null && statusBeforeRevocation != null){
            switch(type){
                case 0:
                    info = "（面试）";
                    info += InterviewConstants.name(statusBeforeRevocation);
                    break;
                case 1:
                    info = "（入职）";
                    info += EntryConstants.name(statusBeforeRevocation);
                    break;
                case 2:
                    info = "（结算）";
                    info += SettleConstants.name(statusBeforeRevocation);
                    break;
                default:
                    break;
            }
        }

        return info;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}
