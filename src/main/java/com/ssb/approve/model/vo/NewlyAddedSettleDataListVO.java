package com.ssb.approve.model.vo;

import lombok.Data;

import java.sql.Timestamp;
import java.util.List;

@Data
public class NewlyAddedSettleDataListVO {

    /**
     * 简历用户id
     */
    private Integer contactId;

    /**
     * 求职者姓名
     */
    private String name;

    /**
     * 求职者手机号
     */
    private String phone;

    /**
     * 项目id
     */
    private Integer projectId;

    /**
     * 项目名称
     */
    private String title;

    /**
     * 客户id
     */
    private Integer customerId;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 跟进人
     */
    private String followUser;

    /**
     * 结算时间
     */
    private Timestamp settleTime;

    /**
     * 项目类型 1/内部 2/外部
     */
    private Integer projectSource;

    /**
     * 是否跳转企业  1/跳  2/不跳
     */
    private Integer companyJump;
}
