package com.ssb.approve.model.vo;

import lombok.Data;

import java.sql.Timestamp;

@Data
public class CooperationProjectFollowUpInfoVO {

    private Integer id;

    /**
     * 当前状态  状态 -1意向 0 约面面试 1 约面失败  2 未参加  3 改约时间  4 未通过 5 下一轮面试  6 发offer  7 已入职  8 更改入职时间  9 拒绝入职  10 结算  11 离职  12 中断
     */
    private Integer currentStatus;
    private String currentStatusStr;

    /**
     * 求职者手机号
     */
    private String phone;

    /**
     * 求职者名称
     */
    private String name;

    /**
     * 跟进人
     */
    private String followUpUserName;

    /**
     * 预约面试时间
     */
    private Timestamp interviewTime;

    /**
     * 面试结果
     */
    private Integer interviewStatus;
    private String interviewStatusStr;

    /**
     * 实际入职时间
     */
    private Timestamp entryTime;

    /**
     * 入职结果
     */
    private Integer entryStatus;
    private String entryStatusStr;

    /**
     * 结算结果
     */
    private Integer settleStatus;
    private String settleStatusStr;

    /**
     * 备注
     */
    private String remark;

    public String getCurrentStatusStr() {
        if(currentStatus != null){
            switch (currentStatus){
                case -1:
                    currentStatusStr = "有意向";
                    break;
                case 0:
                    currentStatusStr = "待面试";
                    break;
                case 3:
                    currentStatusStr = "待面试";
                    break;
                case 4:
                    currentStatusStr = "面试未通过";
                    break;
                case 6:
                    currentStatusStr = "待入职";
                    break;
                case 7:
                    currentStatusStr = "待结算";
                    break;
                case 8:
                    currentStatusStr = "待入职";
                    break;
                case 9:
                    currentStatusStr = "拒绝入职";
                    break;
                case 10:
                    currentStatusStr = "已结算";
                    break;
                case 11:
                    currentStatusStr = "已离职";
                    break;
                case 12:
                    currentStatusStr = "已中断";
                    break;
                default:
                    currentStatusStr = "-";
                    break;
            }
        } else {
            currentStatusStr = "-";
        }
        return currentStatusStr;
    }

    public String getInterviewStatusStr() {
        if(interviewStatus != null){
            switch(interviewStatus){
                case 3:
                    interviewStatusStr = "面试未通过";
                    break;
                case 5:
                    interviewStatusStr = "面试通过";
                    break;
                default:
                    interviewStatusStr = "-";
                    break;
            }
        } else {
            interviewStatusStr = "-";
        }
        return interviewStatusStr;
    }

    public String getEntryStatusStr() {
        if(entryStatus != null){
            switch(entryStatus){
                case 1:
                    entryStatusStr = "已入职";
                    break;
                case 3:
                    entryStatusStr = "拒绝入职";
                    break;
                default:
                    entryStatusStr = "-";
                    break;
            }
        } else {
            entryStatusStr = "-";
        }
        return entryStatusStr;
    }

    public String getSettleStatusStr() {
        if(settleStatus != null){
            switch(settleStatus){
                case 1:
                    settleStatusStr = "已结算";
                    break;
                case 2:
                    settleStatusStr = "已离职";
                    break;
                default:
                    settleStatusStr = "-";
                    break;
            }
        } else {
            settleStatusStr = "-";
        }
        return settleStatusStr;
    }
}
