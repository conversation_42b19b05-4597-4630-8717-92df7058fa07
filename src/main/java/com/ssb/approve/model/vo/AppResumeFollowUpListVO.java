package com.ssb.approve.model.vo;

import com.ssb.approve.common.constants.AppResumeFollowUpConstants;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.sql.Timestamp;

import static java.math.BigDecimal.ROUND_HALF_UP;

/**
 * @Author: WangShiyong
 * @Date: 2021/9/9 11:37 AM
 * @Description:app简历跟进列表
 */
@Data
public class AppResumeFollowUpListVO {

    @ApiModelProperty(value = "简历用户id")
    private Integer contactId;

    @ApiModelProperty(value = "名字")
    private String name;

    @ApiModelProperty(value = "app期望职位")
    private String appPositionName;

    @ApiModelProperty(value = "crm期望职位")
    private String crmPositionName;

    @ApiModelProperty(value = "期望地点-省份")
    private String province;

    @ApiModelProperty(value = "期望地点-市")
    private String city;

    @ApiModelProperty(value = "期望地点-区")
    private String district;

    @ApiModelProperty(value = "入库时间")
    private Timestamp createTime;

    @ApiModelProperty(value = "跟进负责人")
    private String followUpPersonInCharge;

    @ApiModelProperty(value = "跟进状态  0/待跟进  1/已跟进")
    private Integer followStatus;

    @ApiModelProperty(value = "转化结果  0/未转化  1/已转化")
    private Integer changeResult;

    @ApiModelProperty(value = "交付状态 0/有意向 1/待面试 2/面试未通过 3/未到面 4/发offer 5/拒绝入职 6/待入职 7/待结算 8/已结算 9/离职")
    private Integer followResult;

    @ApiModelProperty(value = "当前跟进人")
    private String userName;

    @ApiModelProperty(value = "首次跟进时间")
    private Timestamp firstFollowTime;

    @ApiModelProperty(value = "跟进间隔")
    private String followUpInterval;

    @ApiModelProperty(value = "交付状态")
    private String followResultStr;

    public String getFollowResultStr() {
        if(followResult != null){
            return AppResumeFollowUpConstants.name(followResult);
        }
        return followResultStr;
    }

    public void setFollowResultStr(String followResultStr) {
        this.followResultStr = followResultStr;
    }

    public String getFollowUpInterval() {
        //一小时内用分钟，大于一小时小于两小时，用1.X小时显示，大于两小时以后，显示大于两小时。
        if(createTime != null && firstFollowTime != null){
            long time = firstFollowTime.getTime() - createTime.getTime();
            long oneHour = 60 * 60 * 1000;
            long twoHour = 120 * 60 * 1000;
            if (time < oneHour) {
                time = time / 60 / 1000;
                return time + "分钟";
            } else if (oneHour <= time && time < twoHour) {
                return new BigDecimal(time).divide(new BigDecimal(60 * 60 *1000), 2, ROUND_HALF_UP) + "小时";
            } else if (time >= twoHour) {
                return "大于两小时";
            }
        }

        return followUpInterval;
    }

    public void setFollowUpInterval(String followUpInterval) {
        this.followUpInterval = followUpInterval;
    }

}
