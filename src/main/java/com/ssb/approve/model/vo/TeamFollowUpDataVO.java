package com.ssb.approve.model.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @ClassName: TeamFollowUpDataVO
 * @Description: 团队跟进数据
 * @Author: YZK
 * @Date: 2020年9月2日15:53:19
 **/
@Data
public class TeamFollowUpDataVO {

    private Integer userId; // 用户id

    private Integer deptId; // 团队id

    private String name;// 用户名称

    private Integer level; //级别：3高级负责人2中级负责人1初级负责人0成员

    private Integer status; // 状态：0正常1离职2冻结

    private String phone; //手机号

    private String deptName; //团队名称

    private Integer inviteCount; // 邀约量

    private Integer interviewCount; //到面量

    private Integer offerCount; //offer量

    private Integer entryCount; //入职量

    private Integer settleCount; //结算量

    private Integer callCount; //电话量

    private Integer wechatCount; //微信量

    private Integer resumeCount; //简历量

    private Integer introduceCount; //转介绍量

    private Integer contractCount; // 签约量

    private BigDecimal integral; //积分
    private BigDecimal commission; //提成金额

}
