package com.ssb.approve.model.vo;

import lombok.Data;

import java.sql.Timestamp;

@Data
public class RecruitmentDataListVO {

    /**
     * 求职者id
     */
    private Integer contactId;

    /**
     *  求职者
     */
    private String contactName;

    /**
     *  手机号
     */
    private String phone;

    /**
     * 项目id
     */
    private Integer projectId;

    /**
     *  项目名
     */
    private String title;

    /**
     * 客户id
     */
    private Integer customerId;

    /**
     *  客户名称
     */
    private String customerName;

    /**
     * 面试结果
     */
    private Integer interviewStatus;
    private String interviewStatusStr;


    /**
     * 邀约时间
     */
    private Timestamp invitationForInterviewTime;

    /**
     * 面试时间
     */
    private Timestamp interviewTime;

    /**
     * 入职时间
     */
    private Timestamp entryTime;

    /**
     * 结算时间
     */
    private Timestamp settleTime;

    /**
     * 跟进用户id
     */
    private Integer followUserId;

    /**
     *  跟进用户名
     */
    private String followUserName;

    /**
     * 0猎头端录入，1企业端录入，2平台端录入, 3猎头端分享， 4平台端分享
     */
    private Integer source;

    public String getInterviewStatusStr() {
        if(interviewStatus != null){
            switch (interviewStatus) {
                case 0:
                    interviewStatusStr = "待面试";
                    break;
                case 1:
                    interviewStatusStr = "未参加";
                    break;
                case 2:
                    interviewStatusStr = "待面试";
                    break;
                case 3:
                    interviewStatusStr = "未通过";
                    break;
                case 4:
                    interviewStatusStr = "待面试";
                    break;
                case 5:
                    interviewStatusStr = "已通过";
                    break;
                default:
                    interviewStatusStr = "-";
                    break;
            }
        }
        return interviewStatusStr;
    }

    public void setInterviewStatusStr(String interviewStatusStr) {
        this.interviewStatusStr = interviewStatusStr;
    }
}
