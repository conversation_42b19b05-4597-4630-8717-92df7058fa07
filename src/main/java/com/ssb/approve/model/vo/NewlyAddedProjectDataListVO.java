package com.ssb.approve.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.sql.Timestamp;
import java.util.List;

@Data
public class NewlyAddedProjectDataListVO {

    /**
     * 项目id
     */
    private Integer projectId;

    /**
     * 项目名称
     */
    private String title;

    /**
     * 客户id
     */
    private Integer customerId;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 负责人
     */
    private String masterName;

    /**
     * 状态 1/进行中、2/已暂停、3/已结束
     */
    private Integer status;

    @ApiModelProperty(value = "剩余名额")
    private Integer surplusQuota;

    @ApiModelProperty(value = "招聘名额")
    private Integer recruitCount;

    /**
     * 创建时间
     */
    private Timestamp createTime;

    /**
     * 是否跳转企业  1/跳  2/不跳
     */
    private Integer companyJump;
}
