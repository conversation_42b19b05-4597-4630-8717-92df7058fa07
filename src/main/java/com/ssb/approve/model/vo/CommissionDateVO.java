package com.ssb.approve.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.sql.Timestamp;

/**
 * @Author: WangShiyong
 * @Date: 2020/12/31 10:33 AM
 * @Description:
 */
@Data
public class CommissionDateVO {

    private String title;   //标题

    private Integer contactId; //求职者id

    private Integer projectId; //项目id

    private String contactName; //求职者姓名

    private String contactPhone; // 求职者手机号

    private String userName; //提成人

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp commissionTime; //结算时间

    private Integer type; //提成类型 0/招聘提成  1/销售提成

    private String commissionType; //提成类型

    private Integer integral; //积分

    private BigDecimal commission; //提成金额

    private Integer commissionLevelId; // 提成级别id

    private String levelName; //提成名

    public String getCommissionType() {
        if(type == null || commissionLevelId == null || levelName == null){
            return null;
        }

        if(commissionLevelId == 1 || commissionLevelId == 2){
            return type == 0 ? "招聘提成" : "销售提成";
        }else{
            return type == 0 ? "招聘管理提成" + levelName : "销售管理提成" + levelName;
        }
    }

    public void setCommissionType(String commissionType) {
        this.commissionType = commissionType;
    }
}
