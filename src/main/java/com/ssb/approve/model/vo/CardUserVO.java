package com.ssb.approve.model.vo;

import lombok.Data;

import java.sql.Timestamp;
import java.util.List;

@Data
public class CardUserVO {

    /**
     * 用户id
     */
    private Integer id;

    /**
     * 类型 1/个人  2/管理员  3/超级管理员
     */
    private Integer type;

    /**
     * 头像
     */
    private String head;

    /**
     * 名字
     */
    private String name;

    /**
     * 部门
     */
    private String deptName;

    /**
     * 企业
     */
    private String enterpriseName;

    /**
     * 入职时间
     */
    private Timestamp entryTime;

    /**
     * 用户id集合
     */
    private List<Integer> userIdList;
}
