package com.ssb.approve.model.vo;

import com.ssb.approve.entity.SettleDetails;
import lombok.Data;

/**
 * @ClassName: SettleDetails
 * @Description: 结算明细记录 VO
 * @Author: YZK
 * @Date: 2019年12月27日19:35:02
 **/
@Data
public class SettleDetailsVO extends SettleDetails {

    private String title;   //标题

    private Integer contactId; //求职者id

    private Integer projectId; //项目id

    private String contactName; //求职者姓名

    private String contactPhone; // 求职者手机号

    private String userName; //跟进人

    private Integer performance; // 业绩

    private Integer payment; //扣款

    private Integer type; //用户类型  0/crm求职用户  1/app同步用户

    private String remark; //备注
}
