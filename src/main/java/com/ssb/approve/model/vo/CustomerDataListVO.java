package com.ssb.approve.model.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.sql.Timestamp;

@Data
public class CustomerDataListVO {


    /**
     * 客户id
     */
    private Integer customerId;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 合作项目数量
     */
    private Integer projectCount;

    /**
     * 负责人
     */
    private String masterName;

    /**
     * 创建时间
     */
    private Timestamp createTime;

    /**
     * 账户余额
     */
    private BigDecimal accountBalance;

    /**
     * 待付款金额
     */
    private BigDecimal payment;
}
