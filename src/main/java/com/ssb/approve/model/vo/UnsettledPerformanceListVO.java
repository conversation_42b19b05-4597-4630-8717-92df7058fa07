package com.ssb.approve.model.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.sql.Timestamp;

@Data
public class UnsettledPerformanceListVO {

    /**
     * 求职者id
     */
    private Integer contactId;

    /**
     *  求职者
     */
    private String contactName;

    /**
     *  手机号
     */
    private String phone;

    /**
     * 项目id
     */
    private Integer projectId;

    /**
     *  项目名
     */
    private String title;

    /**
     * 客户id
     */
    private Integer customerId;

    /**
     *  客户名称
     */
    private String customerName;

    /**
     * 跟进用户id
     */
    private Integer followUserId;

    /**
     *  跟进用户名
     */
    private String followUserName;

    /**
     *  业绩
     */
    private BigDecimal performance;

    /**
     *  预计实际结算时间
     */
    private Timestamp settleTime;

    /**
     * 项目类型 1/内部 2/外部
     */
    private Integer projectSource;

    /**
     * 是否跳转企业  1/跳  2/不跳
     */
    private Integer companyJump;
}
