package com.ssb.approve.model.vo;

import lombok.Data;

import java.sql.Timestamp;
import java.util.List;

@Data
public class NewlyAddedCustomerDataListVO {


    /**
     * 客户id
     */
    private Integer customerId;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 合作状态 0/未合作、1/合作中、2/合作终止
     */
    private Integer status;

    /**
     * 合作项目数量
     */
    private Integer projectCount;

    /**
     * 负责人
     */
    private String masterName;

    /**
     * 创建时间
     */
    private Timestamp createTime;
}
