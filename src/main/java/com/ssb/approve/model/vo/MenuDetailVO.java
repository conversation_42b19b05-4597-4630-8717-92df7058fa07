package com.ssb.approve.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;

/**
 * @Author: WangShiYong
 * @Date: 2021/10/15 15:00 AM
 */
@ApiModel(value = "权限菜单返回实体")
@Data
public class MenuDetailVO implements Serializable {

    @ApiModelProperty(value = "主键")
    private Integer id;

    @NotNull(message = "分类编号不能为空")
    @ApiModelProperty(value = "父类编号")
    private Integer parentId;

    @ApiModelProperty(value = "所有父类编号")
    private String parentIds;

    @NotNull(message = "名称不能为空")
    @ApiModelProperty(value = "菜单名称")
    private String name;

    @NotNull(message = "路由不能为空")
    @ApiModelProperty(value = "链接")
    private String href;

    @ApiModelProperty(value = "图标")
    private String icon;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @NotNull(message = "级别不能为空")
    @ApiModelProperty(value = "级别：1菜单2页面3按钮")
    private Integer level;

    @NotNull(message = "标识不能为空")
    @ApiModelProperty(value = "权限标识")
    private String permission;

    @ApiModelProperty(value = "创建人")
    private Integer createBy;

    @ApiModelProperty(value = "创建时间")
    private Timestamp createTime;

    @ApiModelProperty(value = "更新人")
    private Integer updateBy;

    @ApiModelProperty(value = "更新时间")
    private Timestamp updateTime;

    @ApiModelProperty(value = "删除标识：0正常1删除")
    private Integer delFlag;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "子菜单")
    private List<MenuDetailVO> childMenus;

}
