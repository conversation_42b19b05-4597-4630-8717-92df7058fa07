package com.ssb.approve.model;

import lombok.Data;

import java.math.BigDecimal;
import java.sql.Timestamp;

/**
 * @ClassName: FollowUpModel
 * @Description: 跟进 model
 * @Author: YZK
 * @Date: 2019年12月19日20:19:40
 **/
@Data
public class FollowUpModel {

    private Integer id;

    private Integer userId;//用户id

    private Integer enterpriseId;   //企业id

    private Integer contactId;  //求职用户id

    private Integer followUpStatus; // 0 有意向 1 约面

    private Integer projectId;  //项目id

    private Timestamp behaviorTime;   //行为时间

    private Timestamp reviewTime; // 审核时间

    private String remark;  //备注

    private String failure; //失败原因

    private Integer appointmentStatus;// 0 预约面试  1 面试失败

    private Integer interviewStatus;// 1未参加 2 改约时间 3 未通过 4 下轮面试 5 发offer

    private Integer entryStatus; // -1 预约入职 1 已入职 2 更改入职时间 3 拒绝入职

    private String refuse; // 拒绝原因

    private Integer settleStatus; // 1 结算 2 离职

    private BigDecimal performance; //业绩

    private BigDecimal payment; //扣费

    private Integer settleId; //结算明细id

    private Integer tmpStatus; //临时状态0 默认 1 结算  2 离职

    private Timestamp leaveTime; //离职时间

    private String leaveCause; //离职原因

    private Timestamp actualSettleTime; // 实际结算时间

    private Integer delay; // 是否顺延  0/是 1/否

    private Integer belongEnterpriseId;   //企业id


}
