package com.ssb.approve.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.sql.Timestamp;

/**
 * @Author: WangShiyong
 * @Date: 2021/9/9 11:51 AM
 * @Description:app简历跟进列表筛选项
 */
@Data
public class QueryAppResumeFollowUpList {

    @ApiModelProperty(value = "企业id", hidden = true)
    private Integer eid;

    @ApiModelProperty(value = "app一级职位")
    private Integer appPositionFirst;

    @ApiModelProperty(value = "app二级职位")
    private Integer appPositionSecond;

    @ApiModelProperty(value = "crm一级职位")
    private Integer crmPositionZeroth;

    @ApiModelProperty(value = "crm二级职位")
    private Integer crmPositionFirst;

    @ApiModelProperty(value = "crm三级职位")
    private Integer crmPositionSecond;

    @ApiModelProperty(value = "期望地点-省份")
    private Integer province;

    @ApiModelProperty(value = "期望地点-市")
    private Integer city;

    @ApiModelProperty(value = "期望地点-区")
    private Integer district;

    @ApiModelProperty(value = "跟进状态  0/待跟进  1/已跟进")
    private Integer followStatus;

    @ApiModelProperty(value = "转化结果  0/未转化  1/已转化")
    private Integer changeResult;

    @ApiModelProperty(value = "跟进负责人")
    private String followUpPersonInCharge;

    @ApiModelProperty(value = "当前跟进人")
    private String followUp;

    @ApiModelProperty(value = "跟进间隔 0/半小时内  1/半小时到一小时  2/一到两小时  3/大于两小时")
    private Integer firstFollowInterval;

    @ApiModelProperty(value = "入库开始时间")
    private Timestamp beginCreateTime;

    @ApiModelProperty(value = "入库结束时间")
    private Timestamp endCreateTime;

    @ApiModelProperty(value = "交付状态 0/有意向 1/待面试 2/面试未通过 3/未到面 4/发offer 5/拒绝入职 6/待入职 7/待结算 8/已结算 9/离职")
    private Integer followResult;

    @ApiModelProperty(value = "每页多少数据")
    private Integer pageSize = 10;

    @ApiModelProperty(value = "页码")
    private Integer pageNum;

    @ApiModelProperty(value = "起始数量")
    private Integer pageBegin;

    @ApiModelProperty(value = "查询条件 0/未跟进  1/跟进未转化/半小时内  2/跟进已转化/半小时到一小时  3/一小时到两小时  4/大于两小时 ")
    private Integer type;

}
