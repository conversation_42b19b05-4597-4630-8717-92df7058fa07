package com.ssb.approve.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.sql.Timestamp;
import java.util.List;

/**
 * @Description: 核对单
 * @Author: zhangzeng
 * @date: 2021/7/27
 */
@Data
public class CheckSlipModel {
    private Integer pageSize = 10;//每页多少数据

    private Integer pageNum;//页码

    private Integer pageBegin;//起始数量

    @JsonFormat(pattern = "yyyy-MM-dd")
    private Timestamp beginTime; //开始时间

    @JsonFormat(pattern = "yyyy-MM-dd")
    private Timestamp endTime; //结束时间

    private String search; //搜索内容

    private Integer userId;//登录用户id

    private Integer enterpriseId;//登录用户所属公司

    private Integer eid; //客户公司

    /**
     *企业合作状态  0/未合作  1/合作中  2/合作终止 不传查全部
     */
    private Integer enterpriseStatus;

    /**
     * 项目状态   0进行中  1已暂停  2已结束 不传查全部
     */
    private Integer projectStatus;

    /**
     * 范围  0/全量 1/个人
     */
    private Integer range;

    /**
     * 个人及下属id
     */
    private List<Integer> userIds;

    /**
     * 付款类型 1/预付款  2/后付款
     */
    private Integer paymentType;

    /**
     * 是否欠款 1/是 2/否
     */
    private Integer arrearsStatus;
}
