package com.ssb.approve.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.sql.Timestamp;

/**
 * @Author: WangShiyong
 * @Date: 2021/10/15 2:09 PM
 * @Description:
 */
@Data
public class QueryFollowRevokeModel {

    @ApiModelProperty(value = "每页多少数据")
    private Integer pageSize = 10;

    @ApiModelProperty(value = "页码")
    private Integer pageNum;

    @ApiModelProperty(value = "起始数量")
    private Integer pageBegin;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Timestamp beginTime; //开始时间

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Timestamp endTime; //结束时间

    @ApiModelProperty(value = "项目标题")
    private String title;

    @ApiModelProperty(value = "筛选项  contactPhone/手机  contactName/姓名")
    private String searchType;

    @ApiModelProperty(value = "筛选值")
    private String search;

    @ApiModelProperty(value = "企业id")
    private Integer enterpriseId;

    @ApiModelProperty(value = "用户id")
    private Integer userId;

    @ApiModelProperty(value = "范围  0/全量 1/个人")
    private Integer range;

}
