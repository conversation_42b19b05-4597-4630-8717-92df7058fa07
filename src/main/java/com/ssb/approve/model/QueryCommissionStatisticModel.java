package com.ssb.approve.model;

import lombok.Data;

import java.sql.Timestamp;
import java.util.List;
import java.util.Set;

/**
 * @Author: WangShiyong
 * @Date: 2021/5/6 9:23 AM
 * @Description:提成报表model
 */
@Data
public class QueryCommissionStatisticModel {

    //0 权限：部门角色是成员的，只能查看提成人是自己的记录。
    //1 部门角色是管理员的，可以查看权限比自己低的管理员和成员以及下属团队所有人员的记录。
    //2 账号角色是管理员、人事、财务，可以查看全量数据。
    private Integer userType;

    private String title;//项目名称

    private Timestamp commissionStartTime;//提成时间-开始

    private Timestamp commissionEndTime;//提成时间-结束

    private Timestamp contractStartTime;//合同时间-开始

    private Timestamp contractEndTime;//合同时间-结束

    private String followUser;//跟进销售

    private String signingUser;//签单销售

    private String jobSeeker;//求职者

    private String phone;//求职者电话

    private Integer commissionUserId;//提成人id

    private Integer deptId;//团队id（筛选项）

    private List<Integer> deptIds;

    private Integer enterpriseId;//企业id

    private Integer userId;//用户id

    private Integer pageSize = 10;//每页多少数据

    private Integer specialPageSize = 5;//每页多少数据

    private Integer pageNum;//页码

    private Integer pageBegin;//起始数量

    private Set<Integer> userIds;//用户及下级id

    private Integer type;//0/查询  1/导出   区分是否分页

    private Timestamp lastFollowStartTime;//最后跟进时间-开始

    private Timestamp lastFollowEndTime;//最后跟进时间-结束

    private Integer followStatus;//跟进状态  -1意象 0 约面面试 1 约面失败  2 未参加  3 改约时间  4 未通过 5 下一轮面试  6 发offer  7 已入职  8 更改入职时间  9 拒绝入职  10 结算  11 离职

    private String cityName;//城市名（提成策略标题）
}
