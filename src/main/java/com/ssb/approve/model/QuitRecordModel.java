package com.ssb.approve.model;

import lombok.Data;

import java.sql.Timestamp;
import java.util.List;

/**
 * @ClassName: QuitRecordModel
 * @Description: 离职记录 model
 * @Author: YZK
 * @Date: 2020年02月21日17:34:13
 **/
@Data
public class QuitRecordModel {

    private Integer id; //离职记录id

    private Integer fromUserId; // 离职人

    private Integer enterpriseId; //公司id

    private Integer toUserId; //对接人id

    private List<Integer> followIds; //原 跟进id

    private Timestamp updateTime; //更新时间

}
