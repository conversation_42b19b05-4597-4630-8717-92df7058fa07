package com.ssb.approve.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.sql.Timestamp;

/**
 * @Author: WangShiyong
 * @Date: 2021/6/24 2:28 PM
 * @Description:行业岗位关联
 */
@Data
public class PositionIndustryModel {

    @ApiModelProperty(value = "项目id 编辑时使用")
    private Integer id;
    @ApiModelProperty(value = "岗位id")
    private Integer positionId;
    @ApiModelProperty(value = "行业id")
    private Integer industryId;
    @ApiModelProperty(value = "一级岗位id")
    private Integer positionFirstId;
    @ApiModelProperty(value = "一级行业id")
    private Integer industryFirstId;

    @ApiModelProperty(value = "提成策略id")
    private Integer strategyId;
    @ApiModelProperty(value = "更新人", hidden = true)
    private int updateBy;
    @ApiModelProperty(value = "更新时间", hidden = true)
    private Timestamp updateTime;
    @ApiModelProperty(value = "删除标识 0 正常  1 删除", hidden = true)
    private int delFlag;
}
