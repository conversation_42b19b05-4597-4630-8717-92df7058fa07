package com.ssb.approve.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.models.auth.In;
import lombok.Data;

import java.sql.Timestamp;
import java.util.List;
import java.util.Set;

/**
 * @ClassName: QueryFollowUpModel
 * @Description: 查询跟进 model
 * @Author: YZK
 * @Date: 2019年12月20日17:15:57
 **/
@Data
public class QueryFollowUpModel {

    private Integer id; //数据id

    private Integer userId;//登录用户id

    private Integer enterpriseId;//登录用户所属公司

    private Integer pageSize = 10;//每页多少数据

    private Integer pageNum;//页码

    private Integer pageBegin;//起始数量

    private Integer status;//状态 0 待约面 1 已约待面试 2 通过待入职 3 入职待结算

    private String contactName;//求职用户名称

    private String contactPhone;//求职用户手机号

    private String title;//项目标题

    private Integer createBy;//跟进人id
    private String createName;//跟进人名字

    private Set<Integer> createBySet;   //所有相关跟进人

    private Integer finish; //完成状态 0 进行中 1 完成

    private Integer authStatus;// 0 默认 1 审核  (不限 不用这个参数 或者 -1)

    private Integer interviewStatus; // 1 待面试 2 未通过  3 未参加  4 已通过

    private Integer entryStatus; // 1 已入职 2 拒绝入职  3 待入职

    private Integer settleStatus; // 1 待结算 2 已结算

    private Integer inState;//1 待入职、2拒绝入职、3已入职

    private String searchType; //搜索类型

    private String search; //搜索内容

    private Integer projectId;  //项目id

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Timestamp beginTime; //开始时间

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Timestamp endTime; //结束时间

    private Integer queryType; //查询方式  0 默认查询（按照当前人以及下属的 负责的项目  ）  1 指定查询 指定跟进人查询

    // 项目详情  跟进详情 使用 --
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Timestamp beginInterviewTime; //面试开始时间

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Timestamp endInterviewTime;   //面试结束时间

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Timestamp beginEntryTime;     //入职开始时间

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Timestamp endEntryTime;       //入职结束时间

    private Integer dataType;             //0 邀约   1 到面  2offer  3 入职 4 结算

    private String sortFiled;             //排序字段

    private Integer sortType;             //排序  0 正序  1 倒序

    // 项目详情  跟进详情 使用 --

    // 团队跟进数据  开始、

    private Set<Integer> deptIds;            // 部门 id 集合

    // 团队跟进数据  结束

    private String userName;//求职用户名称

    private String phone;//求职用户手机号

    private Integer currentStatus;//当前状态  状态 -1意向 0 约面面试 1 约面失败  2 未参加  3 改约时间  4 未通过 5 下一轮面试  6 发offer  7 已入职  8 更改入职时间  9 拒绝入职  10 结算  11 离职 12 中断

    private Integer interviewResult; // 面试结果（数据统计-个人数据使用）0/不限  1/待面试  2/未参加  3/未通过  4/已通过

    private String settleTimeType;// 查询类型 actualSettleTime/实际结算时间 settleTime/预计结算时间

    private String followUpUser;//跟进人

    private Integer authType; //审核类型 0猎头端审核 1企业端审核

    private String fullName;//公司名称

    /**
     * 用户id集合
     */
    private List<Integer> userIdList;

    /**
     * 1/个人 2/团队
     */
    private Integer type;

    /**
     * 部门id
     */
    private Integer deptId;

    /**
     * 全部空
     * 员工状态 0 正常  1 离职 2 冻结
     */
    private Integer userStatus;

    private Integer timeType;
}
