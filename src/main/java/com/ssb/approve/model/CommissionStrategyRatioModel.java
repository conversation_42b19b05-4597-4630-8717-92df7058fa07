package com.ssb.approve.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: WangShiyong
 * @Date: 2020/12/23 10:47 AM
 * @Description:策略提成比例
 */
@Data
public class CommissionStrategyRatioModel {

    @ApiModelProperty(value = "id")
    private Integer id;
    @ApiModelProperty(value = "策略id")
    private Integer strategyId;
    @ApiModelProperty(value = "提成类型  0/招聘提成   1/销售提成  2/领导提成")
    private Integer type;
    @ApiModelProperty(value = "提成级别id")
    private Integer commissionLevelId;
    @ApiModelProperty(value = "提成比例-高")
    private String high;
    @ApiModelProperty(value = "提成比例-中高")
    private String baseHigh;
    @ApiModelProperty(value = "提成比例-基础")
    private String base;
    @ApiModelProperty(value = "提成比例-中低")
    private String baseLow;
    @ApiModelProperty(value = "提成比例-低")
    private String low;
    @ApiModelProperty(value = "操作人")
    private Integer userId;

}
