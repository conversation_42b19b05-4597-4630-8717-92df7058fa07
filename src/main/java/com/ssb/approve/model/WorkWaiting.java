package com.ssb.approve.model;

import lombok.Data;

import java.sql.Timestamp;

/**
 * @Description: 销售工作台
 * @Author: zhangzeng
 * @date: $
 */
@Data
public class WorkWaiting {

    private Integer id; //id

    private Integer enterpriseId; //企业id

    private Integer followId; //跟进id

    private String enterpriseName;//企业名称

    private String contactName; //求职者

    private String title;//项目名称

    private Timestamp time;//时间

    private String matter;//事项

    public String getEnterpriseName() {
        if (enterpriseName == null || enterpriseName == ""){
            return enterpriseName = "-";
        }else {
            return enterpriseName;
        }
    }



}
