package com.ssb.approve.common.constants;

import com.ssb.approve.utils.MapUtils;

import java.util.Map;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>g
 * @Date: 2021/10/21 5:43 PM
 * @Description:
 */
public class InterviewConstants {

    private static Map<Integer, String> constants = null;

    static {
        constants = MapUtils.create(
                0, "默认",
                1, "未参加",
                2, "改约时间",
                3, "未通过",
                4, "下一轮面试",
                5, "发offer"
        );
    }

    public static String name(Integer id) {
        return constants.get(id);
    }
}
