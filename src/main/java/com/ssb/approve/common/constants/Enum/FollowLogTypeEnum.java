package com.ssb.approve.common.constants.Enum;

public enum FollowLogTypeEnum {

    SCHEDULE_AN_INTERVIEW(0,"待约面"),
    WAITING_FOR_INTERVIEW(1,"已约待面试"),
    WAITING_FOR_ONBOARDING(2,"通过入职"),
    WAITING_FOR_SETTLEMENT(3,"入职待结算"),
    SETTLEMENT(4,"结算"),
    REVOKE(5,"撤销"),
    INTERRUPTED(6,"已中断")
    ;

    private Integer code;
    private String value;
    FollowLogTypeEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public Integer getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }
}
