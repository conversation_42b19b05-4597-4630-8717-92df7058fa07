package com.ssb.approve.common.constants.Enum;

public enum EntryRecordEnum {

    DEFAULT(0, "默认"),
    ENTRY(1, "已入职"),
    CHANGE_ENTRY_TIME(2, "更改入职时间"),
    REFUSE_ENTRY(3, "拒绝入职"),
    INTERRUPTED(4, "已中断")
    ;

    private Integer code;
    private String value;

    EntryRecordEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public Integer getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }
}
