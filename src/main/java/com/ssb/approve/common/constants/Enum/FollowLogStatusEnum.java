package com.ssb.approve.common.constants.Enum;

public enum FollowLogStatusEnum {

    INTENTION(-1,"意向"),
    INTERVIEW_SCHEDULED(0,"约面面试"),
    INTERVIEW_FAILED(1,"约面失败"),
    DID_NOT_ATTEND(2,"未参加"),
    RESCHEDULED(3,"改约时间"),
    NOT_PASSED(4,"未通过"),
    NEXT_ROUND_INTERVIEW(5,"下一轮面试"),
    OFFER_MADE(6,"发offer"),
    JOINED(7,"已入职"),
    CHANGE_START_DATE(8,"更改入职时间"),
    REJECTED(9,"拒绝入职"),
    SETTLEMENT(10,"结算"),
    RESIGNED(11,"离职"),
    TEMPORARY_SETTLEMENT(12,"临时结算"),
    TEMPORARY_RESIGNATION(13,"临时离职"),
    REVOKE_INTERVIEW(14,"撤销面试"),
    REVOKE_JOINED(15,"撤销入职"),
    REVOKE_SETTLEMENT(16,"撤销结算"),
    INTERRUPTED(17,"已中断")
    ;

    private Integer code;
    private String value;
    FollowLogStatusEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public Integer getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }
}
