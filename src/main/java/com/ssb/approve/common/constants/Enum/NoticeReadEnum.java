package com.ssb.approve.common.constants.Enum;

public enum NoticeReadEnum {
    UNREAD(0, "未读"),
    READ(1, "已读");

    private final int value;
    private final String description;

    NoticeReadEnum(int value, String description) {
        this.value = value;
        this.description = description;
    }

    public int getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }

    public static NoticeReadEnum fromValue(int value) {
        for (NoticeReadEnum read : NoticeReadEnum.values()) {
            if (read.getValue() == value) {
                return read;
            }
        }
        throw new IllegalArgumentException("Invalid read value: " + value);
    }
}
