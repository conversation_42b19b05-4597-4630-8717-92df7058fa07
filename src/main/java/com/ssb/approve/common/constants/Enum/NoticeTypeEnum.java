package com.ssb.approve.common.constants.Enum;

public enum NoticeTypeEnum {

    PLATFORM_ENTERPRISE_REVIEW(1, "平台端-审核-企业审核"),
    HEADHUNTER_PROJECT_SQUARE_NEW_PROJECT_REMINDER(2, "猎头端-项目广场-新项目提醒"),
    HEADHUNTER_PROJECT_PUBLIC_INTERNAL_PROJECT_NEW(3, "猎头端-项目公海-内部项目上新"),
    HEADHUNTER_RESUME_DELIVERY_PLATFORM_SELECTED_PROJECT(4, "猎头端-简历投递-平台优选项目简历投递"),
    HEADHUNTER_REVIEW_INTERVIEW_RESULT_CONFIRM(5, "猎头端-审核-面试结果确认"),
    HEADHUNTER_REVIEW_EMPLOYMENT_RESULT_CONFIRM(6, "猎头端-审核-入职结果确认"),
    HEADHUNTER_REVIEW_SETTLEMENT_RESULT_CONFIRM(7, "猎头端-审核-结算结果确认（预付款、后付款）"),
    HEADHUNTER_RECRUITMENT_FOLLOW_UP_CANDIDATE_MISSED_INTERVIEW(8, "猎头端-招聘跟进-候选人面试未参加"),
    HEADHUNTER_RECRUITMENT_FOLLOW_UP_CANDIDATE_RESCHEDULED_INTERVIEW(9, "猎头端-招聘跟进-候选人面试已改约时间"),
    HEADHUNTER_RECRUITMENT_FOLLOW_UP_CANDIDATE_INTERVIEW_FAILED(10, "猎头端-招聘跟进-候选人面试未通过"),
    HEADHUNTER_RECRUITMENT_FOLLOW_UP_CANDIDATE_PASSED_FIRST_ROUND(11, "猎头端-招聘跟进-候选人已通过一面"),
    HEADHUNTER_RECRUITMENT_FOLLOW_UP_CANDIDATE_PASSED_INTERVIEW(12, "猎头端-招聘跟进-候选人面试通过"),
    HEADHUNTER_RECRUITMENT_FOLLOW_UP_CANDIDATE_CONFIRMED_ENTRY(13, "猎头端-招聘跟进-候选人已确认入职"),
    HEADHUNTER_RECRUITMENT_FOLLOW_UP_SETTLEMENT_CONFIRMATION_COMPLETE(14, "猎头端-招聘跟进-结算确认完成"),
    HEADHUNTER_RECRUITMENT_FOLLOW_UP_CANDIDATE_REJECTED_ENTRY(15, "猎头端-招聘跟进-候选人已拒绝入职"),
    HEADHUNTER_RECRUITMENT_FOLLOW_UP_CANDIDATE_CONFIRMED_LEAVING(16, "猎头端-招聘跟进-候选人已确认离职"),
    RECRUITMENT_FOLLOW_UP_INTERVIEW_REMINDER(17, "招聘跟进-面试跟进提醒"),
    RECRUITMENT_FOLLOW_UP_APPOINTMENT_ENTRY_REMINDER(18, "招聘跟进-预约入职跟进提醒"),
    RECRUITMENT_FOLLOW_UP_TIMED_INTERVIEW_REMINDER(19, "招聘跟进-定时面试跟进提醒"),
    RECRUITMENT_FOLLOW_UP_PROJECT_REMINDER_NO_FOLLOW_UP_3_DAYS(20, "招聘跟进-项目跟进提醒-无跟进-3天"),
    RECRUITMENT_FOLLOW_UP_PROJECT_REMINDER_NO_FOLLOW_UP_5_DAYS(21, "招聘跟进-项目跟进提醒-无跟进-5天"),
    RECRUITMENT_FOLLOW_UP_PROJECT_REMINDER_NO_FOLLOW_UP_7_DAYS(22, "招聘跟进-项目跟进提醒-无跟进-7天"),
    RECRUITMENT_FOLLOW_UP_PROJECT_REMINDER_WITH_FOLLOW_UP_4_DAYS(23, "招聘跟进-项目跟进提醒-有跟进-4天"),
    RECRUITMENT_FOLLOW_UP_PROJECT_REMINDER_WITH_FOLLOW_UP_6_DAYS(24, "招聘跟进-项目跟进提醒-有跟进-6天"),
    RECRUITMENT_FOLLOW_UP_PROJECT_REMINDER_WITH_FOLLOW_UP_7_DAYS(25, "招聘跟进-项目跟进提醒-有跟进-7天"),
    HEADHUNTER_CUSTOMER_MANAGEMENT_ACCOUNT_BALANCE_INSUFFICIENT(26, "猎头端-客户管理-账户余额不足提醒"),
    HEADHUNTER_CUSTOMER_MANAGEMENT_CONTRACT_EXPIRATION_REMINDER_5_DAYS(27, "猎头端-客户管理-合同到期提醒-5天"),
    HEADHUNTER_CUSTOMER_MANAGEMENT_CONTRACT_EXPIRATION_REMINDER_30_DAYS(28, "猎头端-客户管理-合同到期提醒-30天"),
    PROJECT_WITH_CUSTOMER_CONTRACT_ENTRY_REMINDER_7_DAYS(29, "项目与客户-录入合同提醒-7天"),
    PROJECT_WITH_CUSTOMER_CONTRACT_ENTRY_REMINDER_31_DAYS(30, "项目与客户-录入合同提醒-30天"),
    VERSION_UPDATE_REMINDER(31, "版本更新-版本更新提示");

    private final int value;
    private final String description;

    // Constructor for the enum
    NoticeTypeEnum(int value, String description) {
        this.value = value;
        this.description = description;
    }

    // Getter for value
    public int getValue() {
        return value;
    }

    // Getter for description
    public String getDescription() {
        return description;
    }

    // Optional: Method to get enum by value
    public static NoticeTypeEnum fromValue(int value) {
        for (NoticeTypeEnum type : values()) {
            if (type.value == value) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unexpected value: " + value);
    }
}
