package com.ssb.approve.common.constants.Enum;

public enum InterviewRecordEnum {

    DEFAULT(0,"默认"),
    NOT_ATTENDED(1,"未参加"),
    RESCHEDULED_TIME(2,"改约时间"),
    NOT_PASSED(3,"未通过"),
    NEXT_ROUND_OF_INTERVIEW(4,"下一轮面试"),
    OFFER(5,"发offer"),
    INTERRUPTED(6,"已中断")
    ;

    private Integer code;
    private String value;
    InterviewRecordEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public Integer getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }
}
