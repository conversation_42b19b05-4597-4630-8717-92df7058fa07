package com.ssb.approve.common.constants.Enum;

public enum FollowUpStatusEnum {

    SCHEDULE_AN_INTERVIEW(0,"待约面"),
    WAITING_FOR_INTERVIEW(1,"已约待面试"),
    WAITING_FOR_ONBOARDING(2,"通过待入职"),
    WAITING_FOR_SETTLEMENT(3,"入职待结算"),
    INTERRUPTED(4,"已中断")
    ;

    private Integer code;
    private String value;
    FollowUpStatusEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public Integer getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }
}
