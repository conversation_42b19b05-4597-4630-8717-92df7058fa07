package com.ssb.approve.common.constants;

import com.ssb.approve.utils.MapUtils;

import java.util.Map;

/**
 * @Author: WangShiyong
 * @Date: 2021/9/15 13:58 PM
 * @Description:
 */
public class AppResumeFollowUpConstants {
    private static Map<Integer, String> constants = null;

    static {
        constants = MapUtils.create(
                -1,"有意向",
                0, "待面试",
                1, "约面失败",
                2, "未到面",
                3, "待面试",
                4, "面试未通过",
                5, "待面试",
                6, "发offer",
                7, "待结算",
                8, "待入职",
                9, "拒绝入职",
                10, "已结算",
                11, "离职"
        );
    }

    public static String name(Integer id) {
        return constants.get(id);
    }
}
