package com.ssb.approve.common.constants;

import com.ssb.approve.utils.MapUtils;

import java.util.Map;

/**
 * @Author: <PERSON><PERSON><PERSON>yong
 * @Date: 2021/5/7 4:45 PM
 * @Description:
 */
public class FollowUpConstants {
    private static Map<Integer, String> constants = null;

    static {
        constants = MapUtils.create(
                -1,"意向",
                0, "约面面试",
                1, "约面失败",
                2, "未参加",
                3, "改约时间",
                4, "未通过",
                5, "下一轮面试",
                6, "发offer",
                7, "已入职",
                8, "更改入职时间",
                9, "拒绝入职",
                10, "结算",
                11, "离职"
        );
    }

    public static String name(Integer id) {
        return constants.get(id);
    }
}
