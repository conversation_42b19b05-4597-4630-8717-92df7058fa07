package com.ssb.approve.common.constants.Enum;

import lombok.Getter;

public enum RechargeLogTransactionTypeEnum {
    RECHARGE(1, "充值/付款"),
    SETTLEMENT(2, "结算"),
    CANCEL_REFUND(3, "撤销退款"),
    RESIGNATION_DEDUCTION(4, "离职扣款"),
    OTHER(5, "其他")
    ;

    @Getter
    private Integer code;

    @Getter
    private String value;

    RechargeLogTransactionTypeEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }


    // 根据code获取value的方法
    public static String getValueByCode(Integer code) {
        for (RechargeLogTransactionTypeEnum enumValue : RechargeLogTransactionTypeEnum.values()) {
            if (enumValue.getCode().equals(code)) {
                return enumValue.getValue();
            }
        }
        return null;
    }

    // 根据value获取code的方法
    public static Integer getCodeByValue(String value) {
        for (RechargeLogTransactionTypeEnum enumValue : RechargeLogTransactionTypeEnum.values()) {
            if (enumValue.getValue().equals(value)) {
                return enumValue.getCode();
            }
        }
        return null;
    }
}
