package com.ssb.approve.common.constants.Enum;

public enum NoticeStatusEnum {
    WAIT_SENT(1, "待发送"),
    SENT(2, "已发送"),
    PENDING(3, "待处理"),
    PROCESSED(4, "已处理");

    private final int value;
    private final String description;

    NoticeStatusEnum(int value, String description) {
        this.value = value;
        this.description = description;
    }

    public int getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }

    public static NoticeStatusEnum fromValue(int value) {
        for (NoticeStatusEnum status : NoticeStatusEnum.values()) {
            if (status.getValue() == value) {
                return status;
            }
        }
        throw new IllegalArgumentException("Invalid status value: " + value);
    }
}
