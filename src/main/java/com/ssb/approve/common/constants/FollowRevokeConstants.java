package com.ssb.approve.common.constants;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021/10/18 5:27 PM
 * @Description: 撤销常量
 */
public class FollowRevokeConstants {

    // crm_follow_up 跟进状态
    public static final int FOLLOW_UP_INTENTION = -1;// -1意向
    public static final int FOLLOW_UP_APPOINTMENT_INTERVIEW = 0;// 0 约面面试
    public static final int FOLLOW_UP_INTERVIEW_FAIL = 1;// 1 约面失败
    public static final int FOLLOW_UP_UNCOMMITTED = 2;// 2 未参加
    public static final int FOLLOW_UP_CHANGE_TIME = 3;//  3 改约时间
    public static final int FOLLOW_UP_FAIL = 4;// 4 未通过
    public static final int FOLLOW_UP_NEXT_ROUND = 5;// 5 下一轮面试
    public static final int FOLLOW_UP_OFFER = 6;//  6 发offer
    public static final int FOLLOW_UP_EMPLOYED = 7;// 7 已入职
    public static final int FOLLOW_UP_CHANGE_EMPLOYED_TIME = 8;//  8 更改入职时间
    public static final int FOLLOW_UP_REFUSE_EMPLOYED = 9;//  9 拒绝入职
    public static final int FOLLOW_UP_SETTLE = 10;// 10 结算
    public static final int FOLLOW_UP_LEAVE = 11;//  11 离职

    // crm_interview_record
    public static final int INTERVIEW_RECORD_DEFAULT = 0;// 0 默认
    public static final int INTERVIEW_RECORD_UNCOMMITTED = 1;// 1 未参加
    public static final int INTERVIEW_RECORD_CHANGE_TIME = 2;// 2 改约时间
    public static final int INTERVIEW_RECORD_FAIL = 3;//  3 未通过
    public static final int INTERVIEW_RECORD_ROUND = 4;// 4 下一轮面试
    public static final int INTERVIEW_RECORD_OFFER = 5;// 5 发offer

    // crm_entry_record
    public static final int ENTRY_RECORD_DEFAULT = 0;// 0 默认
    public static final int ENTRY_RECORD_EMPLOYED = 1;// 1 已入职
    public static final int ENTRY_RECORD_CHANGE_EMPLOYED_TIME = 2;// 2 更改入职时间
    public static final int ENTRY_RECORD_REFUSE_EMPLOYED = 3;//  3 拒绝入职

    // crm_settle_details
    public static final int SETTLE_DETAILS_DEFAULT = 0;// 0 默认
    public static final int SETTLE_DETAILS_SETTLE = 1;// 1 结算
    public static final int SETTLE_DETAILS_LEAVE = 2;// 2 离职

    // crm_follow_log 状态
    public static final int FOLLOW_LOG_INTENTION = -1;// -1意向
    public static final int FOLLOW_LOG_APPOINTMENT_INTERVIEW = 0;// 0 约面面试
    public static final int FOLLOW_LOG_INTERVIEW_FAIL = 1;// 1 约面失败
    public static final int FOLLOW_LOG_UNCOMMITTED = 2;// 2 未参加
    public static final int FOLLOW_LOG_CHANGE_TIME = 3;//  3 改约时间
    public static final int FOLLOW_LOG_FAIL = 4;// 4 未通过
    public static final int FOLLOW_LOG_NEXT_ROUND = 5;// 5 下一轮面试
    public static final int FOLLOW_LOG_OFFER = 6;//  6 发offer
    public static final int FOLLOW_LOG_EMPLOYED = 7;// 7 已入职
    public static final int FOLLOW_LOG_CHANGE_EMPLOYED_TIME = 8;//  8 更改入职时间
    public static final int FOLLOW_LOG_REFUSE_EMPLOYED = 9;//  9 拒绝入职
    public static final int FOLLOW_LOG_SETTLE = 10;// 10 结算
    public static final int FOLLOW_LOG_LEAVE = 11;//  11 离职
    public static final int FOLLOW_LOG_REVOKE_INTERVIEW = 14;//  14 撤销面试
    public static final int FOLLOW_LOG_REVOKE_ENTRY = 15;// 15 撤销入职
    public static final int FOLLOW_LOG_REVOKE_SETTLE = 16;//  16 撤销结算
}
