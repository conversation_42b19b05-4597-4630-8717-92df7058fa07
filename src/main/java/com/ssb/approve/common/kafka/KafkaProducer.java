package com.ssb.approve.common.kafka;

import com.alibaba.fastjson.JSONObject;
import com.ssb.approve.common.constants.KafkaTopicConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.core.KafkaTemplate;

import java.util.Map;


@Configuration
public class KafkaProducer {

    @Autowired
    private KafkaTemplate<String, String> kafkaTemplate;

    public void syncUpdateProjectProgress(Integer id) {
        kafkaTemplate.send(KafkaTopicConstants.UPDATE_PROJECT_PROGRESS_TOPIC, id + "");
    }

    public void syncInterviewFromCRM(Map<String, Object> ssbInterviewMap){
        kafkaTemplate.send(KafkaTopicConstants.SYNC_INTERVIEW_FROM_CRM_TOPIC, JSONObject.toJSONString(ssbInterviewMap));
    }

    public void updateInterviewFromCRM(Map<String, Object> ssbInterviewMap){
        kafkaTemplate.send(KafkaTopicConstants.UPDATE_INTERVIEW_FROM_CRM_TOPIC, JSONObject.toJSONString(ssbInterviewMap));
    }

    public void updateInterviewTimeFromCRM(Map<String, Object> ssbInterviewMap){
        kafkaTemplate.send(KafkaTopicConstants.UPDATE_INTERVIEW_TIME_FROM_CRM_TOPIC, JSONObject.toJSONString(ssbInterviewMap));
    }

}
