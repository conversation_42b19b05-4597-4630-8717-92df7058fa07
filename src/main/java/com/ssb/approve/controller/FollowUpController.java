package com.ssb.approve.controller;

import com.ssb.approve.common.kafka.KafkaProducer;
import com.ssb.approve.entity.CrmFollowRecommend;
import com.ssb.approve.entity.FollowRevokeLog;
import com.ssb.approve.interceptor.annotation.RepeatSubmit;
import com.ssb.approve.model.FollowUpModel;
import com.ssb.approve.model.QueryAppResumeFollowUpList;
import com.ssb.approve.model.QueryFollowRevokeModel;
import com.ssb.approve.model.QueryFollowUpModel;
import com.ssb.approve.model.dto.FollowRecommendDTO;
import com.ssb.approve.model.dto.FollowRecommendListDTO;
import com.ssb.approve.service.*;
import com.ssb.approve.utils.MapUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.sql.Timestamp;
import java.util.Map;

/**
 * @ClassName: FollowUpController
 * @Description: 项目跟进审核 controller
 * @Author: YZK
 * @Date: 2019年12月19日20:04:42
 **/
@Api(tags = "招聘跟进API", value = "招聘跟进API")
@RestController
@RequestMapping("/api/followUp")
public class FollowUpController {

    @Autowired
    private HttpServletRequest request;

    @Autowired
    private FollowUpService followUpService;

    @Autowired
    private InterviewService interviewService;

    @Autowired
    private EntryRecordService entryRecordService;

    @Autowired
    private SettleDetailsService settleDetailsService;

    @Autowired
    private CommonService commonService;

    @Autowired
    private FollowRevokeService followRevokeService;

    @Autowired
    private KafkaProducer kafkaProducer;

    /**
     * 检测是否存在跟进 （正在进行中的）
     *
     * @param contactId     求职用户id
     * @param projectId     项目id
     * @return
     */
    @ApiOperation(value = "检测是否存在跟进 （正在进行中的）")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "contactId", value = "求职用户id", dataType = "int", required = true, paramType = "query"),
            @ApiImplicitParam(name = "projectId", value = "项目id", dataType = "int", required = true, paramType = "query")
    })
    @GetMapping("/checkFollowUp")
    public Object checkFollowUp(Integer contactId, Integer projectId) {
        if (contactId == null || projectId == null) {
            return MapUtils.create("no", 0, "msg", "参数异常");
        }
        return followUpService.checkFollowUp(contactId, projectId);
    }

    /**
     * 保存推荐
     * @param contactId 求职用户id
     * @param resumeId 简历id
     * @param projectId 项目id
     * @param remark 推荐语
     * @param customerEnterpriseId 客户的企业id
     * @return
     */
    @PostMapping("saveFollowRecommend")
    public Object saveFollowRecommend(@RequestBody FollowRecommendDTO dto){

        String userId = request.getHeader("user-id");
        String enterpriseId = request.getHeader("user-eid");
        if (userId == null || enterpriseId == null) {
            return MapUtils.create("no", 500, "msg", "非法登录，请确认后重试");
        }

        dto.setEnterpriseId(Integer.parseInt(enterpriseId));
        dto.setCreateBy(Integer.parseInt(userId));
        dto.setRecommendBy(Integer.parseInt(userId));

        if(dto.getContactId() == null || dto.getProjectId() == null){
            return MapUtils.create("no", 0, "msg", "参数异常");
        }

        if(dto.getRemark() != null && dto.getRemark().length() > 30){
            return MapUtils.create("no", 0, "msg", "参数异常");
        }

        return followUpService.saveFollowRecommend(dto);
    }

    /**
     * 推荐列表
     * @param jobSeekerKeyword 求职者姓名或手机号
     * @param projectName 项目名称
     * @param status 1/审核中 2/通过 3/未通过
     * @param timeType 时间类型 1/推荐时间 2/审核时间
     * @param startTime 开始日期
     * @param endTime 结束日期
     * @param pageNum 页码
     * @return
     */
    @PostMapping("/getFollowRecommendList")
    public Object getFollowRecommendList(@RequestBody FollowRecommendListDTO dto){
        String userId = request.getHeader("user-id");
        String enterpriseId = request.getHeader("user-eid");
        if (userId == null || enterpriseId == null) {
            return MapUtils.create("no", 500, "msg", "非法登录，请确认后重试");
        }

        dto.setEnterpriseId(Integer.parseInt(enterpriseId));
        dto.setUserId(Integer.parseInt(userId));

        if (dto.getPageNum() == null) {
            dto.setPageNum(1);
        }

        return followUpService.getFollowRecommendList(dto);
    }

    /**
     * 保存跟进
     *
     * @param followUpModel
     * @return
     */
    @ApiOperation(value = "保存跟进")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "contactId", value = "求职用户id", dataType = "int", required = true, paramType = "query"),
            @ApiImplicitParam(name = "projectId", value = "项目id", dataType = "int", required = true, paramType = "query"),
            @ApiImplicitParam(name = "followUpStatus", value = "0 有意向 1 约面", dataType = "int", required = true, paramType = "query"),
            @ApiImplicitParam(name = "behaviorTime", value = "行为时间", dataType = "date", required = false, paramType = "query"),
            @ApiImplicitParam(name = "remark", value = "备注", dataType = "date", required = false, paramType = "query")
    })
    @PostMapping("/save")
    public Object save(FollowUpModel followUpModel) {
        //用户id
        String userId = request.getHeader("user-id");
        String enterpriseId = request.getHeader("user-eid");
        if (userId == null || enterpriseId == null) {
            return MapUtils.create("no", 500, "msg", "非法登录，请确认后重试");
        }
        followUpModel.setUserId(Integer.parseInt(userId));
        followUpModel.setEnterpriseId(Integer.parseInt(enterpriseId));
        //校验参数
        if (followUpModel.getFollowUpStatus() == null || followUpModel.getProjectId() == null || followUpModel.getContactId() == null) {
            return MapUtils.create("no", 0, "msg", "参数异常");
        }
        //约面
        if (followUpModel.getFollowUpStatus() != null) {
            //非 意向、约面
            if (!(followUpModel.getFollowUpStatus() == 0 || followUpModel.getFollowUpStatus() == 1)) {
                return MapUtils.create("no", 0, "msg", "followUpStatus 参数异常");
            }
            //约面
            if (followUpModel.getFollowUpStatus() == 1) {
                if (followUpModel.getBehaviorTime() == null) {
                    return MapUtils.create("no", 0, "msg", "面试时间不能为空");
                }
            }
        }
        return followUpService.save(followUpModel);
    }

    /**
     * 获取我的意象列表 tab数量
     *
     * @return
     */
    @ApiOperation(value = "获取我的意象列表 tab数量")
    @ApiImplicitParams({
    })
    @GetMapping("/getFollowUpCount")
    public Object getFollowUpCount (QueryFollowUpModel queryFollowUpModel) {
        //用户id
        String userId = request.getHeader("user-id");
        String enterpriseId = request.getHeader("user-eid");
        if (userId == null || enterpriseId == null) {
            return MapUtils.create("no", 500, "msg", "非法登录，请确认后重试");
        }
        queryFollowUpModel.setUserId(Integer.parseInt(userId));
        queryFollowUpModel.setEnterpriseId(Integer.parseInt(enterpriseId));

        return followUpService.getImageCount(queryFollowUpModel);
    }

    /**
     * 获取审核数量
     *
     * @return
     */
    @ApiOperation(value = "获取审核数量")
    @ApiImplicitParams({
    })
    @GetMapping("/getAuthCount")
    public Object getAuthCount(QueryFollowUpModel queryFollowUpModel) {
        //用户id
        String userId = request.getHeader("user-id");
        String enterpriseId = request.getHeader("user-eid");
        if (userId == null || enterpriseId == null) {
            return MapUtils.create("no", 500, "msg", "非法登录，请确认后重试");
        }
        queryFollowUpModel.setUserId(Integer.parseInt(userId));
        queryFollowUpModel.setEnterpriseId(Integer.parseInt(enterpriseId));
        return followUpService.getAuthCount(queryFollowUpModel);
    }

    /**
     * 获取待约面
     *
     * @param queryFollowUpModel
     * @return
     */
    @ApiOperation(value = "获取待约面")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "求职用户id", dataType = "int", required = true, paramType = "query"),
            @ApiImplicitParam(name = "status", value = "状态 0 待约面 1 已约待面试 2 通过待入职 3 入职待结算", dataType = "int", required = true, paramType = "query"),
    })
    @GetMapping("/getAppointmentList")
    public Object getAppointmentList(QueryFollowUpModel queryFollowUpModel) {
        //用户id
        String userId = request.getHeader("user-id");
        String enterpriseId = request.getHeader("user-eid");
        if (userId == null || enterpriseId == null) {
            return MapUtils.create("no", 500, "msg", "非法登录，请确认后重试");
        }
        queryFollowUpModel.setUserId(Integer.parseInt(userId));
        queryFollowUpModel.setEnterpriseId(Integer.parseInt(enterpriseId));
        //默认第一页
        if (queryFollowUpModel.getPageNum() == null) {
            queryFollowUpModel.setPageNum(1);
        }
        return followUpService.getAppointmentList(queryFollowUpModel);
    }

    /**
     * 更新待约面状态
     *
     * @param followUpModel
     * @return
     */
    @ApiOperation(value = "更新待约面状态")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "招聘跟进id", dataType = "int", required = true, paramType = "query"),
            @ApiImplicitParam(name = "appointmentStatus", value = "0 预约面试  1 约面失败", dataType = "int", required = true, paramType = "query"),
            @ApiImplicitParam(name = "behaviorTime", value = "行为时间", dataType = "date", required = false, paramType = "query"),
            @ApiImplicitParam(name = "remark", value = "备注", dataType = "String", required = false, paramType = "query")
    })
    @PutMapping("/updateAppointment")
    public Object updateAppointment (FollowUpModel followUpModel) {
        //用户id
        String userId = request.getHeader("user-id");
        String enterpriseId = request.getHeader("user-eid");
        if (userId == null || enterpriseId == null) {
            return MapUtils.create("no", 500, "msg", "非法登录，请确认后重试");
        }
        followUpModel.setUserId(Integer.parseInt(userId));
        followUpModel.setEnterpriseId(Integer.parseInt(enterpriseId));
        //参数校验
        if (followUpModel.getId() == null || followUpModel.getAppointmentStatus() == null) {
            return MapUtils.create("no", 0, "msg", "参数异常");
        }
        if (!(followUpModel.getAppointmentStatus() == 0 || followUpModel.getAppointmentStatus() == 1)) {
            return MapUtils.create("no", 0, "msg", "appointmentStatus 参数异常");
        }

        return followUpService.updateAppointment(followUpModel);
    }

    /**
     * 获取已约待面试列表
     *
     * @param queryFollowUpModel
     * @return
     */
    @ApiOperation(value = "获取已约待面试列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页码", dataType = "int", required = true, paramType = "query"),
            @ApiImplicitParam(name = "projectId", value = "面试项目", dataType = "int", required = true, paramType = "query"),
            @ApiImplicitParam(name = "interviewStatus", value = "1 待面试 2 未通过  3 未参加  4 已通过（限制为只查看待面试）", dataType = "int", required = true, paramType = "query"),
            @ApiImplicitParam(name = "beginTime", value = "面试开始时间", dataType = "string", required = true, paramType = "query"),
            @ApiImplicitParam(name = "endTime", value = "面试结束时间", dataType = "string", required = true, paramType = "query"),
            @ApiImplicitParam(name = "contactName", value = "求职者名称", dataType = "string", required = false, paramType = "query")
    })
    @GetMapping("/getInterviewList")
    public Object getInterviewList(QueryFollowUpModel queryFollowUpModel) {
        //用户id
        String userId = request.getHeader("user-id");
        String enterpriseId = request.getHeader("user-eid");
        if (userId == null || enterpriseId == null) {
            return MapUtils.create("no", 500, "msg", "非法登录，请确认后重试");
        }
        queryFollowUpModel.setUserId(Integer.parseInt(userId));
        queryFollowUpModel.setEnterpriseId(Integer.parseInt(enterpriseId));
        if (queryFollowUpModel.getPageNum() == null || queryFollowUpModel.getPageNum() < 1) {
            queryFollowUpModel.setPageNum(1);
        }
        return interviewService.getInterviewList(queryFollowUpModel);
    }

    /**
     * 更新面试结果状态
     *
     * @param followUpModel
     * @return
     */
    @ApiOperation(value = "更新面试结果状态")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "面试记录", dataType = "int", required = true, paramType = "query"),
            @ApiImplicitParam(name = "interviewStatus", value = "面试状态 1未参加 2 改约时间 3 未通过 4 下轮面试 5 发offer", dataType = "int", required = true, paramType = "query"),
            @ApiImplicitParam(name = "behaviorTime", value = "面试时间", dataType = "date", required = false, paramType = "query"),
            @ApiImplicitParam(name = "remark", value = "备注", dataType = "string", required = false, paramType = "query"),
            @ApiImplicitParam(name = "failure", value = "失败原因", dataType = "string", required = false, paramType = "query")
    })
    @PutMapping("/updateInterview")
    public Object updateInterview(FollowUpModel followUpModel) {
        //用户id
        String userId = request.getHeader("user-id");
        String enterpriseId = request.getHeader("user-eid");
        if (userId == null || enterpriseId == null) {
            return MapUtils.create("no", 500, "msg", "非法登录，请确认后重试");
        }
        followUpModel.setUserId(Integer.parseInt(userId));
        followUpModel.setEnterpriseId(Integer.parseInt(enterpriseId));

        if (followUpModel.getId() == null) {
            return MapUtils.create("no", 0, "msg", "参数异常");
        }
        //状态判断
        if (!(followUpModel.getInterviewStatus() == 1 || followUpModel.getInterviewStatus() == 2 || followUpModel.getInterviewStatus() == 3
                || followUpModel.getInterviewStatus() == 4 || followUpModel.getInterviewStatus() == 5)) {
            return MapUtils.create("no", 0, "msg", "interviewStatus参数异常");
        }
        //更改时间
        if (followUpModel.getInterviewStatus() == 2) {
            if (followUpModel.getBehaviorTime() == null) {
                return MapUtils.create("no", 0, "msg", "behaviorTime参数异常");
            }
        }
        return interviewService.updateInterview(followUpModel);
    }

    /**
     * 获取面试确认列表
     *
     * @param queryFollowUpModel
     * @return
     */
    @ApiOperation(value = "获取面试确认列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "createBySet", value = "跟进人（2.1.1版本废弃）", dataType = "set", required = false, paramType = "query"),
            @ApiImplicitParam(name = "authStatus", value = " 审核状态 0 默认 1 审核  (不限 不用这个参数 或者 -1)", dataType = "int", required = true, paramType = "query"),
            @ApiImplicitParam(name = "interviewStatus", value = "面试状态 1 待面试 2 未通过  3 未参加  4 已通过", dataType = "int", required = false, paramType = "query"),
            @ApiImplicitParam(name = "searchType", value = "搜索类型  fullName (公司名称) title （标题）contactName (求职者名称) contactPhone (求职者手机号)", dataType = "string", required = false, paramType = "query"),
            @ApiImplicitParam(name = "search", value = "搜索内容", dataType = "string", required = false, paramType = "query")
    })
    @PostMapping("/getInterviewAuthList")
    public Object getInterviewAuthList(@RequestBody QueryFollowUpModel queryFollowUpModel) {
        //用户id
        String userId = request.getHeader("user-id");
        String enterpriseId = request.getHeader("user-eid");
        if (userId == null || enterpriseId == null) {
            return MapUtils.create("no", 500, "msg", "非法登录，请确认后重试");
        }
        queryFollowUpModel.setUserId(Integer.parseInt(userId));
        queryFollowUpModel.setEnterpriseId(Integer.parseInt(enterpriseId));
        if (queryFollowUpModel.getPageNum() == null || queryFollowUpModel.getPageNum() < 1) {
            queryFollowUpModel.setPageNum(1);
        }
        return interviewService.getInterviewAuthList(queryFollowUpModel);
    }

    /**
     * 面试确认 -- 审核操作
     *
     * @return
     */
    @ApiOperation(value = "面试确认 -- 审核操作")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "面试记录表id", dataType = "int", required = true, paramType = "query")
    })
    @PutMapping("/updateInterviewAuth")
    public Object updateInterviewAuth(Integer id) {
        String userId = request.getHeader("user-id");
        String enterpriseId = request.getHeader("user-eid");
        if (userId == null || enterpriseId == null) {
            return MapUtils.create("no", 500, "msg", "非法登录，请确认后重试");
        }
        //参数判断
        if (id == null) {
            return MapUtils.create("no", 0, "msg", "参数异常");
        }
        return interviewService.updateInterviewAuth(id, Integer.parseInt(userId));
    }


    /**
     * 面试审核跟进   （主管操作哦  跟进默认审核通过）
     *
     * @param followUpModel
     * @return
     */
    @ApiOperation(value = "面试审核跟进")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "面试记录", dataType = "int", required = true, paramType = "query"),
            @ApiImplicitParam(name = "interviewStatus", value = "面试状态 1未参加 2 改约时间 3 未通过 4 下轮面试 5 发offer", dataType = "int", required = true, paramType = "query"),
            @ApiImplicitParam(name = "behaviorTime", value = "面试时间", dataType = "date", required = false, paramType = "query"),
            @ApiImplicitParam(name = "remark", value = "备注", dataType = "string", required = false, paramType = "query"),
            @ApiImplicitParam(name = "failure", value = "失败原因", dataType = "string", required = false, paramType = "query")
    })
    @PutMapping("/interviewAuthFollowUp")
    public Object interviewAuthFollowUp(FollowUpModel followUpModel) {
        //用户id
        String userId = request.getHeader("user-id");
        String enterpriseId = request.getHeader("user-eid");
        if (userId == null || enterpriseId == null) {
            return MapUtils.create("no", 500, "msg", "非法登录，请确认后重试");
        }
        followUpModel.setUserId(Integer.parseInt(userId));
        followUpModel.setEnterpriseId(Integer.parseInt(enterpriseId));

        if (followUpModel.getId() == null) {
            return MapUtils.create("no", 0, "msg", "参数异常");
        }
        //状态判断
        if (!(followUpModel.getInterviewStatus() == 1 || followUpModel.getInterviewStatus() == 2 || followUpModel.getInterviewStatus() == 3
                || followUpModel.getInterviewStatus() == 4 || followUpModel.getInterviewStatus() == 5)) {
            return MapUtils.create("no", 0, "msg", "interviewStatus参数异常");
        }
        //更改时间、下一轮面试
        if (followUpModel.getInterviewStatus() == 2) {
            if (followUpModel.getBehaviorTime() == null) {
                return MapUtils.create("no", 0, "msg", "behaviorTime参数异常");
            }
        }

        return interviewService.interviewAuthFollowUp(followUpModel);
    }

    @ApiOperation(value = "获取发offer列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页码", dataType = "int", required = true, paramType = "query")
    })
    @GetMapping("/getOnlyOfferList")
    public Object getOnlyOfferList(QueryFollowUpModel queryFollowUpModel) {
        //用户id
        String userId = request.getHeader("user-id");
        String enterpriseId = request.getHeader("user-eid");
        if (userId == null || enterpriseId == null) {
            return MapUtils.create("no", 500, "msg", "非法登录，请确认后重试");
        }
        queryFollowUpModel.setUserId(Integer.parseInt(userId));
        queryFollowUpModel.setEnterpriseId(Integer.parseInt(enterpriseId));
        if (queryFollowUpModel.getPageNum() == null || queryFollowUpModel.getPageNum() < 1) {
            queryFollowUpModel.setPageNum(1);
        }
        return entryRecordService.getOnlyOfferList(queryFollowUpModel);
    }

    @ApiOperation(value = "更新 发offer 操作")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "入职记录id", dataType = "int", required = true, paramType = "query"),
            @ApiImplicitParam(name = "entryStatus", value = "入职状态 0 预约入职 1 已入职 2 更改入职时间 3 拒绝入职", dataType = "int", required = true, paramType = "query"),
            @ApiImplicitParam(name = "behaviorTime", value = "面试时间", dataType = "date", required = false, paramType = "query"),
            @ApiImplicitParam(name = "remark", value = "备注", dataType = "string", required = false, paramType = "query"),
            @ApiImplicitParam(name = "refuse", value = "拒绝原因", dataType = "string", required = false, paramType = "query")
    })
    @PutMapping("/updateOnlyOffer")
    public Object updateOnlyOffer(FollowUpModel followUpModel) {
        //用户id
        String userId = request.getHeader("user-id");
        String enterpriseId = request.getHeader("user-eid");
        if (userId == null || enterpriseId == null) {
            return MapUtils.create("no", 500, "msg", "非法登录，请确认后重试");
        }
        followUpModel.setUserId(Integer.parseInt(userId));
        followUpModel.setEnterpriseId(Integer.parseInt(enterpriseId));

        if (followUpModel.getId() == null) {
            return MapUtils.create("no", 0, "msg", "参数异常");
        }
        //状态判断
        if (!(followUpModel.getEntryStatus() == 0 || followUpModel.getEntryStatus() == 3)) {
            return MapUtils.create("no", 0, "msg", "entryStatus参数异常");
        }
        //预约入职
        if (followUpModel.getEntryStatus() == 0) {
            //校验时间不能为空
            if (followUpModel.getBehaviorTime() == null) {
                return MapUtils.create("no", 0, "msg", "behaviorTime异常");
            }
        }
        //拒绝入职
        if (followUpModel.getEntryStatus() == 3) {
            //空数据
            if (StringUtils.isBlank(followUpModel.getRefuse())) {
                return MapUtils.create("no", 0, "msg", "refuse参数异常");
            }
        }
        return entryRecordService.updateOnlyOffer(followUpModel);
    }

    /**
     * 获取待入职列表
     *
     * @param queryFollowUpModel
     * @return
     */
    @ApiOperation(value = "获取待入职列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页码", dataType = "int", required = true, paramType = "query"),
            @ApiImplicitParam(name = "projectId", value = "入职项目", dataType = "int", required = true, paramType = "query"),
            @ApiImplicitParam(name = "entryStatus", value = "入职类型  1 已入职 2 拒绝入职  3 待入职(只显示待入职)", dataType = "int", required = true, paramType = "query"),
            @ApiImplicitParam(name = "beginTime", value = "入职开始时间", dataType = "string", required = true, paramType = "query"),
            @ApiImplicitParam(name = "endTime", value = "入职结束时间", dataType = "string", required = true, paramType = "query"),
            @ApiImplicitParam(name = "contactName", value = "求职者名称", dataType = "string", required = false, paramType = "query")
    })
    @GetMapping("/getEntryRecordList")
    public Object getEntryRecordList(QueryFollowUpModel queryFollowUpModel) {
        //用户id
        String userId = request.getHeader("user-id");
        String enterpriseId = request.getHeader("user-eid");
        if (userId == null || enterpriseId == null) {
            return MapUtils.create("no", 500, "msg", "非法登录，请确认后重试");
        }
        queryFollowUpModel.setUserId(Integer.parseInt(userId));
        queryFollowUpModel.setEnterpriseId(Integer.parseInt(enterpriseId));
        if (queryFollowUpModel.getPageNum() == null || queryFollowUpModel.getPageNum() < 1) {
            queryFollowUpModel.setPageNum(1);
        }
        return entryRecordService.getEntryRecordList(queryFollowUpModel);
    }

    /**
     * 更新入职结果
     *
     * @param followUpModel
     * @return
     */
    @ApiOperation(value = "更新入职结果")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "入职记录id", dataType = "int", required = true, paramType = "query"),
            @ApiImplicitParam(name = "entryStatus", value = "入职状态  1 已入职 2 更改入职时间 3 拒绝入职", dataType = "int", required = true, paramType = "query"),
            @ApiImplicitParam(name = "behaviorTime", value = "面试时间", dataType = "date", required = false, paramType = "query"),
            @ApiImplicitParam(name = "remark", value = "备注", dataType = "string", required = false, paramType = "query"),
            @ApiImplicitParam(name = "refuse", value = "拒绝原因", dataType = "string", required = false, paramType = "query")
    })
    @PutMapping("/updateEntryRecord")
    public Object updateEntryRecord(FollowUpModel followUpModel) {
        //用户id
        String userId = request.getHeader("user-id");
        String enterpriseId = request.getHeader("user-eid");
        if (userId == null || enterpriseId == null) {
            return MapUtils.create("no", 500, "msg", "非法登录，请确认后重试");
        }
        followUpModel.setUserId(Integer.parseInt(userId));
        followUpModel.setEnterpriseId(Integer.parseInt(enterpriseId));

        if (followUpModel.getId() == null) {
            return MapUtils.create("no", 0, "msg", "参数异常");
        }
        //状态判断
        if (!(followUpModel.getEntryStatus() == 1 || followUpModel.getEntryStatus() == 2 || followUpModel.getEntryStatus() == 3)) {
            return MapUtils.create("no", 0, "msg", "entryStatus参数异常");
        }
        //更改时间、下一轮面试
        if (followUpModel.getEntryStatus() == 1 || followUpModel.getEntryStatus() == 2) {
            if (followUpModel.getBehaviorTime() == null) {
                return MapUtils.create("no", 0, "msg", "behaviorTime参数异常");
            }
        }
        //拒绝入职
        if (followUpModel.getEntryStatus() == 3) {
            //空数据
            if (StringUtils.isBlank(followUpModel.getRefuse())) {
                return MapUtils.create("no", 0, "msg", "refuse参数异常");
            }
        }
        return entryRecordService.updateEntryRecord(followUpModel);
    }

    /**
     * 获取入职确认列表
     *
     * @param queryFollowUpModel
     * @return
     */
    @ApiOperation(value = "获取入职确认列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "createBySet", value = "跟进人", dataType = "set", required = false, paramType = "query"),
            @ApiImplicitParam(name = "authStatus", value = " 审核状态 0 默认 1 审核  (不限 不用这个参数 或者 -1)", dataType = "int", required = true, paramType = "query"),
            @ApiImplicitParam(name = "entryStatus", value = "1 已入职 2 拒绝入职  3 待入职", dataType = "int", required = false, paramType = "query"),
            @ApiImplicitParam(name = "searchType", value = "搜索类型  fullName (公司名称) title （标题）contactName (求职者名称) contactPhone (求职者手机号) ", dataType = "string", required = false, paramType = "query"),
            @ApiImplicitParam(name = "search", value = "搜索内容", dataType = "string", required = false, paramType = "query")
    })
    @PostMapping("/getEntryAuthList")
    public Object getEntryAuthList(@RequestBody QueryFollowUpModel queryFollowUpModel) {
        //用户id
        String userId = request.getHeader("user-id");
        String enterpriseId = request.getHeader("user-eid");
        if (userId == null || enterpriseId == null) {
            return MapUtils.create("no", 500, "msg", "非法登录，请确认后重试");
        }
        queryFollowUpModel.setUserId(Integer.parseInt(userId));
        queryFollowUpModel.setEnterpriseId(Integer.parseInt(enterpriseId));
        if (queryFollowUpModel.getPageNum() == null || queryFollowUpModel.getPageNum() < 1) {
            queryFollowUpModel.setPageNum(1);
        }
        return entryRecordService.getEntryAuthList(queryFollowUpModel);
    }

    /**
     * 入职确认 -- 审核操作更新
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "入职确认 -- 审核操作")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "面试记录表id", dataType = "int", required = true, paramType = "query")
    })
    @PutMapping("/updateEntryAuth")
    public Object updateEntryAuth(Integer id) {
        String userId = request.getHeader("user-id");
        String enterpriseId = request.getHeader("user-eid");
        if (userId == null || enterpriseId == null) {
            return MapUtils.create("no", 500, "msg", "非法登录，请确认后重试");
        }
        //参数判断
        if (id == null) {
            return MapUtils.create("no", 0, "msg", "参数异常");
        }
        return entryRecordService.updateEntryAuth(id, Integer.parseInt(userId));
    }

    /**
     * 入职确认  跟进操作
     *
     * @param followUpModel
     * @return
     */
    @ApiOperation(value = "入职确认  跟进操作")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "入职记录id", dataType = "int", required = true, paramType = "query"),
            @ApiImplicitParam(name = "entryStatus", value = "入职状态  1 已入职 2 更改入职时间 3 拒绝入职", dataType = "int", required = true, paramType = "query"),
            @ApiImplicitParam(name = "behaviorTime", value = "面试时间", dataType = "date", required = false, paramType = "query"),
            @ApiImplicitParam(name = "remark", value = "备注", dataType = "string", required = false, paramType = "query"),
            @ApiImplicitParam(name = "refuse", value = "拒绝原因", dataType = "string", required = false, paramType = "query")
    })
    @PutMapping("/entryAuthFollowUp")
    public Object entryAuthFollowUp(FollowUpModel followUpModel) {
        //用户id
        String userId = request.getHeader("user-id");
        String enterpriseId = request.getHeader("user-eid");
        if (userId == null || enterpriseId == null) {
            return MapUtils.create("no", 500, "msg", "非法登录，请确认后重试");
        }
        followUpModel.setUserId(Integer.parseInt(userId));
        followUpModel.setEnterpriseId(Integer.parseInt(enterpriseId));

        if (followUpModel.getId() == null) {
            return MapUtils.create("no", 0, "msg", "参数异常");
        }
        //状态判断 1 已入职  2 更改入职时间  3 拒绝入职
        if (!(followUpModel.getEntryStatus() == 1 || followUpModel.getEntryStatus() == 2 || followUpModel.getEntryStatus() == 3)) {
            return MapUtils.create("no", 0, "msg", "interviewStatus参数异常");
        }
        // 1 已入职、 2 更改时间
        if (followUpModel.getEntryStatus() == 1 || followUpModel.getEntryStatus() == 2) {
            //入职时间
            if (followUpModel.getBehaviorTime() == null) {
                return MapUtils.create("no", 0, "msg", "behaviorTime参数异常");
            }
        }
        //拒绝入职
        if (followUpModel.getEntryStatus() == 3) {
            //拒绝原因
            if (StringUtils.isBlank(followUpModel.getRefuse())) {
                return MapUtils.create("no", 0, "msg", "refuse 参数异常");
            }
        }

        return entryRecordService.entryAuthFollowUp(followUpModel);
    }

    /**
     * 获取入职待结算列表
     *
     * @param queryFollowUpModel
     * @return
     */
    @ApiOperation(value = "获取入职待结算列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页码", dataType = "int", required = true, paramType = "query")
    })
    @GetMapping("/getSettleDetailsList")
    public Object getSettleDetailsList(QueryFollowUpModel queryFollowUpModel) {
        //用户id
        String userId = request.getHeader("user-id");
        String enterpriseId = request.getHeader("user-eid");
        if (userId == null || enterpriseId == null) {
            return MapUtils.create("no", 500, "msg", "非法登录，请确认后重试");
        }
        queryFollowUpModel.setUserId(Integer.parseInt(userId));
        queryFollowUpModel.setEnterpriseId(Integer.parseInt(enterpriseId));
        //重置页码
        if (queryFollowUpModel.getPageNum() == null || queryFollowUpModel.getPageNum() < 1) {
            queryFollowUpModel.setPageNum(1);
        }
        return settleDetailsService.getSettleDetailsList(queryFollowUpModel);
    }

    /**
     * 获取结算确认列表
     *
     * @param queryFollowUpModel
     * @return
     */
    @ApiOperation(value = "获取结算确认列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页码", dataType = "int", required = true, paramType = "query"),
            @ApiImplicitParam(name = "createBySet", value = "跟进人", dataType = "set", required = true, paramType = "query"),
            @ApiImplicitParam(name = "settleStatus", value = "1 待结算 2 已结算", dataType = "int", required = false, paramType = "query"),
            @ApiImplicitParam(name = "searchType", value = "搜索类型  fullName (公司名称) title （标题）contactName (求职者名称) contactPhone (求职者手机号) ", dataType = "int", required = false, paramType = "query"),
            @ApiImplicitParam(name = "search", value = "对应的值", dataType = "int", required = false, paramType = "query"),
            @ApiImplicitParam(name = "settleTimeType", value = "结算时间类型 actualSettleTime/实际结算时间 settleTime/预计结算时间", dataType = "int", required = false, paramType = "query"),
            @ApiImplicitParam(name = "beginTime", value = "时间-开始(根据settleTimeType区分)", dataType = "String", required = false, paramType = "query"),
            @ApiImplicitParam(name = "endTime", value = "时间-结束(根据settleTimeType区分)", dataType = "String", required = false, paramType = "query")
    })
    @PostMapping("/getSettleAuthList")
    public Object getSettleAuthList(@RequestBody QueryFollowUpModel queryFollowUpModel) {
        //用户id
        String userId = request.getHeader("user-id");
        String enterpriseId = request.getHeader("user-eid");
        if (userId == null || enterpriseId == null) {
            return MapUtils.create("no", 500, "msg", "非法登录，请确认后重试");
        }
        queryFollowUpModel.setUserId(Integer.parseInt(userId));
        queryFollowUpModel.setEnterpriseId(Integer.parseInt(enterpriseId));
        if (queryFollowUpModel.getPageNum() == null || queryFollowUpModel.getPageNum() < 1) {
            queryFollowUpModel.setPageNum(1);
        }
        return settleDetailsService.getSettleAuthList(queryFollowUpModel);
    }

    /**
     * 结算确认 -- 结算、离职操作
     *
     * @param followUpModel
     * @return
     */
    @ApiOperation(value = "结算确认 -- 结算、离职操作")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "id", dataType = "int", required = true, paramType = "query"),
            @ApiImplicitParam(name = "settleStatus", value = "1 结算 2 离职", dataType = "int", required = true, paramType = "query"),
            @ApiImplicitParam(name = "behaviorTime", value = "离职时间", dataType = "date", required = true, paramType = "query"),
            @ApiImplicitParam(name = "performance", value = "绩效", dataType = "int", required = true, paramType = "query"),
            @ApiImplicitParam(name = "payment", value = "扣款", dataType = "int", required = true, paramType = "query"),
            @ApiImplicitParam(name = "remark", value = "备注", dataType = "string", required = false, paramType = "query"),
            @ApiImplicitParam(name = "delay", value = "是否顺延 0/否 1/是", dataType = "int", required = false, paramType = "query"),
            @ApiImplicitParam(name = "actualSettleTime", value = "实际结算时间", dataType = "date", required = true, paramType = "query")
    })
    @PutMapping("/updateSettleAuth")
    public Object updateSettleAuth(FollowUpModel followUpModel) {
        //用户id
        String userId = request.getHeader("user-id");
        String enterpriseId = request.getHeader("user-eid");
        if (userId == null || enterpriseId == null) {
            return MapUtils.create("no", 500, "msg", "非法登录，请确认后重试");
        }
        followUpModel.setUserId(Integer.parseInt(userId));
        followUpModel.setEnterpriseId(Integer.parseInt(enterpriseId));

        if (followUpModel.getId() == null) {
            return MapUtils.create("no", 0, "msg", "参数异常");
        }
        //结算操作  1 结算 2 离职
        if (!(followUpModel.getSettleStatus() == 1 || followUpModel.getSettleStatus() == 2)) {
            return MapUtils.create("no", 0, "msg", "settleStatus参数异常");
        }
        //离职
        if (followUpModel.getSettleStatus() == 2) {
            //离职时间
            if (followUpModel.getBehaviorTime() == null) {
                return MapUtils.create("no", 0, "msg", "behaviorTime参数异常");
            }
            //备注
            if(StringUtils.isBlank(followUpModel.getRemark())) {
                return MapUtils.create("no", 0, "msg", "remark参数异常");
            }
        }
        //业绩 、扣费
        if (followUpModel.getPerformance() == null || followUpModel.getPayment() == null) {
            return MapUtils.create("no", 0, "msg", "performance、payment参数异常");
        }
        Map map = settleDetailsService.updateSettleAuth(followUpModel);

        if(map.get("projectId") != null){
            // 结算完更新项目进度
            kafkaProducer.syncUpdateProjectProgress(Integer.parseInt(map.get("projectId") + ""));
        }
        return map;
    }

    /**
     * 获取历史跟进列表
     *
     * @param queryFollowUpModel
     * @return
     */
    @ApiOperation(value = "获取历史跟进列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页码", dataType = "int", required = true, paramType = "query"),
            @ApiImplicitParam(name = "userName", value = "求职者姓名", dataType = "String", required = false, paramType = "query"),
            @ApiImplicitParam(name = "phone", value = "电话", dataType = "int", required = false, paramType = "query"),
            @ApiImplicitParam(name = "currentStatus", value = "跟进结果  1 约面失败  2 未参加  4 未通过  9 拒绝入职  10 结算  11 离职", dataType = "int", required = false, paramType = "query"),
            @ApiImplicitParam(name = "title", value = "项目标题", dataType = "String", required = false, paramType = "query"),
            @ApiImplicitParam(name = "search", value = "查询类型 followUpTime  interviewTime  entryTime", dataType = "String", required = false, paramType = "query"),
            @ApiImplicitParam(name = "beginTime", value = "时间-开始(根据search区分)", dataType = "String", required = false, paramType = "query"),
            @ApiImplicitParam(name = "endTime", value = "时间-结束(根据search区分)", dataType = "String", required = false, paramType = "query")
    })
    @GetMapping("/getHistoryList")
    public Object getHistoryList(QueryFollowUpModel queryFollowUpModel) {
        //用户id
        String userId = request.getHeader("user-id");
        String enterpriseId = request.getHeader("user-eid");
        if (userId == null || enterpriseId == null) {
            return MapUtils.create("no", 500, "msg", "非法登录，请确认后重试");
        }
        queryFollowUpModel.setUserId(Integer.parseInt(userId));
        queryFollowUpModel.setEnterpriseId(Integer.parseInt(enterpriseId));
        if (queryFollowUpModel.getPageNum() == null || queryFollowUpModel.getPageNum() < 1) {
            queryFollowUpModel.setPageNum(1);
        }
        return followUpService.getHistoryList(queryFollowUpModel);
    }

    /**
     * 检测项目暂停状态
     *
     * @param queryFollowUpModel
     * @return
     */
    @ApiOperation(value = "检测项目暂停状态")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "跟进id", dataType = "int", required = true, paramType = "query"),
    })
    @GetMapping("/checkSuspend")
    public Object checkSuspend(QueryFollowUpModel queryFollowUpModel) {
        //用户id
        String userId = request.getHeader("user-id");
        String enterpriseId = request.getHeader("user-eid");
        if (userId == null || enterpriseId == null) {
            return MapUtils.create("no", 500, "msg", "非法登录，请确认后重试");
        }
        queryFollowUpModel.setUserId(Integer.parseInt(userId));
        queryFollowUpModel.setEnterpriseId(Integer.parseInt(enterpriseId));

        return commonService.checkSuspend(queryFollowUpModel);
    }

    /**
     * 检测用户未完结跟进记录
     *
     * @param queryFollowUpModel
     * @return
     */
    @ApiOperation(value = "检测用户未完结跟进记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "createBy", value = "执行用户id", dataType = "int", required = true, paramType = "query"),
    })
    @GetMapping("/checkQuitFollowUp")
    public Object checkQuitFollowUp(QueryFollowUpModel queryFollowUpModel) {
        //用户id
        String userId = request.getHeader("user-id");
        String enterpriseId = request.getHeader("user-eid");
        if (userId == null || enterpriseId == null) {
            return MapUtils.create("no", 500, "msg", "非法登录，请确认后重试");
        }
        //空判断
        if (queryFollowUpModel.getCreateBy() == null) {
            return MapUtils.create("no", 0, "msg", "请指定用户");
        }

        queryFollowUpModel.setUserId(Integer.parseInt(userId));
        queryFollowUpModel.setEnterpriseId(Integer.parseInt(enterpriseId));
        return followUpService.checkQuitFollowUp(queryFollowUpModel);
    }

    /**
     * 获取面试每日上线数
     *
     * @param days
     * @param projectId
     * @return
     */
    @ApiOperation(value = "获取面试每日上线数")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "projectId", value = "项目id", dataType = "int", required = true, paramType = "query"),
            @ApiImplicitParam(name = "days", value = "上限天数", dataType = "int", required = true, paramType = "query")
    })
    @GetMapping("/getInterviewDailyLimit")
    public Object getInterviewDailyLimit(Integer projectId, Integer days) {
        //判断
        if (projectId == null || days == null) {
            return MapUtils.create("no", 500, "msg", "参数异常");
        }
        return interviewService.getInterviewDailyLimit(projectId, days);
    }

    @ApiOperation(value = "项目跟进详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "projectId", value = "项目id", dataType = "int", required = true, paramType = "query"),
            @ApiImplicitParam(name = "beginInterviewTime", value = "面试开始时间", dataType = "date", required = false, paramType = "query"),
            @ApiImplicitParam(name = "endInterviewTime", value = "面试结束时间", dataType = "date", required = false, paramType = "query"),
            @ApiImplicitParam(name = "beginEntryTime", value = "入职开始时间", dataType = "date", required = false, paramType = "query"),
            @ApiImplicitParam(name = "endEntryTime", value = "入职结束时间", dataType = "date", required = false, paramType = "query"),
            @ApiImplicitParam(name = "dataType", value = "数据统计类型  0 邀约 (默认)   1到面  2offer  3入职  4结算", dataType = "int", required = false, paramType = "query"),
            @ApiImplicitParam(name = "pageNum", value = "页码", dataType = "int", required = true, paramType = "query"),
            @ApiImplicitParam(name = "sortFiled", value = "面试时间  'interviewTime' 入职时间 'entryTime' ", dataType = "string", required = true, paramType = "query"),
            @ApiImplicitParam(name = "sortType", value = "sortType 0 正序  1 倒序", dataType = "int", required = true, paramType = "query")
    })
    @GetMapping("/getProjectFollowUpDetails")
    public Object getProjectFollowUpDetails(QueryFollowUpModel queryFollowUpModel) {
        //用户id
        String userId = request.getHeader("user-id");
        String enterpriseId = request.getHeader("user-eid");
        if (userId == null || enterpriseId == null) {
            return MapUtils.create("no", 500, "msg", "非法登录，请确认后重试");
        }
        queryFollowUpModel.setUserId(Integer.parseInt(userId));
        queryFollowUpModel.setEnterpriseId(Integer.parseInt(enterpriseId));
        // 重置页码
        if (queryFollowUpModel.getPageNum() == null || queryFollowUpModel.getPageNum() < 1) {
            queryFollowUpModel.setPageNum(1);
        }
        //校验参数
        if (queryFollowUpModel.getProjectId() == null) {
            return MapUtils.create("no", 0, "msg", "projectId 不能为空");
        }

        return followUpService.getProjectFollowUpDetails(queryFollowUpModel);
    }

    @ApiOperation(value = "更新临时结算状态（猎头操作）")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "settleId", value = "结算id", dataType = "int", required = true, paramType = "query"),
            @ApiImplicitParam(name = "tmpStatus", value = "临时状态0 默认 1 结算  2 离职", dataType = "date", required = true, paramType = "query"),
            @ApiImplicitParam(name = "leaveTime", value = "离职时间", dataType = "date", required = false, paramType = "query"),
            @ApiImplicitParam(name = "leaveCause", value = "离职原因", dataType = "string", required = false, paramType = "query")
    })
    @PutMapping("/updateSettleDetails")
    public Object updateSettleDetails(FollowUpModel followUpModel) {
        //用户id
        String userId = request.getHeader("user-id");
        String enterpriseId = request.getHeader("user-eid");
        if (userId == null || enterpriseId == null) {
            return MapUtils.create("no", 500, "msg", "非法登录，请确认后重试");
        }
        followUpModel.setUserId(Integer.parseInt(userId));
        followUpModel.setEnterpriseId(Integer.parseInt(enterpriseId));
        //参数校验
        if (followUpModel.getSettleId() == null) {
            return MapUtils.create("no", 0, "msg", "id 不能为空");
        }
        if (followUpModel.getTmpStatus() == null) {
            return MapUtils.create("no", 0, "msg", "id 不能为空");
        } else {
            // 0 默认 1 结算  2 离职
            if (!(followUpModel.getTmpStatus() == 1 || followUpModel.getTmpStatus() == 2)) {
                return MapUtils.create("no", 0, "msg", "tmpStatus 无效参数");
            }
            if (followUpModel.getTmpStatus() == 2) {
                if (followUpModel.getLeaveTime() == null){
                    return MapUtils.create("no", 0, "msg", "离职时间不能为空");
                }
                if (StringUtils.isBlank(followUpModel.getLeaveCause())){
                    return MapUtils.create("no", 0, "msg", "离职原因不能为空");
                }
            }
        }
        return settleDetailsService.updateSettleDetails(followUpModel);
    }

    @ApiOperation(value = "app简历跟进结果")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页码", dataType = "int", required = true, paramType = "query"),
            @ApiImplicitParam(name = "appPositionFirst", value = "app一级职位", dataType = "int", required = false, paramType = "query"),
            @ApiImplicitParam(name = "appPositionSecond", value = "app二级职位", dataType = "int", required = false, paramType = "query"),
            @ApiImplicitParam(name = "crmPositionFirst", value = "crm一级职位", dataType = "int", required = false, paramType = "query"),
            @ApiImplicitParam(name = "crmPositionSecond", value = "crm二级职位", dataType = "int", required = false, paramType = "query"),
            @ApiImplicitParam(name = "province", value = "期望地点-省份", dataType = "int", required = false, paramType = "query"),
            @ApiImplicitParam(name = "city", value = "期望地点-市", dataType = "int", required = false, paramType = "query"),
            @ApiImplicitParam(name = "district", value = "期望地点-区", dataType = "int", required = false, paramType = "query"),
            @ApiImplicitParam(name = "followStatus", value = "跟进状态  0/待跟进  1/已跟进", dataType = "int", required = false, paramType = "query"),
            @ApiImplicitParam(name = "changeResult", value = "转化结果  0/未转化  1/已转化", dataType = "int", required = false, paramType = "query"),
            @ApiImplicitParam(name = "followUpPersonInCharge", value = "跟进负责人", dataType = "int", required = false, paramType = "query"),
            @ApiImplicitParam(name = "followUp", value = "当前跟进人", dataType = "int", required = true, paramType = "query"),
            @ApiImplicitParam(name = "followUpInterval", value = "跟进间隔 0/半小时内  1/一到两小时  2/大于两小时", dataType = "int", required = true, paramType = "query"),
            @ApiImplicitParam(name = "beginCreateTime", value = "入库开始时间", dataType = "date", required = false, paramType = "query"),
            @ApiImplicitParam(name = "endCreateTime", value = "入库结束时间", dataType = "date", required = false, paramType = "query"),
            @ApiImplicitParam(name = "followResult", value = "交付状态 0/有意向 1/约面失败 2/待面试 3/面试未通过 4/未到面 5/发offer 6/拒绝入职 7/待入职 8/待结算 9/已结算 10/离职", dataType = "int", required = false, paramType = "query"),
    })
    @PostMapping("/getAppResumeFollowUpList")
    public Object getAppResumeFollowUpList(@RequestBody QueryAppResumeFollowUpList queryAppResumeFollowUpList) {
        //用户id
        String userId = request.getHeader("user-id");
        String enterpriseId = request.getHeader("user-eid");
        if (userId == null || enterpriseId == null) {
            return MapUtils.create("no", 500, "msg", "非法登录，请确认后重试");
        }

        //默认第一页
        if (queryAppResumeFollowUpList.getPageNum() == null) {
            queryAppResumeFollowUpList.setPageNum(1);
        }

        queryAppResumeFollowUpList.setEid(Integer.parseInt(enterpriseId));

        return followUpService.getAppResumeFollowUpList(queryAppResumeFollowUpList);
    }

    @ApiOperation(value = "app简历跟进结果-图表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "appPositionFirst", value = "app一级职位", dataType = "int", required = false, paramType = "query"),
            @ApiImplicitParam(name = "appPositionSecond", value = "app二级职位", dataType = "int", required = false, paramType = "query"),
            @ApiImplicitParam(name = "crmPositionFirst", value = "crm一级职位", dataType = "int", required = false, paramType = "query"),
            @ApiImplicitParam(name = "crmPositionSecond", value = "crm二级职位", dataType = "int", required = false, paramType = "query"),
            @ApiImplicitParam(name = "province", value = "期望地点-省份", dataType = "int", required = false, paramType = "query"),
            @ApiImplicitParam(name = "city", value = "期望地点-市", dataType = "int", required = false, paramType = "query"),
            @ApiImplicitParam(name = "district", value = "期望地点-区", dataType = "int", required = false, paramType = "query"),
            @ApiImplicitParam(name = "followStatus", value = "跟进状态  0/待跟进  1/已跟进", dataType = "int", required = false, paramType = "query"),
            @ApiImplicitParam(name = "changeResult", value = "转化结果  0/未转化  1/已转化", dataType = "int", required = false, paramType = "query"),
            @ApiImplicitParam(name = "followUpPersonInCharge", value = "跟进负责人", dataType = "int", required = false, paramType = "query"),
            @ApiImplicitParam(name = "followUp", value = "当前跟进人", dataType = "int", required = true, paramType = "query"),
            @ApiImplicitParam(name = "followUpInterval", value = "跟进间隔 0/半小时内  1/一到两小时  2/大于两小时", dataType = "int", required = true, paramType = "query"),
            @ApiImplicitParam(name = "beginCreateTime", value = "入库开始时间", dataType = "date", required = false, paramType = "query"),
            @ApiImplicitParam(name = "endCreateTime", value = "入库结束时间", dataType = "date", required = false, paramType = "query"),
            @ApiImplicitParam(name = "followResult", value = "交付状态 0/有意向 1/约面失败 2/待面试 3/面试未通过 4/未到面 5/发offer 6/拒绝入职 7/待入职 8/待结算 9/已结算 10/离职", dataType = "int", required = false, paramType = "query"),
    })
    @PostMapping("/getAppResumeFollowUpChart")
    public Object getAppResumeFollowUpChart(@RequestBody QueryAppResumeFollowUpList queryAppResumeFollowUpList) {
        //用户id
        String userId = request.getHeader("user-id");
        String enterpriseId = request.getHeader("user-eid");
        if (userId == null || enterpriseId == null) {
            return MapUtils.create("no", 500, "msg", "非法登录，请确认后重试");
        }

        return followUpService.getAppResumeFollowUpChart(queryAppResumeFollowUpList);
    }

    @ApiOperation(value = "数据校对-面试列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页码", dataType = "int", required = true, paramType = "query"),
            @ApiImplicitParam(name = "beginTime", value = "面试开始时间", dataType = "string", required = false, paramType = "query"),
            @ApiImplicitParam(name = "endTime", value = "面试结束时间", dataType = "string", required = false, paramType = "query"),
            @ApiImplicitParam(name = "title", value = "项目标题", dataType = "string", required = false, paramType = "query"),
            @ApiImplicitParam(name = "searchType", value = "筛选项  contactPhone/手机  contactName/姓名", dataType = "string", required = false, paramType = "query"),
            @ApiImplicitParam(name = "search", value = "筛选值", dataType = "string", required = false, paramType = "query")
    })
    @PostMapping("/getRevokeInterviewList")
    public Object getRevokeInterviewList(@RequestBody QueryFollowRevokeModel model) {
        String userId = request.getHeader("user-id");
        String enterpriseId = request.getHeader("user-eid");
        if (userId == null || enterpriseId == null) {
            return MapUtils.create("no", 500, "msg", "非法登录，请确认后重试");
        }
        model.setUserId(Integer.parseInt(userId));
        model.setEnterpriseId(Integer.parseInt(enterpriseId));
        return followRevokeService.getRevokeInterviewList(model);
    }

    @ApiOperation(value = "数据校对-入职列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页码", dataType = "int", required = true, paramType = "query"),
            @ApiImplicitParam(name = "beginTime", value = "入职开始时间", dataType = "string", required = false, paramType = "query"),
            @ApiImplicitParam(name = "endTime", value = "入职结束时间", dataType = "string", required = false, paramType = "query"),
            @ApiImplicitParam(name = "title", value = "项目标题", dataType = "string", required = false, paramType = "query"),
            @ApiImplicitParam(name = "searchType", value = "筛选项  contactPhone/手机  contactName/姓名", dataType = "string", required = false, paramType = "query"),
            @ApiImplicitParam(name = "search", value = "筛选值", dataType = "string", required = false, paramType = "query")
    })
    @PostMapping("/getRevokeEntryList")
    public Object getRevokeEntryList(@RequestBody QueryFollowRevokeModel model) {
        String userId = request.getHeader("user-id");
        String enterpriseId = request.getHeader("user-eid");
        if (userId == null || enterpriseId == null) {
            return MapUtils.create("no", 500, "msg", "非法登录，请确认后重试");
        }
        model.setUserId(Integer.parseInt(userId));
        model.setEnterpriseId(Integer.parseInt(enterpriseId));
        return followRevokeService.getRevokeEntryList(model);
    }

    @ApiOperation(value = "数据校对-结算列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页码", dataType = "int", required = true, paramType = "query"),
            @ApiImplicitParam(name = "beginTime", value = "入职开始时间", dataType = "string", required = false, paramType = "query"),
            @ApiImplicitParam(name = "endTime", value = "入职结束时间", dataType = "string", required = false, paramType = "query"),
            @ApiImplicitParam(name = "title", value = "项目标题", dataType = "string", required = false, paramType = "query"),
            @ApiImplicitParam(name = "searchType", value = "筛选项  contactPhone/手机  contactName/姓名", dataType = "string", required = false, paramType = "query"),
            @ApiImplicitParam(name = "search", value = "筛选值", dataType = "string", required = false, paramType = "query")
    })
    @PostMapping("/getRevokeSettleList")
    public Object getRevokeSettleList(@RequestBody QueryFollowRevokeModel model) {
        String userId = request.getHeader("user-id");
        String enterpriseId = request.getHeader("user-eid");
        if (userId == null || enterpriseId == null) {
            return MapUtils.create("no", 500, "msg", "非法登录，请确认后重试");
        }
        model.setUserId(Integer.parseInt(userId));
        model.setEnterpriseId(Integer.parseInt(enterpriseId));
        return followRevokeService.getRevokeSettleList(model);
    }

    @ApiOperation(value = "数据校对-撤销")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "followId", value = "跟进id", dataType = "int", required = true, paramType = "query"),
            @ApiImplicitParam(name = "targetId", value = "id 根据type区分", dataType = "int", required = true, paramType = "query"),
            @ApiImplicitParam(name = "type", value = "撤销类型  0/面试  1/入职  2/结算", dataType = "int", required = true, paramType = "query"),
            @ApiImplicitParam(name = "revokeRemark", value = "撤销原因", dataType = "string", required = false, paramType = "query")
    })
    @RepeatSubmit
    @PostMapping("/revoke")
    public Object revoke(@RequestBody FollowRevokeLog followRevoke) {
        String userId = request.getHeader("user-id");
        String enterpriseId = request.getHeader("user-eid");
        if (userId == null || enterpriseId == null) {
            return MapUtils.create("no", 500, "msg", "非法登录，请确认后重试");
        }

        if(followRevoke.getFollowId() == null || followRevoke.getTargetId() == null || followRevoke.getType() == null){
            return MapUtils.create("no", 0, "msg", "参数异常");
        }

        followRevoke.setRevokeBy(Integer.parseInt(userId));
        followRevoke.setEnterpriseId(Integer.parseInt(enterpriseId));
        return followRevokeService.revoke(followRevoke);
    }

    /**
     * 企业端 获取面试确认列表
     *
     * @param project 项目id
     * @param contactName 求职者姓名
     * @param followUpUser 跟进人
     * @param interviewStatus 面试结果 1 待面试 2 未通过  3 未参加  4 已通过  5已中断
     * @param auth 审核状态 0 默认 1 审核
     * @param beginTime 开始时间
     * @param endTime 结束时间
     * @param pageNum 页码
     * @param timeType 时间类型 1/面试时间 2/审核时间
     * @return
     */
    @ApiOperation(value = "企业端 获取面试确认列表")
    @PostMapping("/getEnterpriseProjectInterviewAuthList")
    public Object getEnterpriseProjectInterviewAuthList(@RequestBody QueryFollowUpModel queryFollowUpModel) {

        if(queryFollowUpModel.getProjectId() == null || queryFollowUpModel.getProjectId() == 0){
            return MapUtils.create("no", 0, "msg", "参数异常");
        }

        //用户id
        String userId = request.getHeader("user-id");
        String enterpriseId = request.getHeader("user-eid");
        if (userId == null || enterpriseId == null) {
            return MapUtils.create("no", 500, "msg", "非法登录，请确认后重试");
        }
        queryFollowUpModel.setUserId(Integer.parseInt(userId));
        queryFollowUpModel.setEnterpriseId(Integer.parseInt(enterpriseId));
        if (queryFollowUpModel.getPageNum() == null || queryFollowUpModel.getPageNum() < 1) {
            queryFollowUpModel.setPageNum(1);
        }
        Integer pageBegin = (queryFollowUpModel.getPageNum() - 1) * queryFollowUpModel.getPageSize();
        queryFollowUpModel.setPageBegin(pageBegin);
        return interviewService.getEnterpriseProjectInterviewAuthList(queryFollowUpModel);
    }

    /**
     * 面试审核跟进   （企业跟进）
     *
     * @param id 面试记录id
     * @param interviewStatus 面试状态 1未参加 2 改约时间 3 未通过 4 下轮面试 5 发offer
     * @param remark 备注
     * @param failure 失败原因
     * @param enterpriseId 项目的企业id 不是当前企业端的id
     * @return
     */
    @ApiOperation(value = "面试审核跟进")
    @PostMapping("/interviewEnterpriseAuthFollowUp")
    public Object interviewEnterpriseAuthFollowUp(@RequestBody FollowUpModel followUpModel) {
        //用户id
        String userId = request.getHeader("user-id");
        String enterpriseId = request.getHeader("user-eid");
        if (userId == null || enterpriseId == null) {
            return MapUtils.create("no", 500, "msg", "非法登录，请确认后重试");
        }
        followUpModel.setUserId(Integer.parseInt(userId));
        followUpModel.setBelongEnterpriseId(Integer.parseInt(enterpriseId));

        if (followUpModel.getId() == null) {
            return MapUtils.create("no", 0, "msg", "参数异常");
        }
        //状态判断
        if (!(followUpModel.getInterviewStatus() == 1 || followUpModel.getInterviewStatus() == 2 || followUpModel.getInterviewStatus() == 3
                || followUpModel.getInterviewStatus() == 4 || followUpModel.getInterviewStatus() == 5)) {
            return MapUtils.create("no", 0, "msg", "interviewStatus参数异常");
        }
        //更改时间、下一轮面试
        if (followUpModel.getInterviewStatus() == 2) {
            if (followUpModel.getBehaviorTime() == null) {
                return MapUtils.create("no", 0, "msg", "behaviorTime参数异常");
            }
        }
        followUpModel.setBehaviorTime(new Timestamp(System.currentTimeMillis()));
        return interviewService.interviewEnterpriseAuthFollowUp(followUpModel);
    }

    /**
     * 企业端 获取入职确认列表
     *
     * @param projectId 项目id
     * @param contactName 求职者姓名
     * @param followUpUser 跟进人
     * @param entryStatus 入职结果: 1 已入职 2 拒绝入职  3 待入职  4已中断
     * @param authStatus 0待审核，1已审核
     * @param beginTime 开始时间
     * @param endTime 结束时间
     * @param pageNum 页码
     * @param timeType 1预计入职时间、2实际入职时间、3审核时间
     * @return
     */
    @ApiOperation(value = "企业端 获取入职确认列表")
    @PostMapping("/getEnterpriseProjectEntryAuthList")
    public Object getEnterpriseProjectEntryAuthList(@RequestBody QueryFollowUpModel queryFollowUpModel) {
        //用户id
        String userId = request.getHeader("user-id");
        String enterpriseId = request.getHeader("user-eid");
        if (userId == null || enterpriseId == null) {
            return MapUtils.create("no", 500, "msg", "非法登录，请确认后重试");
        }
        queryFollowUpModel.setUserId(Integer.parseInt(userId));
        queryFollowUpModel.setEnterpriseId(Integer.parseInt(enterpriseId));
        if (queryFollowUpModel.getPageNum() == null || queryFollowUpModel.getPageNum() < 1) {
            queryFollowUpModel.setPageNum(1);
        }
        Integer pageBegin = (queryFollowUpModel.getPageNum() - 1) * queryFollowUpModel.getPageSize();
        queryFollowUpModel.setPageBegin(pageBegin);
        return entryRecordService.getEnterpriseProjectEntryAuthList(queryFollowUpModel);
    }

    /**
     * 企业端 入职确认  跟进操作
     *
     * @param id 入职记录id
     * @param entryStatus 入职状态  1 已入职 2 更改入职时间 3 拒绝入职
     * @param behaviorTime 入职时间
     * @param remark 备注
     * @param refuse 拒绝原因
     * @param enterpriseId 项目的企业id 不是当前企业端的id
     * @return
     */
    @ApiOperation(value = "入职确认  跟进操作")
    @PostMapping("/entryEnterpriseAuthFollowUp")
    public Object entryEnterpriseAuthFollowUp(@RequestBody FollowUpModel followUpModel) {
        //用户id
        String userId = request.getHeader("user-id");
        String enterpriseId = request.getHeader("user-eid");
        if (userId == null || enterpriseId == null) {
            return MapUtils.create("no", 500, "msg", "非法登录，请确认后重试");
        }
        followUpModel.setUserId(Integer.parseInt(userId));
        followUpModel.setBelongEnterpriseId(Integer.parseInt(enterpriseId));

        if (followUpModel.getId() == null) {
            return MapUtils.create("no", 0, "msg", "参数异常");
        }
        //状态判断 1 已入职  2 更改入职时间  3 拒绝入职
        if (!(followUpModel.getEntryStatus() == 1 || followUpModel.getEntryStatus() == 2 || followUpModel.getEntryStatus() == 3)) {
            return MapUtils.create("no", 0, "msg", "interviewStatus参数异常");
        }
        // 1 已入职、 2 更改时间
        if (followUpModel.getEntryStatus() == 1 || followUpModel.getEntryStatus() == 2) {
            //入职时间
            if (followUpModel.getBehaviorTime() == null) {
                return MapUtils.create("no", 0, "msg", "behaviorTime参数异常");
            }
        }
        //拒绝入职
        if (followUpModel.getEntryStatus() == 3) {
            //拒绝原因
            if (StringUtils.isBlank(followUpModel.getRefuse())) {
                return MapUtils.create("no", 0, "msg", "refuse 参数异常");
            }
        }

        return entryRecordService.entryEnterpriseAuthFollowUp(followUpModel);
    }

    /**
     * 企业端 获取结算确认列表
     *
     * @param projectId 项目id
     * @param contactName 求职者姓名
     * @param followUpUser 跟进人
     * @param settleStatus 结算结果 1 待结算 2 已结算  3已离职  4已中断
     * @param authStatus 结算状态
     * @param beginTime 时间开始
     * @param endTime 时间结束
     * @param pageNum 页码
     * @param timeType 1预计结算时间、2实际结算时间、3离职时间、4审核时间
     * @return
     */
    @ApiOperation(value = "企业端 获取结算确认列表")
    @PostMapping("/getEnterpriseProjectSettleAuthList")
    public Object getEnterpriseProjectSettleAuthList(@RequestBody QueryFollowUpModel queryFollowUpModel) {
        //用户id
        String userId = request.getHeader("user-id");
        String enterpriseId = request.getHeader("user-eid");
        if (userId == null || enterpriseId == null) {
            return MapUtils.create("no", 500, "msg", "非法登录，请确认后重试");
        }
        queryFollowUpModel.setUserId(Integer.parseInt(userId));
        queryFollowUpModel.setEnterpriseId(Integer.parseInt(enterpriseId));
        if (queryFollowUpModel.getPageNum() == null || queryFollowUpModel.getPageNum() < 1) {
            queryFollowUpModel.setPageNum(1);
        }
        Integer pageBegin = (queryFollowUpModel.getPageNum() - 1) * queryFollowUpModel.getPageSize();
        queryFollowUpModel.setPageBegin(pageBegin);
        return settleDetailsService.getEnterpriseProjectSettleAuthList(queryFollowUpModel);
    }

    /**
     * 结算确认 -- 结算、离职操作
     *
     * @param id 结算记录id
     * @param settleStatus 结算状态 1 结算 2 离职
     * @param behaviorTime 离职时间
     * @param remark 备注
     * @param actualSettleTime 实际结算时间
     * @param enterpriseId 项目的企业id 不是当前企业端的id
     * @return
     */
    @ApiOperation(value = "结算确认 -- 结算、离职操作")
    @PostMapping("/updateEnterpriseSettleAuth")
    public Object updateEnterpriseSettleAuth(@RequestBody FollowUpModel followUpModel) {
        //用户id
        String userId = request.getHeader("user-id");
        String enterpriseId = request.getHeader("user-eid");
        if (userId == null || enterpriseId == null) {
            return MapUtils.create("no", 500, "msg", "非法登录，请确认后重试");
        }
        followUpModel.setUserId(Integer.parseInt(userId));
        followUpModel.setBelongEnterpriseId(Integer.parseInt(enterpriseId));

        if (followUpModel.getId() == null) {
            return MapUtils.create("no", 0, "msg", "参数异常");
        }
        //结算操作  1 结算 2 离职
        if (!(followUpModel.getSettleStatus() == 1 || followUpModel.getSettleStatus() == 2)) {
            return MapUtils.create("no", 0, "msg", "settleStatus参数异常");
        }
        //离职
        if (followUpModel.getSettleStatus() == 2) {
            //离职时间
            if (followUpModel.getBehaviorTime() == null) {
                return MapUtils.create("no", 0, "msg", "behaviorTime参数异常");
            }
        }
        Map map = settleDetailsService.updateEnterpriseSettleAuth(followUpModel);

        if(map.get("projectId") != null){
            // 结算完更新项目进度
            kafkaProducer.syncUpdateProjectProgress(Integer.parseInt(map.get("projectId") + ""));
        }
        return map;
    }

    /**
     * 合作项目跟进信息
     * @param projectId 项目id
     * @param currentStatus
     * @param beginInterviewTime 面试开始时间
     * @param endInterviewTime 面试结束时间
     * @param beginEntryTime 入职开始时间
     * @param endEntryTime 入职结束时间
     * @param pageNum 页码
     */
    @ApiOperation(value = "合作项目跟进信息")
    @PostMapping("/getCooperationProjectFollowUpList")
    public Object getCooperationProjectFollowUpInfo(@RequestBody QueryFollowUpModel queryFollowUpModel) {
        //用户id
        String userId = request.getHeader("user-id");
        String enterpriseId = request.getHeader("user-eid");
        if (userId == null || enterpriseId == null) {
            return MapUtils.create("no", 500, "msg", "非法登录，请确认后重试");
        }
        queryFollowUpModel.setUserId(Integer.parseInt(userId));
        queryFollowUpModel.setEnterpriseId(Integer.parseInt(enterpriseId));

        if (queryFollowUpModel.getPageNum() == null || queryFollowUpModel.getPageNum() < 1) {
            queryFollowUpModel.setPageNum(1);
        }
        Integer pageBegin = (queryFollowUpModel.getPageNum() - 1) * queryFollowUpModel.getPageSize();
        queryFollowUpModel.setPageBegin(pageBegin);

        return followUpService.getCooperationProjectFollowUpList(queryFollowUpModel);
    }

    @PostMapping("/stopFollowUp/{userId}/{projectId}")
    public Object stopFollowUp(@PathVariable("userId") Integer userId, @PathVariable("projectId") Integer projectId) {
        return followUpService.stopFollowUp(userId, projectId);
    }

    @PostMapping("/getShareProjectFollowUpCount")
    public Object getShareProjectFollowUpCount(Integer projectId){
        if(projectId == null){
            return MapUtils.create("no", 0, "msg", "参数异常");
        }
        return followUpService.getShareProjectFollowUpCount(projectId);
    }


    /**
     * 简历推荐列表--企业端
     * @param jobSeekerKeyword 求职者姓名或手机号
     * @param status 1/审核中 2/通过 3/未通过
     * @param timeType 时间类型 1/推荐时间 2/审核时间
     * @param startTime 开始日期
     * @param endTime 结束日期
     * @param pageNum 页码
     * @param recommendUserName 推荐人姓名
     * @return
     */
    @PostMapping("/getFollowRecommendListByEnterprise")
    public Object getFollowRecommendListByEnterprise(@RequestBody FollowRecommendListDTO dto) {
        String userId = request.getHeader("user-id");
        String enterpriseId = request.getHeader("user-eid");
        if (userId == null || enterpriseId == null) {
            return MapUtils.create("no", 500, "msg", "非法登录，请确认后重试");
        }

        dto.setEnterpriseId(Integer.parseInt(enterpriseId));
        dto.setUserId(Integer.parseInt(userId));

        if (dto.getPageNum() == null) {
            dto.setPageNum(1);
        }
        return followUpService.getFollowRecommendListByEnterprise(dto);
    }


    /**
     * 简历推荐列表审核--企业端
     *
     * @param id 数据id
     * @param status 审核状态 1/审核中 2/通过 3/未通过
     * @param failureReason 未通过原因
     * @return
     */
    @PostMapping("/auditFollowRecommendById")
    public Object auditFollowRecommendById(@RequestBody CrmFollowRecommend dto) {
        String userId = request.getHeader("user-id");
        String enterpriseId = request.getHeader("user-eid");
        if (userId == null || enterpriseId == null) {
            return MapUtils.create("no", 500, "msg", "非法登录，请确认后重试");
        }

        dto.setCheckedBy(Integer.parseInt(userId));

        return followUpService.auditFollowRecommendById(dto);
    }

    /**
     * 复制链接
     * @param projectId 项目id
     * @param interviewRecordId 面试id
     */
    @PostMapping("/copyLink")
    public Object copyLink(Integer projectId, Integer interviewRecordId) {
        String userId = request.getHeader("user-id");
        String enterpriseId = request.getHeader("user-eid");
        if (userId == null || enterpriseId == null) {
            return MapUtils.create("no", 500, "msg", "非法登录，请确认后重试");
        }
        return interviewService.copyLink(projectId, interviewRecordId, Integer.parseInt(userId), Integer.parseInt(enterpriseId));
}