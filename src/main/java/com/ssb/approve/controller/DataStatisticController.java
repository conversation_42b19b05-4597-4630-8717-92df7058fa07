package com.ssb.approve.controller;

import com.ssb.approve.model.*;
import com.ssb.approve.model.dto.*;
import com.ssb.approve.service.DataStatisticService;
import com.ssb.approve.utils.MapUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.FileCopyUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.FileInputStream;
import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;

/**
 * @ClassName: DataStatisticController
 * @Description: 跟进数据统计 controller
 * @Author: YZK
 * @Date: 2020年9月2日10:11:48
 **/
@Api(tags = "跟进数据统计API", value = "跟进数据统计API")
@RestController
@RequestMapping("/api/data")
public class DataStatisticController {

    @Autowired
    private HttpServletRequest request;

    @Autowired
    private DataStatisticService dataStatisticService;

    @ApiOperation(value = "团队项目跟进数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "deptIds", value = "部门id", dataType = "array", required = true, paramType = "query"),
            @ApiImplicitParam(name = "beginTime", value = "面试开始时间", dataType = "date", required = true, paramType = "query"),
            @ApiImplicitParam(name = "endTime", value = "面试结束时间", dataType = "date", required = true, paramType = "query"),
            @ApiImplicitParam(name = "pageNum", value = "页码", dataType = "date", required = true, paramType = "query"),
            @ApiImplicitParam(name = "userStatus", value = "员工状态", dataType = "date", required = true, paramType = "query")
    })
    @GetMapping("/getTeamFollowUpData")
    public Object getTeamFollowUpData(QueryFollowUpModel queryFollowUpModel) {
        //用户id
        String userId = request.getHeader("user-id");
        String enterpriseId = request.getHeader("user-eid");
        if (userId == null || enterpriseId == null) {
            return MapUtils.create("no", 500, "msg", "非法登录，请确认后重试");
        }
        queryFollowUpModel.setUserId(Integer.parseInt(userId));
        queryFollowUpModel.setEnterpriseId(Integer.parseInt(enterpriseId));
        // 重置页码
        if (queryFollowUpModel.getPageNum() == null || queryFollowUpModel.getPageNum() < 1) {
            queryFollowUpModel.setPageNum(1);
        }
        // 校验时间
        if (queryFollowUpModel.getBeginTime() == null || queryFollowUpModel.getEndTime() == null) {
            return MapUtils.create("no", 0, "msg", "请选择时间");
        }
        // 校验 团队
        if (queryFollowUpModel.getDeptIds() == null || queryFollowUpModel.getDeptIds().size() == 0) {
            return MapUtils.create("no", 0, "msg", "请选择团队");
        }

        return dataStatisticService.getTeamFollowUpData(queryFollowUpModel);
    }

    @ApiOperation(value = "个人跟进数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "createBySet", value = "跟进人", dataType = "array", required = true, paramType = "query"),
            @ApiImplicitParam(name = "beginTime", value = "面试开始时间", dataType = "date", required = true, paramType = "query"),
            @ApiImplicitParam(name = "endTime", value = "面试结束时间", dataType = "date", required = true, paramType = "query"),
            @ApiImplicitParam(name = "searchType", value = "查询时间类型 inviteTime/邀约时间 interviewTime/面试时间", dataType = "date", required = true, paramType = "query"),
            @ApiImplicitParam(name = "queryType", value = "查询入口  0/邀约量  1/到面量  2/offer量 ", dataType = "date", required = true, paramType = "query"),
            @ApiImplicitParam(name = "interviewResult", value = "面试结果 0/不限  1/待面试  2/未参加  3/未通过  4/已通过 ", dataType = "date", required = true, paramType = "query")
    })
    @GetMapping("/getIndividualData")
    public Object getIndividualData(QueryFollowUpModel queryFollowUpModel) {
        //用户id
        String userId = request.getHeader("user-id");
        String enterpriseId = request.getHeader("user-eid");
        if (userId == null || enterpriseId == null) {
            return MapUtils.create("no", 500, "msg", "非法登录，请确认后重试");
        }
        queryFollowUpModel.setUserId(Integer.parseInt(userId));
        queryFollowUpModel.setEnterpriseId(Integer.parseInt(enterpriseId));

        // 校验时间
        if (queryFollowUpModel.getBeginTime() == null || queryFollowUpModel.getEndTime() == null) {
            return MapUtils.create("no", 0, "msg", "请选择时间");
        }
        return dataStatisticService.getIndividualData(queryFollowUpModel);
    }

    @ApiOperation(value = "个人数据 邀约量")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "createBySet", value = "跟进人", dataType = "array", required = true, paramType = "query"),
            @ApiImplicitParam(name = "beginTime", value = "面试开始时间", dataType = "date", required = true, paramType = "query"),
            @ApiImplicitParam(name = "endTime", value = "面试结束时间", dataType = "date", required = true, paramType = "query"),
            @ApiImplicitParam(name = "pageNum", value = "页码", dataType = "date", required = true, paramType = "query"),
            @ApiImplicitParam(name = "interviewResult", value = "面试结果 0/不限  1/待面试  2/未参加  3/未通过  4/已通过 ", dataType = "date", required = true, paramType = "query"),
            @ApiImplicitParam(name = "searchType", value = "查询时间类型 inviteTime/邀约时间 interviewTime/面试时间", dataType = "date", required = true, paramType = "query")
    })
    @GetMapping("/getIndividualInviteData")
    public Object getIndividualInviteData(QueryFollowUpModel queryFollowUpModel) {
        //用户id
        String userId = request.getHeader("user-id");
        String enterpriseId = request.getHeader("user-eid");
        if (userId == null || enterpriseId == null) {
            return MapUtils.create("no", 500, "msg", "非法登录，请确认后重试");
        }
        queryFollowUpModel.setUserId(Integer.parseInt(userId));
        queryFollowUpModel.setEnterpriseId(Integer.parseInt(enterpriseId));
        // 重置页码
        if (queryFollowUpModel.getPageNum() == null || queryFollowUpModel.getPageNum() < 1) {
            queryFollowUpModel.setPageNum(1);
        }
        // 校验时间
        if (queryFollowUpModel.getBeginTime() == null || queryFollowUpModel.getEndTime() == null) {
            return MapUtils.create("no", 0, "msg", "请选择时间");
        }

        return dataStatisticService.getIndividualInviteData(queryFollowUpModel);
    }

    @ApiOperation(value = "个人数据 面试量")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "createBySet", value = "跟进人", dataType = "array", required = true, paramType = "query"),
            @ApiImplicitParam(name = "beginTime", value = "面试开始时间", dataType = "date", required = true, paramType = "query"),
            @ApiImplicitParam(name = "endTime", value = "面试结束时间", dataType = "date", required = true, paramType = "query"),
            @ApiImplicitParam(name = "pageNum", value = "页码", dataType = "date", required = true, paramType = "query"),
            @ApiImplicitParam(name = "interviewResult", value = "面试结果 0/不限  3/未通过  4/已通过 ", dataType = "date", required = true, paramType = "query"),
            @ApiImplicitParam(name = "searchType", value = "查询时间类型 inviteTime/邀约时间 interviewTime/面试时间", dataType = "date", required = true, paramType = "query")
    })
    @GetMapping("/getIndividualInterviewData")
    public Object getIndividualInterviewData(QueryFollowUpModel queryFollowUpModel) {
        //用户id
        String userId = request.getHeader("user-id");
        String enterpriseId = request.getHeader("user-eid");
        if (userId == null || enterpriseId == null) {
            return MapUtils.create("no", 500, "msg", "非法登录，请确认后重试");
        }
        queryFollowUpModel.setUserId(Integer.parseInt(userId));
        queryFollowUpModel.setEnterpriseId(Integer.parseInt(enterpriseId));
        // 重置页码
        if (queryFollowUpModel.getPageNum() == null || queryFollowUpModel.getPageNum() < 1) {
            queryFollowUpModel.setPageNum(1);
        }
        // 校验时间
        if (queryFollowUpModel.getBeginTime() == null || queryFollowUpModel.getEndTime() == null) {
            return MapUtils.create("no", 0, "msg", "请选择时间");
        }
        return dataStatisticService.getIndividualInterviewData(queryFollowUpModel);
    }

    @ApiOperation(value = "个人数据 offer量")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "createBySet", value = "跟进人", dataType = "array", required = true, paramType = "query"),
            @ApiImplicitParam(name = "beginTime", value = "面试开始时间", dataType = "date", required = true, paramType = "query"),
            @ApiImplicitParam(name = "endTime", value = "面试结束时间", dataType = "date", required = true, paramType = "query"),
            @ApiImplicitParam(name = "pageNum", value = "页码", dataType = "date", required = true, paramType = "query"),
            @ApiImplicitParam(name = "searchType", value = "查询时间类型 inviteTime/邀约时间 interviewTime/面试时间", dataType = "date", required = true, paramType = "query")
    })
    @GetMapping("/getIndividualOfferData")
    public Object getIndividualOfferData(QueryFollowUpModel queryFollowUpModel) {
        //用户id
        String userId = request.getHeader("user-id");
        String enterpriseId = request.getHeader("user-eid");
        if (userId == null || enterpriseId == null) {
            return MapUtils.create("no", 500, "msg", "非法登录，请确认后重试");
        }
        queryFollowUpModel.setUserId(Integer.parseInt(userId));
        queryFollowUpModel.setEnterpriseId(Integer.parseInt(enterpriseId));
        // 重置页码
        if (queryFollowUpModel.getPageNum() == null || queryFollowUpModel.getPageNum() < 1) {
            queryFollowUpModel.setPageNum(1);
        }
        // 校验时间
        if (queryFollowUpModel.getBeginTime() == null || queryFollowUpModel.getEndTime() == null) {
            return MapUtils.create("no", 0, "msg", "请选择时间");
        }
        return dataStatisticService.getIndividualOfferData(queryFollowUpModel);
    }

    @ApiOperation(value = "个人数据 入职量")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "createBySet", value = "跟进人", dataType = "array", required = true, paramType = "query"),
            @ApiImplicitParam(name = "beginTime", value = "面试开始时间", dataType = "date", required = true, paramType = "query"),
            @ApiImplicitParam(name = "endTime", value = "面试结束时间", dataType = "date", required = true, paramType = "query"),
            @ApiImplicitParam(name = "pageNum", value = "页码", dataType = "date", required = true, paramType = "query")
    })
    @GetMapping("/getIndividualEntryData")
    public Object getIndividualEntryData(QueryFollowUpModel queryFollowUpModel) {
        //用户id
        String userId = request.getHeader("user-id");
        String enterpriseId = request.getHeader("user-eid");
        if (userId == null || enterpriseId == null) {
            return MapUtils.create("no", 500, "msg", "非法登录，请确认后重试");
        }
        queryFollowUpModel.setUserId(Integer.parseInt(userId));
        queryFollowUpModel.setEnterpriseId(Integer.parseInt(enterpriseId));
        // 重置页码
        if (queryFollowUpModel.getPageNum() == null || queryFollowUpModel.getPageNum() < 1) {
            queryFollowUpModel.setPageNum(1);
        }
        // 校验时间
        if (queryFollowUpModel.getBeginTime() == null || queryFollowUpModel.getEndTime() == null) {
            return MapUtils.create("no", 0, "msg", "请选择时间");
        }
        return dataStatisticService.getIndividualEntryData(queryFollowUpModel);
    }

    @ApiOperation(value = "个人数据 结算量")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "createBySet", value = "跟进人", dataType = "array", required = true, paramType = "query"),
            @ApiImplicitParam(name = "beginTime", value = "面试开始时间", dataType = "date", required = true, paramType = "query"),
            @ApiImplicitParam(name = "endTime", value = "面试结束时间", dataType = "date", required = true, paramType = "query"),
            @ApiImplicitParam(name = "pageNum", value = "页码", dataType = "date", required = true, paramType = "query")
    })
    @GetMapping("/getIndividualSettleData")
    public Object getIndividualSettleData(QueryFollowUpModel queryFollowUpModel) {
        //用户id
        String userId = request.getHeader("user-id");
        String enterpriseId = request.getHeader("user-eid");
        if (userId == null || enterpriseId == null) {
            return MapUtils.create("no", 500, "msg", "非法登录，请确认后重试");
        }
        queryFollowUpModel.setUserId(Integer.parseInt(userId));
        queryFollowUpModel.setEnterpriseId(Integer.parseInt(enterpriseId));
        // 重置页码
        if (queryFollowUpModel.getPageNum() == null || queryFollowUpModel.getPageNum() < 1) {
            queryFollowUpModel.setPageNum(1);
        }
        // 校验时间
        if (queryFollowUpModel.getBeginTime() == null || queryFollowUpModel.getEndTime() == null) {
            return MapUtils.create("no", 0, "msg", "请选择时间");
        }
        return dataStatisticService.getIndividualSettleData(queryFollowUpModel);
    }

    //销售工作台
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "页数", dataType = "int", required = false, paramType = "query"),
            @ApiImplicitParam(name = "beginTime", value = "开始时间", dataType = "date", required = false, paramType = "query"),
            @ApiImplicitParam(name = "endTime", value = "结束时间", dataType = "date", required = false, paramType = "query"),
            @ApiImplicitParam(name = "status", value = "1待面试2待入职3结算", dataType = "int", required = false, paramType = "query")
    })
    @PostMapping("/getVertrieb")
    public Object getVertrieb(@RequestBody MarketWorkModel mk){
        //用户id
        String userId = request.getHeader("user-id");
        String enterpriseId = request.getHeader("user-eid");
        if (userId == null || enterpriseId == null) {
            return MapUtils.create("no", 500, "msg", "非法登录，请确认后重试");
        }
        mk.setUid(Integer.parseInt(userId));
        mk.setEid(Integer.parseInt(enterpriseId));
        return dataStatisticService.getVertrieb(mk);
    }

    //核对单
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页数", dataType = "int", required = false, paramType = "query"),
            @ApiImplicitParam(name = "beginTime", value = "开始时间", dataType = "date", required = false, paramType = "query"),
            @ApiImplicitParam(name = "endTime", value = "结束时间", dataType = "date", required = false, paramType = "query"),
            @ApiImplicitParam(name = "search", value = "搜索内容", dataType = "String", required = false, paramType = "query")
    })
    @PostMapping("/getChectSlip")
    public Object getChectSlip(@RequestBody CheckSlipModel cs){
        //用户idw
        String userId = request.getHeader("user-id");
        String enterpriseId = request.getHeader("user-eid");
        if (userId == null || enterpriseId == null) {
            return MapUtils.create("no", 500, "msg", "非法登录，请确认后重试");
        }
        cs.setUserId(Integer.parseInt(userId));
        cs.setEnterpriseId(Integer.parseInt(enterpriseId));
        return dataStatisticService.getCheckSlip(cs);
    }

    //核对单明细
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页数", dataType = "int", required = false, paramType = "query"),
            @ApiImplicitParam(name = "beginTime", value = "开始时间", dataType = "date", required = false, paramType = "query"),
            @ApiImplicitParam(name = "endTime", value = "结束时间", dataType = "date", required = false, paramType = "query"),
            @ApiImplicitParam(name = "eid", value = "客户公司id", dataType = "int", required = false, paramType = "query")
    })
    @PostMapping("/getCheckSlipDetail")
    public Object getCheckSlipDetail(@RequestBody CheckSlipModel cs){
        //用户idw
        String userId = request.getHeader("user-id");
        String enterpriseId = request.getHeader("user-eid");
        if (userId == null || enterpriseId == null) {
            return MapUtils.create("no", 500, "msg", "非法登录，请确认后重试");
        }
        if (cs.getEid() == null){
            return MapUtils.create("no", 500, "msg", "没有公司");
        }
        cs.setUserId(Integer.parseInt(userId));
        cs.setEnterpriseId(Integer.parseInt(enterpriseId));
        return dataStatisticService.getCheckSlipDetail(cs);
    }

    @ApiOperation(value = "导出核对单明细")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "beginTime", value = "开始时间", dataType = "date", required = false, paramType = "query"),
            @ApiImplicitParam(name = "endTime", value = "结束时间", dataType = "date", required = false, paramType = "query"),
            @ApiImplicitParam(name = "eid", value = "客户公司id", dataType = "int", required = false, paramType = "query")
    })
    @PostMapping("/exportExpectedCheckSlip")
    public Object exportExpectedCheckSlip(@RequestBody CheckSlipModel cs, HttpServletResponse httpServletResponse) throws IOException, ParseException {
        //用户idw
        String userId = request.getHeader("user-id");
        String enterpriseId = request.getHeader("user-eid");
        if (userId == null || enterpriseId == null) {
            return MapUtils.create("no", 500, "msg", "非法登录，请确认后重试");
        }
        if (cs.getEid() == null){
            return MapUtils.create("no", 500, "msg", "没有公司");
        }
        cs.setUserId(Integer.parseInt(userId));
        cs.setEnterpriseId(Integer.parseInt(enterpriseId));
        String exportInfo = dataStatisticService.exportExpectedCheckSlip(cs);
        Calendar calendar = Calendar.getInstance();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        String now = formatter.format(calendar.getTime());
        String filename = now + ".xls";
        httpServletResponse.setContentType("application/x-msdownload");
        httpServletResponse.setHeader("Content-Disposition", "attachment;filename=\"" + filename + "\"");
        FileCopyUtils.copy(new FileInputStream(exportInfo), httpServletResponse.getOutputStream());
        return "success";
    }

    @ApiOperation(value = "数据监测-招聘跟进撤销")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页数", dataType = "int", required = true, paramType = "query"),
            @ApiImplicitParam(name = "beginTime", value = "开始时间", dataType = "date", required = false, paramType = "query"),
            @ApiImplicitParam(name = "endTime", value = "结束时间", dataType = "date", required = false, paramType = "query"),
            @ApiImplicitParam(name = "revokeUser", value = "撤销操作人", dataType = "string", required = false, paramType = "query"),
            @ApiImplicitParam(name = "searchType", value = "查询类型 手机号/contactPhone  项目标题/title  求职者姓名/contactName", dataType = "string", required = false, paramType = "query"),
            @ApiImplicitParam(name = "search", value = "查询参数", dataType = "string", required = false, paramType = "query")
    })
    @PostMapping("/getRevokeList")
    public Object getRevokeList(@RequestBody QueryFollowRevokeStatisticsModel model){
        //用户idw
        String userId = request.getHeader("user-id");
        String enterpriseId = request.getHeader("user-eid");
        if (userId == null || enterpriseId == null) {
            return MapUtils.create("no", 500, "msg", "非法登录，请确认后重试");
        }

        model.setUserId(Integer.parseInt(userId));
        model.setEnterpriseId(Integer.parseInt(enterpriseId));
        return dataStatisticService.getRevokeList(model);
    }

    /**
     * 卡片
     * @return
     */
    @PostMapping("/card")
    public Object card(){
        String userId = request.getHeader("user-id");
        String enterpriseId = request.getHeader("user-eid");
        if (userId == null || enterpriseId == null) {
            return MapUtils.create("no", 500, "msg", "非法登录，请确认后重试");
        }
        return dataStatisticService.card(Integer.parseInt(userId), Integer.parseInt(enterpriseId));
    }

    /**
     * 排行榜
     * @param deptId   部门id
     * @param userName   用户名
     * @param settlementTimeStart 结算时间
     * @param settlementTimeEnd  结算时间
     * @param type 1/首页  2/全部
     * @param pageNum  页码
     */
    @PostMapping("/theCharts")
    public Object theCharts(@RequestBody TheChartsDTO dto){
        String userId = request.getHeader("user-id");
        String enterpriseId = request.getHeader("user-eid");
        if (userId == null || enterpriseId == null) {
            return MapUtils.create("no", 500, "msg", "非法登录，请确认后重试");
        }
        if(dto.getType() == null){
            return MapUtils.create("no", 0, "msg", "参数异常");
        }
        dto.setUserId(Integer.parseInt(userId));
        dto.setEnterpriseId(Integer.parseInt(enterpriseId));
        return dataStatisticService.getTheCharts(dto);
    }

    /**
     * 日常数据
     * @param timeStart 开始时间
     * @param timeEnd  结束时间
     * @param type       1/个人 2/团队
     * @param deptId    部门id
     * @param userId    选择用户时传用户id
     * @return
     */
    @PostMapping("/getDailyData")
    public Object getDailyData(@RequestBody DailyDataDTO dto){

        if(dto.getUserId() == null || dto.getUserId() == 0){
            String userId = request.getHeader("user-id");
            dto.setUserId(Integer.parseInt(userId));
        }
        String enterpriseId = request.getHeader("user-eid");
        dto.setEnterpriseId(Integer.parseInt(enterpriseId));

        if(dto.getType() == null){
            return MapUtils.create("no", 0, "msg", "参数异常");
        }

        if(dto.getType() == 2 && dto.getDeptId() == null){
            return MapUtils.create("no", 0, "msg", "参数异常");
        }

        return dataStatisticService.getDailyData(dto);
    }

    /**
     * 数据变化图
     * @param timeStart 开始时间
     * @param timeEnd  结束时间
     * @param type       1/个人 2/团队
     * @param deptId    部门id
     * @param userId    选择用户时传用户id
     * @return
     */
    @PostMapping("/getRecruitmentData")
    public Object getRecruitmentData(@RequestBody RecruitmentDataDTO dto){
        if(dto.getUserId() == null || dto.getUserId() == 0){
            String userId = request.getHeader("user-id");
            dto.setUserId(Integer.parseInt(userId));
        }
        String enterpriseId = request.getHeader("user-eid");
        dto.setEnterpriseId(Integer.parseInt(enterpriseId));

        if(dto.getTimeStart() == null || dto.getTimeEnd() == null){
            return MapUtils.create("no", 0, "msg", "参数异常");
        }

        if(dto.getType() == null){
            return MapUtils.create("no", 0, "msg", "参数异常");
        }

        if(dto.getType() == 2 && dto.getDeptId() == null){
            return MapUtils.create("no", 0, "msg", "参数异常");
        }

        return dataStatisticService.getRecruitmentData(dto);
    }

    /**
     * 邀约数据
     * @param timeStart 开始时间
     * @param timeEnd  结束时间
     * @param userName 求职者姓名
     * @param phone   求职者电话
     * @param title   项目名称
     * @param customerName  企业名称
     * @param interviewStatus 面试结果 0/不限、1/待面试、2/未参加、3/未通过、4/已通过
     * @param pageNum 页码
     * @param followUser 跟进人
     * @param type       1/个人 2/团队
     * @param deptId    部门id
     * @param userId    选择用户时传用户id
     * @return
     */
    @PostMapping("/getInvitationForInterviewDataList")
    public Object getInvitationForInterviewDataList(@RequestBody RecruitmentDataListDTO dto){
        if(dto.getUserId() == null || dto.getUserId() == 0){
            String userId = request.getHeader("user-id");
            dto.setUserId(Integer.parseInt(userId));
        }
        String enterpriseId = request.getHeader("user-eid");
        dto.setEnterpriseId(Integer.parseInt(enterpriseId));

        if(dto.getType() == null){
            return MapUtils.create("no", 0, "msg", "参数异常");
        }

        if(dto.getType() == 2 && dto.getDeptId() == null){
            return MapUtils.create("no", 0, "msg", "参数异常");
        }

        return dataStatisticService.getInvitationForInterviewDataList(dto);
    }

    /**
     * 到面数据
     * @param timeStart 开始时间
     * @param timeEnd  结束时间
     * @param userName 求职者姓名
     * @param phone   求职者电话
     * @param title   项目名称
     * @param customerName  企业名称
     * @param interviewStatus 面试结果 0/不限、1/未通过、2/已通过
     * @param pageNum 页码
     * @param followUser 跟进人
     * @param type       1/个人 2/团队
     * @param deptId    部门id
     * @param userId    选择用户时传用户id
     * @return
     */
    @PostMapping("/getInterviewDataList")
    public Object getInterviewDataList(@RequestBody RecruitmentDataListDTO dto){
        if(dto.getUserId() == null || dto.getUserId() == 0){
            String userId = request.getHeader("user-id");
            dto.setUserId(Integer.parseInt(userId));
        }
        String enterpriseId = request.getHeader("user-eid");
        dto.setEnterpriseId(Integer.parseInt(enterpriseId));

        if(dto.getType() == null){
            return MapUtils.create("no", 0, "msg", "参数异常");
        }

        if(dto.getType() == 2 && dto.getDeptId() == null){
            return MapUtils.create("no", 0, "msg", "参数异常");
        }

        return dataStatisticService.getInterviewDataList(dto);
    }

    /**
     * offer数据
     * @param timeStart 开始时间
     * @param timeEnd  结束时间
     * @param userName 求职者姓名
     * @param phone   求职者电话
     * @param title   项目名称
     * @param customerName  企业名称
     * @param pageNum 页码
     * @param followUser 跟进人
     * @param type       1/个人 2/团队
     * @param deptId    部门id
     * @param userId    选择用户时传用户id
     * @return
     */
    @PostMapping("/getOfferDataList")
    public Object getOfferDataList(@RequestBody RecruitmentDataListDTO dto){
        if(dto.getUserId() == null || dto.getUserId() == 0){
            String userId = request.getHeader("user-id");
            dto.setUserId(Integer.parseInt(userId));
        }
        String enterpriseId = request.getHeader("user-eid");
        dto.setEnterpriseId(Integer.parseInt(enterpriseId));

        if(dto.getType() == null){
            return MapUtils.create("no", 0, "msg", "参数异常");
        }

        if(dto.getType() == 2 && dto.getDeptId() == null){
            return MapUtils.create("no", 0, "msg", "参数异常");
        }

        return dataStatisticService.getOfferDataList(dto);
    }

    /**
     * 入职数据
     * @param timeStart 开始时间
     * @param timeEnd  结束时间
     * @param userName 求职者姓名
     * @param phone   求职者电话
     * @param title   项目名称
     * @param customerName  企业名称
     * @param pageNum 页码
     * @param followUser 跟进人
     * @param type       1/个人 2/团队
     * @param deptId    部门id
     * @param userId    选择用户时传用户id
     * @return
     */
    @PostMapping("/getEntryDataList")
    public Object getEntryDataList(@RequestBody RecruitmentDataListDTO dto){
        if(dto.getUserId() == null || dto.getUserId() == 0){
            String userId = request.getHeader("user-id");
            dto.setUserId(Integer.parseInt(userId));
        }
        String enterpriseId = request.getHeader("user-eid");
        dto.setEnterpriseId(Integer.parseInt(enterpriseId));

        if(dto.getTimeStart() == null || dto.getTimeEnd() == null){
            return MapUtils.create("no", 0, "msg", "参数异常");
        }

        if(dto.getType() == null){
            return MapUtils.create("no", 0, "msg", "参数异常");
        }

        if(dto.getType() == 2 && dto.getDeptId() == null){
            return MapUtils.create("no", 0, "msg", "参数异常");
        }

        return dataStatisticService.getEntryDataList(dto);
    }

    /**
     * 结算数据
     * @param timeStart 开始时间
     * @param timeEnd  结束时间
     * @param userName 求职者姓名
     * @param phone   求职者电话
     * @param title   项目名称
     * @param customerName  企业名称
     * @param pageNum 页码
     * @param followUser 跟进人
     * @param type       1/个人 2/团队
     * @param deptId    部门id
     * @param userId    选择用户时传用户id
     * @return
     */
    @PostMapping("/getSettleDataList")
    public Object getSettleDataList(@RequestBody RecruitmentDataListDTO dto){
        if(dto.getUserId() == null || dto.getUserId() == 0){
            String userId = request.getHeader("user-id");
            dto.setUserId(Integer.parseInt(userId));
        }
        String enterpriseId = request.getHeader("user-eid");
        dto.setEnterpriseId(Integer.parseInt(enterpriseId));

        if(dto.getTimeStart() == null || dto.getTimeEnd() == null){
            return MapUtils.create("no", 0, "msg", "参数异常");
        }

        if(dto.getType() == null){
            return MapUtils.create("no", 0, "msg", "参数异常");
        }

        if(dto.getType() == 2 && dto.getDeptId() == null){
            return MapUtils.create("no", 0, "msg", "参数异常");
        }

        return dataStatisticService.getSettleDataList(dto);
    }

    /**
     * 转化率
     * @param timeStart 开始时间
     * @param timeEnd  结束时间
     * @param type       1/个人 2/团队
     * @param deptId    部门id
     * @param userId    选择用户时传用户id
     * @return
     */
    @PostMapping("/getConversionRateDataList")
    public Object getConversionRateDataList(@RequestBody ConversionRateDataDTO dto){
        if(dto.getUserId() == null || dto.getUserId() == 0){
            String userId = request.getHeader("user-id");
            dto.setUserId(Integer.parseInt(userId));
        }
        String enterpriseId = request.getHeader("user-eid");
        dto.setEnterpriseId(Integer.parseInt(enterpriseId));

        if(dto.getTimeStart() == null || dto.getTimeEnd() == null){
            return MapUtils.create("no", 0, "msg", "参数异常");
        }

        if(dto.getType() == null){
            return MapUtils.create("no", 0, "msg", "参数异常");
        }

        if(dto.getType() == 2 && dto.getDeptId() == null){
            return MapUtils.create("no", 0, "msg", "参数异常");
        }

        return dataStatisticService.getConversionRateDataList(dto);
    }

    /**
     * 基础数据
     *      业绩
     *      邀约
     *      面试
     *      入职
     * @param type       1/个人 2/团队
     * @param deptId    部门id
     * @param timeType  时间类型 0/全部 1/日 2/周 3/月 4/年
     * @return
     */
    @PostMapping("/getBasicData")
    public Object getBasicData(@RequestBody BasicDataDTO dto){
        String userId = request.getHeader("user-id");
        String enterpriseId = request.getHeader("user-eid");
        if (userId == null || enterpriseId == null) {
            return MapUtils.create("no", 500, "msg", "非法登录，请确认后重试");
        }

        if(dto.getType() == null){
            return MapUtils.create("no", 0, "msg", "参数异常");
        }

        if(dto.getType() == 2 && dto.getDeptId() == null){
            return MapUtils.create("no", 0, "msg", "参数异常");
        }

        if(dto.getTimeType() == null){
            return MapUtils.create("no", 0, "msg", "参数异常");
        }

        dto.setUserId(Integer.parseInt(userId));
        dto.setEnterpriseId(Integer.parseInt(enterpriseId));
        return dataStatisticService.getBasicData(dto);
    }
    /**
     * 企业端
     * 基础数据
     * 推荐 邀约 面试 入职 结算
     * @param timeType  时间类型 0/全部 1/选择
     * @return
     */
    @PostMapping("/enterprise/getBasicData")
    public Object getEnterpriseBasicData(@RequestBody BasicDataDTO dto){
        String userId = request.getHeader("user-id");
        String enterpriseId = request.getHeader("user-eid");
        if (userId == null || enterpriseId == null) {
            return MapUtils.create("no", 500, "msg", "非法登录，请确认后重试");
        }

        if(dto.getTimeType() == null){
            return MapUtils.create("no", 0, "msg", "参数异常");
        }

        dto.setUserId(Integer.parseInt(userId));
        dto.setEnterpriseId(Integer.parseInt(enterpriseId));
        return dataStatisticService.getEnterpriseBasicData(dto);
    }

    /**
     * 业绩目标
     * @param date 日期
     * @param type       1/个人 2/团队
     * @param deptId    部门id
     * @return
     */
    @PostMapping("/getPerformanceTarget")
    public Object getPerformanceTarget(@RequestBody PerformanceTargetDTO dto){
        if(dto.getUserId() == null || dto.getUserId() == 0){
            String userId = request.getHeader("user-id");
            dto.setUserId(Integer.parseInt(userId));
        }
        String enterpriseId = request.getHeader("user-eid");
        dto.setEnterpriseId(Integer.parseInt(enterpriseId));

        if(dto.getType() == null){
            return MapUtils.create("no", 0, "msg", "参数异常");
        }

        if(dto.getType() == 2 && dto.getDeptId() == null){
            return MapUtils.create("no", 0, "msg", "参数异常");
        }

        if(dto.getDate() == null || dto.getDate().indexOf("-") == -1){
            return MapUtils.create("no", 0, "msg", "参数异常");
        }

        return dataStatisticService.getPerformanceTarget(dto);
    }

}
