package com.ssb.approve.controller;

import com.ssb.approve.entity.CommissionStrategy;
import com.ssb.approve.entity.CommissionStrategyDifficulty;
import com.ssb.approve.interceptor.annotation.RepeatSubmit;
import com.ssb.approve.model.CommissionStrategyModel;
import com.ssb.approve.model.CommissionStrategyRatioModel;
import com.ssb.approve.model.QueryCommissionStrategyModel;
import com.ssb.approve.service.CommissionStrategyService;
import com.ssb.approve.utils.MapUtils;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * @Author: Wang<PERSON><PERSON>yong
 * @Date: 2020/12/21 7:19 PM
 * @Description:提成策略
 */
@RestController
@RequestMapping("/api/commission")
public class CommissionStrategyController {
//
//    @Autowired
//    private HttpServletRequest request;
//
//    @Autowired
//    private CommissionStrategyService commissionStrategyService;
//
//    @ApiOperation(value = "获取提成策略列表")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "pageNum", value = "页码", dataType = "int", required = true, paramType = "query")
//    })
//    @GetMapping("/getStrategyList")
//    public Object getStrategyList(QueryCommissionStrategyModel query) {
//        //用户id
//        String userId = request.getHeader("user-id");
//        String enterpriseId = request.getHeader("user-eid");
//        if (userId == null || enterpriseId == null) {
//            return MapUtils.create("no", 500, "msg", "非法登录，请确认后重试");
//        }
//        query.setUserId(Integer.parseInt(userId));
//        query.setEnterpriseId(Integer.parseInt(enterpriseId));
//        if (query.getPageNum() == null || query.getPageNum() < 1) {
//            query.setPageNum(1);
//        }
//        return commissionStrategyService.getStrategyList(query);
//    }
//
//    @ApiOperation(value = "新建提成策略")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "title", value = "策略名称", dataType = "String", required = true, paramType = "query"),
//            @ApiImplicitParam(name = "cityLevel", value = "城市发展水平  0超一线   1新一线   2二线", dataType = "int", required = true, paramType = "query"),
//            @ApiImplicitParam(name = "newProjectTime", value = "新项目持续时间", dataType = "int", required = true, paramType = "query"),
//            @ApiImplicitParam(name = "followUpCompletedCount", value = "新项目跟进完成数", dataType = "int", required = true, paramType = "query"),
//            @ApiImplicitParam(name = "quota", value = "衡量指标", dataType = "int", required = true, paramType = "query")
//    })
//    @RepeatSubmit
//    @PostMapping("/saveStrategy")
//    public Object saveStrategy(@RequestBody CommissionStrategy commissionStrategy){
//        String userId = request.getHeader("user-id");
//        String enterpriseId = request.getHeader("user-eid");
//        if (userId == null || enterpriseId == null) {
//            return MapUtils.create("no", 500, "msg", "非法登录，请确认后重试");
//        }
//        if(commissionStrategy.getTitle().isEmpty() || commissionStrategy.getTitle().length()>10){
//            return MapUtils.create("no", 0, "msg", "策略名称异常");
//        }
//        if(commissionStrategy.getCityLevel() == null){
//            return MapUtils.create("no", 0, "msg", "发展水平不能为空");
//        }
//        if(commissionStrategy.getNewProjectTime() == null || commissionStrategy.getNewProjectTime() == 0){
//            return MapUtils.create("no", 0, "msg", "新项目持续时间异常");
//        }
//        if(commissionStrategy.getFollowUpCompletedCount() == null || commissionStrategy.getFollowUpCompletedCount() < 6
//                || commissionStrategy.getFollowUpCompletedCount() > 99){
//            return MapUtils.create("no", 0, "msg", "新项目跟进完成数异常");
//        }
//        if(commissionStrategy.getQuota() == null || commissionStrategy.getQuota() < 6
//                || commissionStrategy.getQuota() > 100){
//            return MapUtils.create("no", 0, "msg", "衡量指标异常");
//        }
//        commissionStrategy.setCreateBy(Integer.parseInt(userId));
//        commissionStrategy.setEnterpriseId(Integer.parseInt(enterpriseId));
//        return commissionStrategyService.insertStrategy(commissionStrategy);
//    }
//
//    @ApiOperation(value = "编辑策略")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "id", value = "id", dataType = "int", required = true, paramType = "query"),
//            @ApiImplicitParam(name = "type", value = "根据难度控制比例  0/否  1/是", dataType = "int", required = false, paramType = "query"),
//            @ApiImplicitParam(name = "title", value = "策略名称", dataType = "String", required = false, paramType = "query"),
//            @ApiImplicitParam(name = "cityLevel", value = "城市发展水平  0超一线   1新一线   2二线", dataType = "int", required = false, paramType = "query"),
//            @ApiImplicitParam(name = "newProjectTime", value = "新项目持续时间", dataType = "int", required = false, paramType = "query"),
//            @ApiImplicitParam(name = "followUpCompletedCount", value = "新项目跟进完成数", dataType = "int", required = false, paramType = "query"),
//            @ApiImplicitParam(name = "quota", value = "衡量指标", dataType = "int", required = false, paramType = "query")
//    })
//    @PostMapping("/editStrategy")
//    public Object editStrategy(@RequestBody CommissionStrategyModel commissionStrategy){
//        String userId = request.getHeader("user-id");
//        String enterpriseId = request.getHeader("user-eid");
//        if (userId == null || enterpriseId == null) {
//            return MapUtils.create("no", 500, "msg", "非法登录，请确认后重试");
//        }
//        if(commissionStrategy.getId() == null){
//            return MapUtils.create("no", 0, "msg", "参数异常");
//        }
//        if(commissionStrategy.getTitle() == null && commissionStrategy.getType() == null){
//            return MapUtils.create("no", 0, "msg", "参数异常");
//        }
//        if(commissionStrategy.getTitle() != null && commissionStrategy.getTitle().length()>10){
//            return MapUtils.create("no", 0, "msg", "策略名称异常");
//        }
//        if(commissionStrategy.getNewProjectTime() != null && commissionStrategy.getNewProjectTime() == 0){
//            return MapUtils.create("no", 0, "msg", "新项目持续时间异常");
//        }
//        if(commissionStrategy.getFollowUpCompletedCount() != null && (commissionStrategy.getFollowUpCompletedCount() < 6
//                || commissionStrategy.getFollowUpCompletedCount() > 99)){
//            return MapUtils.create("no", 0, "msg", "新项目跟进完成数异常");
//        }
//        if(commissionStrategy.getQuota() != null && (commissionStrategy.getQuota() < 6
//                || commissionStrategy.getQuota() > 100)){
//            return MapUtils.create("no", 0, "msg", "衡量指标异常");
//        }
//
//        commissionStrategy.setCreateBy(Integer.parseInt(userId));
//        commissionStrategy.setEnterpriseId(Integer.parseInt(enterpriseId));
//        return commissionStrategyService.updateStrategy(commissionStrategy);
//    }
//
//    @ApiOperation(value = "获取管理提成级别列表(新建用户)")
//    @GetMapping("/getCommissionLevelList")
//    public Object getCommissionLevelList() {
//        String enterpriseId = request.getHeader("user-eid");
//        if (enterpriseId == null) {
//            return MapUtils.create("no", 500, "msg", "非法登录，请确认后重试");
//        }
//        return commissionStrategyService.getCommissionLevelList(Integer.parseInt(enterpriseId));
//    }
//
//    @ApiOperation(value = "获取管理提成级别列表(提成策略提成比例列表)")
//    @GetMapping("/getCommissionLevelListV2")
//    public Object getCommissionLevelListV2() {
//        String enterpriseId = request.getHeader("user-eid");
//        if (enterpriseId == null) {
//            return MapUtils.create("no", 500, "msg", "非法登录，请确认后重试");
//        }
//        return commissionStrategyService.getCommissionLevelListV2(Integer.parseInt(enterpriseId));
//    }
//
//    @ApiOperation(value = "提成比例修改")
//    @PostMapping("/saveStrategyRatio")
//    public Object saveStrategyRatio(@RequestBody List<CommissionStrategyRatioModel> list){
//        String userId = request.getHeader("user-id");
//        if (userId == null) {
//            return MapUtils.create("no", 500, "msg", "非法登录，请确认后重试");
//        }
//        if(list == null || list.isEmpty()){
//            return MapUtils.create("no", 0, "msg", "参数异常");
//        }
//
//        return commissionStrategyService.saveStrategyRatio(list, Integer.parseInt(userId));
//    }
//
//    @ApiOperation(value = "难度设置")
//    @PostMapping("/saveStrategyDifficulty")
//    public Object saveStrategyDifficulty(@RequestBody CommissionStrategyDifficulty difficulty){
//        String userId = request.getHeader("user-id");
//        if (userId == null) {
//            return MapUtils.create("no", 500, "msg", "非法登录，请确认后重试");
//        }
//        if(difficulty.getBase() == null || difficulty.getHigh() == null || difficulty.getLow() == null){
//            return MapUtils.create("no", 0, "msg", "参数异常");
//        }
//
//        difficulty.setCreateBy(Integer.parseInt(userId));
//        return commissionStrategyService.saveStrategyDifficulty(difficulty);
//    }
//
//    @ApiOperation(value = "提成策略详情")
//    @GetMapping("/getStrategyInfo")
//    public Object getStrategyInfo(Integer id){
//        if(id == null){
//            return MapUtils.create("no", 0, "msg", "参数异常");
//        }
//        return commissionStrategyService.getStrategyInfo(id);
//    }
//
//
//    @ApiOperation(value = "删除提成策略")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "id", value = "id", dataType = "int", required = true, paramType = "query")
//    })
//    @PostMapping("/deleteStrategy")
//    public Object deleteStrategy(Integer id){
//        String userId = request.getHeader("user-id");
//        String enterpriseId = request.getHeader("user-eid");
//        if (userId == null || enterpriseId == null) {
//            return MapUtils.create("no", 500, "msg", "非法登录，请确认后重试");
//        }
//        if(id == null){
//            return MapUtils.create("no", 0, "msg", "参数异常");
//        }
//        return commissionStrategyService.deleteStrategy(id, Integer.parseInt(userId));
//    }
//
//
//    @ApiOperation(value = "添加销售提成削减比例")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "id", value = "提成策略id", dataType = "int", required = true, paramType = "query"),
//            @ApiImplicitParam(name = "commission", value = "削减比例", dataType = "String", required = true, paramType = "query")
//    })
//    @PostMapping("/saveSaleCommissionCut")
//    public Object saveSaleCommissionCut(Integer id, String commission){
//        String userId = request.getHeader("user-id");
//        String enterpriseId = request.getHeader("user-eid");
//        if (userId == null || enterpriseId == null) {
//            return MapUtils.create("no", 500, "msg", "非法登录，请确认后重试");
//        }
//        if(id == null){
//            return MapUtils.create("no", 0, "msg", "参数异常");
//        }
//
//        if(commission == null || commission.indexOf(",") == -1){
//            return MapUtils.create("no", 0, "msg", "参数异常");
//        }
//
//        return commissionStrategyService.saveSaleCommissionCut(id, Integer.parseInt(userId), Integer.parseInt(enterpriseId), commission);
//    }
//
//    @ApiOperation(value = "提成策略详情-难度列表")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "id", value = "策略id", dataType = "int", required = true, paramType = "query"),
//            @ApiImplicitParam(name = "industryId", value = "行业id", dataType = "int", required = false, paramType = "query"),
//            @ApiImplicitParam(name = "positionId", value = "岗位id", dataType = "int", required = false, paramType = "query")
//    })
//    @GetMapping("/getStrategyDifficultyList")
//    public Object getStrategyDifficultyList(Integer id, Integer industryId, Integer positionId){
//        String userId = request.getHeader("user-id");
//        if (userId == null) {
//            return MapUtils.create("no", 500, "msg", "非法登录，请确认后重试");
//        }
//        if(id == null){
//            return MapUtils.create("no", 0, "msg", "参数异常");
//        }
//        return commissionStrategyService.getStrategyDifficultyList(id, Integer.parseInt(userId), industryId, positionId);
//    }
//
//    @ApiOperation(value = "行业岗位修改 对应提成策略难度修改")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "userId", value = "用户id", dataType = "int", required = true, paramType = "query"),
//            @ApiImplicitParam(name = "industryFirstId", value = "一级行业id", dataType = "int", required = true, paramType = "query"),
//            @ApiImplicitParam(name = "industryId", value = "二级行业id", dataType = "int", required = true, paramType = "query"),
//            @ApiImplicitParam(name = "positionIndustryList", value = "行业岗位", dataType = "int", required = true, paramType = "query")
//    })
//    @PostMapping("/updateStrategyDifficultyList")
//    public Object updateStrategyDifficultyList(@RequestBody Map<String,Object> map){
//
//        return commissionStrategyService.updateStrategyDifficultyList(map);
//    }
//
//
//
//
//
//
//
//
//
//
//

}
