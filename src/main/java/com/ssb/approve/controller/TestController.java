package com.ssb.approve.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ssb.approve.service.CommissionGaveLogService;
import com.ssb.approve.service.FollowCommissionService;
import com.ssb.approve.service.client.ProjectService;
import com.ssb.approve.service.client.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;

/**
 * Created by violence on 2019/12/19.
 */
@RestController
@RequestMapping("/api/test")
public class TestController {

    @Autowired
    private ProjectService projectService;

    @Autowired
    private CommissionGaveLogService commissionGaveLogService;

    @Autowired
    private FollowCommissionService followCommissionService;

    @GetMapping("/aaa")
    public Object getTest () {
        System.out.println("aaaaa");
        return null;
    }

    /**
     * 获取结算详情
     *
     * @param id
     * @return
     */
    @GetMapping("/getFeignToProject")
    public Object getFeignToProject(Integer id) {
        Object object = projectService.getSettleDetails(id);
        JSONObject jsonObjectResult = JSONObject.parseObject(JSON.toJSON(object).toString());
        if (jsonObjectResult.getInteger("no") != null && jsonObjectResult.getInteger("no").equals(200)) {
            System.out.println(jsonObjectResult.getJSONArray("obj"));
        }
        return object;
    }

    /**
     * 获取项目详情
     *
     * @param id
     * @return
     */
    @GetMapping("/getProjectInfo")
    public Object getProjectInfo(Integer id) {
        Object result = projectService.getProjectInfo(id);
        JSONObject jsonObjectResult = JSONObject.parseObject(JSON.toJSON(result).toString());

        System.out.println(jsonObjectResult);
        return null;
    }


    @GetMapping("/testFollowCommission")
    public Object testFollowCommission(Integer followId, Integer projectId) {
        followCommissionService.saveFollowCommission(followId, projectId,null);
        return "success";
    }

    @GetMapping("/testGaveCommission")
    public Object testGaveCommission(Integer settleId, BigDecimal payment) {
        commissionGaveLogService.gaveCommission(settleId, payment);
        return "success";
    }

}
