package com.ssb.approve.controller;

import com.ssb.approve.entity.CrmFollowRecommend;
import com.ssb.approve.service.InnerService;
import com.ssb.approve.service.QuitService;
import com.ssb.approve.utils.MapUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @ClassName: InnerServiceController
 * @Description: 内部服务 controller
 * @Author: YZK
 * @Date: 2020年02月20日08:47:02
 **/
@Api(tags = "内部服务Controller", value = "内部服务API")
@RestController
@RequestMapping("/inner-api/followUp")
public class InnerServiceController {

    @Autowired
    private InnerService innerService;

    @Autowired
    private QuitService quitService;

    /**
     * 获取联系人集合
     * 招聘跟进查询
     * <p>
     * List<Integer> releaseIds    要释放到公海的contact_id集合
     * List<Integer> handoverIds     要进入交接人私有库的contact_id集合
     * Integer toUserId  交接人id
     * Integer fromUserId  离职人id
     *
     * @param fromUserId    离职人
     * @return
     */
    @ApiOperation(value = "获取联系人集合 招聘跟进查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "fromUserId", value = "离职人id", dataType = "int", required = true, paramType = "query")
    })
    @PostMapping("/getContactsFromFollowUp")
    public Object getContactsFromFollowUp(Integer fromUserId) {
        //数据判断
        if (fromUserId == null) {
            return MapUtils.create("no", 0, "msg", "离职人不能给空");
        }
        return innerService.getContactsFromFollowUp(fromUserId);
    }

    /**
     * 更新离职招聘跟进
     * 替换对接人
     *
     * @param fromUserId
     * @param toUserId
     * @return
     */
    @ApiOperation(value = "更新离职招聘跟进 替换对接人")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "fromUserId", value = "离职人id", dataType = "int", required = true, paramType = "query"),
            @ApiImplicitParam(name = "toUserId", value = "对接人", dataType = "int", required = true, paramType = "query")
    })
    @PostMapping("/updateQuitFollowUp")
    public Object updateQuitFollowUp(Integer eid, Integer fromUserId, Integer toUserId, Integer uid) {
        //参数判断
        if (fromUserId == null || toUserId == null) {
            return MapUtils.create("no", 0, "msg", "离职人、对接人不能给空");
        }
        return quitService.updateQuitFollowUp(eid, fromUserId, toUserId, uid);
    }

    /**
     * 公海页面的跟进数量
     */
    @PostMapping("/getProjectsNotInvolvedFollowCount")
    public Object getProjectsNotInvolvedFollowCount(@RequestBody List<Map<String, Object>> list){
        return innerService.getProjectsNotInvolvedFollowCount(list);
    }


    /**
     * 我参与的页面的跟进数量
     */
    @PostMapping("/getProjectsInvolvedFollowCount")
    public Object getProjectsInvolvedFollowCount(@RequestBody List<Map<String, Object>> list){
        return innerService.getProjectsInvolvedFollowCount(list);
    }

    /**
     * 我负责的页面的跟进数量
     */
    @PostMapping("/getResponsibleProjectFollowCount")
    public Object getResponsibleProjectFollowCount(@RequestBody List<Map<String, Object>> list){
        return innerService.getResponsibleProjectFollowCount(list);
    }

    /**
     * 未合作的页面的跟进数量
     */
    @PostMapping("/getExternalOrdersFollowCount")
    public Object getExternalOrdersFollowCount(@RequestBody List<Map<String, Object>> list){
        return innerService.getExternalOrdersFollowCount(list);
    }


    @PostMapping("/getProjectsRecommendCount")
    public Object getProjectsRecommendCount(@RequestBody Map<String, Object> params) {
        return innerService.getProjectsRecommendCount(params);
    }


    @PostMapping("/getFollowUpRecRemark")
    public Object getFollowUpRecRemark(Integer projectId, Integer customerEnterpriseId, Integer resumeId) {
        List<CrmFollowRecommend> list = innerService.getFollowUpRecRemark(projectId, customerEnterpriseId, resumeId);
        if (!CollectionUtils.isEmpty(list)) {
            return MapUtils.create("no", 1, "msg", list.stream().sorted(Comparator.comparing(CrmFollowRecommend::getId).reversed()).map(CrmFollowRecommend::getRemark).findFirst().orElse(""));
        }
        return MapUtils.create("no", 1, "msg", "");
    }

    @PostMapping("/getStatisticalData")
    public Object getStatisticalData(Integer uid, Integer eid, Date startTime, Date endTime) {
        return innerService.getStatisticalData(uid, eid, startTime, endTime);
    }
}
