package com.ssb.approve.controller;

import com.ssb.approve.entity.PerformanceTarget;
import com.ssb.approve.model.dto.PerformanceTargetDTO;
import com.ssb.approve.service.PerformanceTargetService;
import com.ssb.approve.utils.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * 业绩目标
 */
@RestController
@RequestMapping("/api/performanceTarget")
public class PerformanceTargetController {

    @Autowired
    private HttpServletRequest request;

    @Autowired
    private PerformanceTargetService performanceTargetService;

    /**
     * 保存业绩目标
     * @param performance 业绩
     * @return 是否保存成功
     */
    @PostMapping("/save")
    public Object save(@RequestBody PerformanceTarget performanceTarget) {
        String userId = request.getHeader("user-id");
        performanceTarget.setUserId(Integer.parseInt(userId));

        if(performanceTarget.getYear() == null || performanceTarget.getMonth() == null){
            return MapUtils.create("no", 0, "msg", "参数异常");
        }

        return performanceTargetService.save(performanceTarget);
    }

} 