package com.ssb.approve.utils;

import com.ssb.approve.common.constants.CacheConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * @ClassName: RedisLockUtils
 * @Description: redis锁 工具类
 * @Author: YZK
 * @Date: 2020年01月13日09:13:18
 **/
@Component
public class RedisLockUtils {

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    /**
     * 数据加锁
     *
     * @param type   1 面试  2 入职  3 结算  4 发offer
     * @param dataId 数据id
     * @param userId 用户id
     * @param status 1 加锁  2 解锁
     * @return   false 获取锁失败  true  加锁成功
     */
    public boolean lock(Integer type, Integer dataId, Integer userId, Integer status) {
        String typeStr = "";
        // 数据类型
        if (type == 1) {
            typeStr = "interview_";
        } else if (type == 2) {
            typeStr = "entry_";
        } else if (type == 3) {
            typeStr = "settle_";
        } else if (type == 4) {
            typeStr = "onlyOffer_";
        }
        //key
        String key = CacheConstants.FOLLOWUP_KEY + typeStr + dataId;
        //加锁
        if (status == 1) {
            //校验是否执行
            boolean isSuccess = stringRedisTemplate.opsForValue().setIfAbsent(key, userId.toString());
            if (!isSuccess) {
                return false;
            }
            //设置有效期  防止击穿  5分钟
            stringRedisTemplate.expire(key, 5, TimeUnit.MINUTES);
        }
        //解锁
        if (status == 2) {
            stringRedisTemplate.delete(key);
        }
        return true;
    }
}
