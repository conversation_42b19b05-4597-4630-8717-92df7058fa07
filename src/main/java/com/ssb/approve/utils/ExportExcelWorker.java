package com.ssb.approve.utils;

import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.util.CellRangeAddress;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.util.List;
import java.util.UUID;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

public class ExportExcelWorker {

    private String filepath = "";
    private String fileName = "";

    public ExportExcelWorker(String f) {
        filepath = f;
    }

    public void setFileName(String name) {
        fileName = name;
    }

    public ExportExcelWorker(){}

    private void createTitle(HSSFWorkbook workbook, HSSFSheet sheet, String[] titles) {
        HSSFRow row = sheet.createRow(0);
        //设置列宽，setColumnWidth的第二个参数要乘以256，这个参数的单位是1/256个字符宽度
        sheet.setColumnWidth(2, 12 * 256);
        sheet.setColumnWidth(3, 17 * 256);

        //设置为居中加粗
        HSSFCellStyle style = workbook.createCellStyle();
        HSSFFont font = workbook.createFont();
        font.setBold(true);
        style.setFont(font);

        HSSFCell cell;
        for (int i = 0; i < titles.length; i++) {
            cell = row.createCell(i);
            cell.setCellValue(titles[i]);
            cell.setCellStyle(style);
        }
    }

    public String genWorkbookReturnString(String[] titles, String[][] rws) {
        HSSFWorkbook workbook = new HSSFWorkbook();
        HSSFSheet sheet = workbook.createSheet("item1");
        createTitle(workbook, sheet, titles);

        for (int i = 0; i < rws.length; i++) {
            addRow(sheet, i + 1, rws[i]);
        }

        String fn = "";
        if(fileName == ""){
            fn = UUID.randomUUID().toString() + ".xls";
        }else{
            fn = fileName + ".xls";
        }
        String rfn = filepath + fn;
        OutputStream os = null;

        File dir = new File(filepath);
        File file = new File(rfn);
        try {
            if (!dir.exists())
                dir.mkdirs();
            if (file.exists()) {
                file.delete();
                file.createNewFile();
            }
            os = new FileOutputStream(file);
            workbook.write(os);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                if (os != null)
                    os.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return rfn;
    }

    public File genWorkbook(String[] titles, String[][] rws) {
        HSSFWorkbook workbook = new HSSFWorkbook();
        HSSFSheet sheet = workbook.createSheet("item1");
        createTitle(workbook, sheet, titles);

        for (int i = 0; i < rws.length; i++) {
            addRow(sheet, i + 1, rws[i]);
        }

        String fn = "";
        if(fileName == ""){
            fn = UUID.randomUUID().toString() + ".xls";
        }else{
            fn = fileName + ".xls";
        }
        String rfn = filepath + fn;
        OutputStream os = null;

        File dir = new File(filepath);
        File file = new File(rfn);
        try {
            if (!dir.exists())
                dir.mkdirs();
            if (file.exists()) {
                file.delete();
                file.createNewFile();
            }
            os = new FileOutputStream(file);
            workbook.write(os);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                if (os != null)
                    os.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return file;
    }

    private void addRow(HSSFSheet sheet, int r, String[] rs) {
        HSSFRow row = sheet.createRow(r);
        for (int i = 0; i < rs.length; i++) {
            row.createCell(i).setCellValue(rs[i]);
        }
    }

    /**
     * 将多个Excel打包成zip文件
     * @param srcfile
     * @param zipfile
     */
    public static void zipFiles(List<File> srcfile, File zipfile) {
        byte[] buf = new byte[1024];
        try {
            // Create the ZIP file
            ZipOutputStream out = new ZipOutputStream(new FileOutputStream(zipfile));
            // Compress the files
            for (int i = 0; i < srcfile.size(); i++) {
                File file = srcfile.get(i);
                FileInputStream in = new FileInputStream(file);
                // Add ZIP entry to output stream.
                out.putNextEntry(new ZipEntry(file.getName()));
                // Transfer bytes from the file to the ZIP file
                int len;
                while ((len = in.read(buf)) > 0) {
                    out.write(buf, 0, len);
                }
                // Complete the entry
                out.closeEntry();
                in.close();
            }
            // Complete the ZIP file
            out.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 删除多个文件方法
     *
     * @param srcfile
     */
    public static void deleteFiles(List<File> srcfile) {
        for (File file : srcfile) {
            if (file.exists()) {
                file.delete();
            }
        }
    }

    public static void downFile(HttpServletResponse response,String serverPath, String str) {
        try {
            String path = serverPath + str;
            File file = new File(path);
            if (file.exists()) {
                InputStream ins = new FileInputStream(path);
                BufferedInputStream bins = new BufferedInputStream(ins);// 放到缓冲流里面
                OutputStream outs = response.getOutputStream();// 获取文件输出IO流
                BufferedOutputStream bouts = new BufferedOutputStream(outs);
                response.setContentType("application/x-download");// 设置response内容的类型
                response.setHeader(
                        "Content-disposition",
                        "attachment;filename="
                                + URLEncoder.encode(str, "GBK"));// 设置头部信息
                int bytesRead = 0;
                byte[] buffer = new byte[8192];
                //开始向网络传输文件流
                while ((bytesRead = bins.read(buffer, 0, 8192)) != -1) {
                    bouts.write(buffer, 0, bytesRead);
                }
                bouts.flush();// 这里一定要调用flush()方法
                ins.close();
                bins.close();
                outs.close();
                bouts.close();
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 删除zip
     */
    public static void deleteZip(File path) {
        if (path.exists()) {
            path.delete();
        }
    }



    public String WorkbookCheckSlip(String[] titles, String[][] rws) {
        HSSFWorkbook workbook = new HSSFWorkbook();
        HSSFSheet sheet = workbook.createSheet("item1");
        createTitle(workbook, sheet, titles);
        for (int i = 0; i < rws.length; i++) {
            addRow(sheet, i + 1, rws[i]);
        }
        for (int i = 0; i < 2; i++) {
            mergeRowCell(sheet,i, 1, rws.length, false, 0);
        }

        String fn = "";
        if(fileName == ""){
            fn = UUID.randomUUID().toString() + ".xls";
        }else{
            fn = fileName + ".xls";
        }
        String rfn = filepath + fn;
        OutputStream os = null;

        File dir = new File(filepath);
        File file = new File(rfn);
        try {
            if (!dir.exists())
                dir.mkdirs();
            if (file.exists()) {
                file.delete();
                file.createNewFile();
            }
            os = new FileOutputStream(file);
            workbook.write(os);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                if (os != null)
                    os.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return rfn;
    }
    /* @param sheet
     * @param colIdx	合并的列
     * @param startRow	起始行
     * @param stopRow	结束行
     * @param isForward	是否递进合并其它列
     * @param forwardToColIdx 递进到的列
     * 审核明细导出合并单元格
     */
    public void mergeRowCell(HSSFSheet sheet,int colIdx,int startRow,int stopRow ,boolean isForward,int forwardToColIdx) {
        String compareValue = null;
        int beginRow = startRow;
        int endRow = startRow;
        for (int i = startRow; i <= stopRow; ++i) {
            String value = sheet.getRow(i).getCell(colIdx).getRichStringCellValue() == null ? "" : sheet.getRow(i).getCell(colIdx).getRichStringCellValue().toString();
            if (i == startRow) {
                compareValue = value;
            } else {
                if (compareValue.equals(value)) {//相同，则设置重复的值为空
                    sheet.getRow(i).getCell(colIdx).setCellValue("");
                    endRow = i;
                } else {//不同，则合并之前相同的单元格
                    if (beginRow < endRow) {
                        CellRangeAddress cellRangeAddress = new CellRangeAddress(beginRow, endRow, colIdx, colIdx);
                        sheet.addMergedRegion(cellRangeAddress);
                        if (isForward) {//递进合并下一列
                            int nextColIndex = colIdx;
                            if (colIdx < forwardToColIdx) {
                                nextColIndex++;
                            } else if (colIdx > forwardToColIdx) {
                                nextColIndex--;
                            } else {
                                return;
                            }
                            mergeRowCell(sheet, nextColIndex, beginRow, endRow, isForward, forwardToColIdx);
                        }
                    }

                    compareValue = value;
                    beginRow = i;
                    endRow = i;
                }
            }
        }
        if(beginRow < endRow){
            CellRangeAddress cellRangeAddress = new CellRangeAddress(beginRow, endRow, colIdx, colIdx);
            sheet.addMergedRegion(cellRangeAddress);
            if(isForward){//递进合并下一列
                int nextColIndex = colIdx;
                if(colIdx < forwardToColIdx){
                    nextColIndex ++;
                }else if(colIdx > forwardToColIdx){
                    nextColIndex --;
                }else{
                    return;
                }
                mergeRowCell(sheet, nextColIndex, beginRow, endRow, isForward, forwardToColIdx);
            }
        }
    }
}
