package com.ssb.approve.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class WeChatUtil {

    private static final String WX_APP_ID = "wx6cd3b036a5134b4a";
    private static final String WX_APP_SECRET = "ef0cfce70d604ed2a56a9ed7851c7f3e";
    private static final String GET_ACCESS_TOKEN_URL = "https://api.weixin.qq.com/cgi-bin/token";
    private static final String GENERATE_URL_LINK = "https://api.weixin.qq.com/wxa/generate_urllink?access_token=";

    // Redis key for access token
    private static final String ACCESS_TOKEN_REDIS_KEY = "wechat:access_token";

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    private final RestTemplate restTemplate = new RestTemplate();

    /**
     * 获取微信access_token
     * 先从Redis中获取，如果不存在或已过期则重新获取并存储到Redis
     *
     * @return access_token
     */
    public String getAccessToken() {
        try {
            // 先从Redis中获取
            String cachedToken = stringRedisTemplate.opsForValue().get(ACCESS_TOKEN_REDIS_KEY);
            if (cachedToken != null && !cachedToken.isEmpty()) {
                log.info("从Redis获取到access_token: {}", cachedToken);
                return cachedToken;
            }

            // Redis中没有或已过期，重新获取
            log.info("Redis中没有access_token，开始重新获取");
            return refreshAccessToken();

        } catch (Exception e) {
            log.error("获取access_token失败", e);
            return null;
        }
    }

    /**
     * 刷新access_token
     * 调用微信API获取新的access_token并存储到Redis
     *
     * @return access_token
     */
    private String refreshAccessToken() {
        try {
            // 构建请求URL
            String url = String.format("%s?grant_type=client_credential&appid=%s&secret=%s",
                    GET_ACCESS_TOKEN_URL, WX_APP_ID, WX_APP_SECRET);

            log.info("调用微信API获取access_token: {}", url);

            // 发送HTTP GET请求
            String response = restTemplate.getForObject(url, String.class);
            log.info("微信API响应: {}", response);

            if (response == null || response.isEmpty()) {
                log.error("微信API响应为空");
                return null;
            }

            // 解析响应
            JSONObject jsonResponse = JSON.parseObject(response);

            // 检查是否有错误
            if (jsonResponse.containsKey("errcode")) {
                Integer errcode = jsonResponse.getInteger("errcode");
                String errmsg = jsonResponse.getString("errmsg");
                log.error("微信API返回错误: errcode={}, errmsg={}", errcode, errmsg);
                return null;
            }

            // 获取access_token和过期时间
            String accessToken = jsonResponse.getString("access_token");
            Integer expiresIn = jsonResponse.getInteger("expires_in");

            if (accessToken == null || accessToken.isEmpty()) {
                log.error("微信API返回的access_token为空");
                return null;
            }

            // 存储到Redis，设置过期时间（提前5分钟过期以避免边界问题）
            long expireTime = expiresIn != null ? expiresIn - 300 : 7200 - 300; // 默认7200秒-300秒
            stringRedisTemplate.opsForValue().set(ACCESS_TOKEN_REDIS_KEY, accessToken, expireTime, TimeUnit.SECONDS);

            log.info("成功获取并缓存access_token: {}, 过期时间: {}秒", accessToken, expireTime);
            return accessToken;

        } catch (Exception e) {
            log.error("刷新access_token失败", e);
            return null;
        }
    }

    /**
     * 强制刷新access_token
     * 清除Redis缓存并重新获取
     *
     * @return access_token
     */
    public String forceRefreshAccessToken() {
        try {
            // 删除Redis中的缓存
            stringRedisTemplate.delete(ACCESS_TOKEN_REDIS_KEY);
            log.info("已清除Redis中的access_token缓存");

            // 重新获取
            return refreshAccessToken();

        } catch (Exception e) {
            log.error("强制刷新access_token失败", e);
            return null;
        }
    }
}
