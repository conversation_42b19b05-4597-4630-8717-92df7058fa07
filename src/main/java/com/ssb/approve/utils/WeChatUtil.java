package com.ssb.approve.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class WeChatUtil {

    private static final String WX_APP_ID = "wx6cd3b036a5134b4a";
    private static final String WX_APP_SECRET = "ef0cfce70d604ed2a56a9ed7851c7f3e";
    private static final String GET_ACCESS_TOKEN_URL = "https://api.weixin.qq.com/cgi-bin/token";
    private static final String GENERATE_URL_LINK_URL = "https://api.weixin.qq.com/wxa/generate_urllink";

    // Redis key for access token
    private static final String ACCESS_TOKEN_REDIS_KEY = "wechat:access_token";

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    private final RestTemplate restTemplate = new RestTemplate();

    /**
     * 获取微信access_token
     * 先从Redis中获取，如果不存在或已过期则重新获取并存储到Redis
     *
     * @return access_token
     */
    public String getAccessToken() {
        try {
            // 先从Redis中获取
            String cachedToken = stringRedisTemplate.opsForValue().get(ACCESS_TOKEN_REDIS_KEY);
            if (cachedToken != null && !cachedToken.isEmpty()) {
                log.info("从Redis获取到access_token: {}", cachedToken);
                return cachedToken;
            }

            // Redis中没有或已过期，重新获取
            log.info("Redis中没有access_token，开始重新获取");
            return refreshAccessToken();

        } catch (Exception e) {
            log.error("获取access_token失败", e);
            return null;
        }
    }

    /**
     * 刷新access_token
     * 调用微信API获取新的access_token并存储到Redis
     *
     * @return access_token
     */
    private String refreshAccessToken() {
        try {
            // 构建请求URL
            String url = String.format("%s?grant_type=client_credential&appid=%s&secret=%s",
                    GET_ACCESS_TOKEN_URL, WX_APP_ID, WX_APP_SECRET);

            log.info("调用微信API获取access_token: {}", url);

            // 发送HTTP GET请求
            String response = restTemplate.getForObject(url, String.class);
            log.info("微信API响应: {}", response);

            if (response == null || response.isEmpty()) {
                log.error("微信API响应为空");
                return null;
            }

            // 解析响应
            JSONObject jsonResponse = JSON.parseObject(response);

            // 检查是否有错误
            if (jsonResponse.containsKey("errcode")) {
                Integer errcode = jsonResponse.getInteger("errcode");
                String errmsg = jsonResponse.getString("errmsg");
                log.error("微信API返回错误: errcode={}, errmsg={}", errcode, errmsg);
                return null;
            }

            // 获取access_token和过期时间
            String accessToken = jsonResponse.getString("access_token");
            Integer expiresIn = jsonResponse.getInteger("expires_in");

            if (accessToken == null || accessToken.isEmpty()) {
                log.error("微信API返回的access_token为空");
                return null;
            }

            // 存储到Redis，设置过期时间（提前5分钟过期以避免边界问题）
            long expireTime = expiresIn != null ? expiresIn - 300 : 7200 - 300; // 默认7200秒-300秒
            stringRedisTemplate.opsForValue().set(ACCESS_TOKEN_REDIS_KEY, accessToken, expireTime, TimeUnit.SECONDS);

            log.info("成功获取并缓存access_token: {}, 过期时间: {}秒", accessToken, expireTime);
            return accessToken;

        } catch (Exception e) {
            log.error("刷新access_token失败", e);
            return null;
        }
    }

    /**
     * 强制刷新access_token
     * 清除Redis缓存并重新获取
     *
     * @return access_token
     */
    public String forceRefreshAccessToken() {
        try {
            // 删除Redis中的缓存
            stringRedisTemplate.delete(ACCESS_TOKEN_REDIS_KEY);
            log.info("已清除Redis中的access_token缓存");

            // 重新获取
            return refreshAccessToken();

        } catch (Exception e) {
            log.error("强制刷新access_token失败", e);
            return null;
        }
    }

    /**
     * 生成微信小程序URL Link
     *
     * @param path 小程序页面路径，必须是已经发布的小程序存在的页面
     * @param query 小程序页面的query，最多1024个字符，只支持数字，大小写英文以及部分特殊字符
     * @param expireType URL Link失效类型，失效时间：0，失效间隔天数：1
     * @param expireTime 到期失效的URL Link的失效时间，为Unix时间戳。生成的到期失效URL Link在该时间前有效。最长有效期为30天。expire_type为0必填
     * @param expireInterval 到期失效的URL Link的失效间隔天数。生成的到期失效URL Link在该间隔时间到达前有效，最长间隔天数为30天。expire_type为1必填
     * @return GenerateUrlLinkResponse 包含生成的URL Link或错误信息
     */
    public GenerateUrlLinkResponse generateUrlLink(String path, String query, Integer expireType,
                                                  Long expireTime, Integer expireInterval) {
        try {
            // 获取access_token
            String accessToken = getAccessToken();
            if (accessToken == null || accessToken.isEmpty()) {
                log.error("获取access_token失败，无法生成URL Link");
                return GenerateUrlLinkResponse.error(-1, "获取access_token失败");
            }

            // 构建请求URL
            String url = GENERATE_URL_LINK_URL + "?access_token=" + accessToken;

            // 构建请求参数
            GenerateUrlLinkRequest request = new GenerateUrlLinkRequest();
            request.setPath(path);
            request.setQuery(query);
            request.setExpire_type(expireType);
            request.setExpire_time(expireTime);
            request.setExpire_interval(expireInterval);

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            // 创建请求实体
            HttpEntity<String> entity = new HttpEntity<>(JSON.toJSONString(request), headers);

            log.info("调用微信API生成URL Link: {}, 请求参数: {}", url, JSON.toJSONString(request));

            // 发送POST请求
            String response = restTemplate.postForObject(url, entity, String.class);
            log.info("微信API响应: {}", response);

            if (response == null || response.isEmpty()) {
                log.error("微信API响应为空");
                return GenerateUrlLinkResponse.error(-1, "微信API响应为空");
            }

            // 解析响应
            JSONObject jsonResponse = JSON.parseObject(response);

            // 检查是否有错误
            if (jsonResponse.containsKey("errcode")) {
                Integer errcode = jsonResponse.getInteger("errcode");
                String errmsg = jsonResponse.getString("errmsg");

                if (errcode != 0) {
                    log.error("微信API返回错误: errcode={}, errmsg={}", errcode, errmsg);
                    return GenerateUrlLinkResponse.error(errcode, errmsg);
                }
            }

            // 获取生成的URL Link
            String urlLink = jsonResponse.getString("url_link");
            if (urlLink == null || urlLink.isEmpty()) {
                log.error("微信API返回的url_link为空");
                return GenerateUrlLinkResponse.error(-1, "生成的URL Link为空");
            }

            log.info("成功生成URL Link: {}", urlLink);
            return GenerateUrlLinkResponse.success(urlLink);

        } catch (Exception e) {
            log.error("生成URL Link失败", e);
            return GenerateUrlLinkResponse.error(-1, "生成URL Link失败: " + e.getMessage());
        }
    }

    /**
     * 生成微信小程序URL Link（简化版本）
     * 使用默认的失效时间（30天）
     *
     * @param path 小程序页面路径
     * @param query 小程序页面的query参数
     * @return GenerateUrlLinkResponse 包含生成的URL Link或错误信息
     */
    public GenerateUrlLinkResponse generateUrlLink(String path, String query) {
        // 默认使用失效间隔天数30天
        return generateUrlLink(path, query, 1, null, 30);
    }

    /**
     * 生成微信小程序URL Link（仅路径）
     * 使用默认的失效时间（30天），无query参数
     *
     * @param path 小程序页面路径
     * @return GenerateUrlLinkResponse 包含生成的URL Link或错误信息
     */
    public GenerateUrlLinkResponse generateUrlLink(String path) {
        return generateUrlLink(path, "", 1, null, 30);
    }

    /**
     * 生成URL Link请求参数类
     */
    @Data
    public static class GenerateUrlLinkRequest {
        private String path;
        private String query;
        private Integer expire_type;
        private Long expire_time;
        private Integer expire_interval;
    }

    /**
     * 生成URL Link响应类
     */
    @Data
    public static class GenerateUrlLinkResponse {
        private Integer errcode;
        private String errmsg;
        private String url_link;
        private boolean success;

        public static GenerateUrlLinkResponse success(String urlLink) {
            GenerateUrlLinkResponse response = new GenerateUrlLinkResponse();
            response.setSuccess(true);
            response.setErrcode(0);
            response.setErrmsg("ok");
            response.setUrl_link(urlLink);
            return response;
        }

        public static GenerateUrlLinkResponse error(Integer errcode, String errmsg) {
            GenerateUrlLinkResponse response = new GenerateUrlLinkResponse();
            response.setSuccess(false);
            response.setErrcode(errcode);
            response.setErrmsg(errmsg);
            response.setUrl_link(null);
            return response;
        }
    }
}
