package com.ssb.approve.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.util.DigestUtils;
import org.springframework.web.client.RestTemplate;

import java.nio.charset.StandardCharsets;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class WeChatUtil {

    private static final String WX_APP_ID = "wx6cd3b036a5134b4a";
    private static final String WX_APP_SECRET = "ef0cfce70d604ed2a56a9ed7851c7f3e";
    private static final String GET_ACCESS_TOKEN_URL = "https://api.weixin.qq.com/cgi-bin/token";
    private static final String GENERATE_URL_LINK_URL = "https://api.weixin.qq.com/wxa/generate_urllink";

    // Redis key for access token
    private static final String ACCESS_TOKEN_REDIS_KEY = "wechat:access_token";
    // Redis key prefix for URL Link
    private static final String URL_LINK_REDIS_KEY_PREFIX = "wechat:url_link:";

    // todo
    private static final String SMALL_PROGRAM_LINK = "www.baidu.com";

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    private final RestTemplate restTemplate = new RestTemplate();

    /**
     * 获取微信access_token
     * 先从Redis中获取，如果不存在或已过期则重新获取并存储到Redis
     *
     * @return access_token
     */
    public String getAccessToken() {
        try {
            // 先从Redis中获取
            String cachedToken = stringRedisTemplate.opsForValue().get(ACCESS_TOKEN_REDIS_KEY);
            if (cachedToken != null && !cachedToken.isEmpty()) {
                log.info("从Redis获取到access_token: {}", cachedToken);
                return cachedToken;
            }

            // Redis中没有或已过期，重新获取
            log.info("Redis中没有access_token，开始重新获取");
            return refreshAccessToken();

        } catch (Exception e) {
            log.error("获取access_token失败", e);
            return null;
        }
    }

    /**
     * 刷新access_token
     * 调用微信API获取新的access_token并存储到Redis
     *
     * @return access_token
     */
    private String refreshAccessToken() {
        try {
            // 构建请求URL
            String url = String.format("%s?grant_type=client_credential&appid=%s&secret=%s",
                    GET_ACCESS_TOKEN_URL, WX_APP_ID, WX_APP_SECRET);

            log.info("调用微信API获取access_token: {}", url);

            // 发送HTTP GET请求
            String response = restTemplate.getForObject(url, String.class);
            log.info("微信API响应: {}", response);

            if (response == null || response.isEmpty()) {
                log.error("微信API响应为空");
                return null;
            }

            // 解析响应
            JSONObject jsonResponse = JSON.parseObject(response);

            // 检查是否有错误
            if (jsonResponse.containsKey("errcode")) {
                Integer errcode = jsonResponse.getInteger("errcode");
                String errmsg = jsonResponse.getString("errmsg");
                log.error("微信API返回错误: errcode={}, errmsg={}", errcode, errmsg);
                return null;
            }

            // 获取access_token和过期时间
            String accessToken = jsonResponse.getString("access_token");
            Integer expiresIn = jsonResponse.getInteger("expires_in");

            if (accessToken == null || accessToken.isEmpty()) {
                log.error("微信API返回的access_token为空");
                return null;
            }

            // 存储到Redis，设置过期时间（提前5分钟过期以避免边界问题）
            long expireTime = expiresIn != null ? expiresIn - 300 : 7200 - 300; // 默认7200秒-300秒
            stringRedisTemplate.opsForValue().set(ACCESS_TOKEN_REDIS_KEY, accessToken, expireTime, TimeUnit.SECONDS);

            log.info("成功获取并缓存access_token: {}, 过期时间: {}秒", accessToken, expireTime);
            return accessToken;

        } catch (Exception e) {
            log.error("刷新access_token失败", e);
            return null;
        }
    }

    /**
     * 强制刷新access_token
     * 清除Redis缓存并重新获取
     *
     * @return access_token
     */
    public String forceRefreshAccessToken() {
        try {
            // 删除Redis中的缓存
            stringRedisTemplate.delete(ACCESS_TOKEN_REDIS_KEY);
            log.info("已清除Redis中的access_token缓存");

            // 重新获取
            return refreshAccessToken();

        } catch (Exception e) {
            log.error("强制刷新access_token失败", e);
            return null;
        }
    }

    /**
     * 生成微信小程序URL Link
     * 先从Redis中获取缓存的URL Link，如果不存在或即将过期则重新生成
     *
     * @param path 小程序页面路径，必须是已经发布的小程序存在的页面
     * @param query 小程序页面的query，最多1024个字符，只支持数字，大小写英文以及部分特殊字符
     * @param expireType URL Link失效类型，失效时间：0，失效间隔天数：1
     * @param expireTime 到期失效的URL Link的失效时间，为Unix时间戳。生成的到期失效URL Link在该时间前有效。最长有效期为30天。expire_type为0必填
     * @param expireInterval 到期失效的URL Link的失效间隔天数。生成的到期失效URL Link在该间隔时间到达前有效，最长间隔天数为30天。expire_type为1必填
     * @return GenerateUrlLinkResponse 包含生成的URL Link或错误信息
     */
    public GenerateUrlLinkResponse generateUrlLink(String path, String query, Integer expireType,
                                                  Long expireTime, Integer expireInterval) {
        try {
            // 生成缓存key
            String cacheKey = generateUrlLinkCacheKey(path, query, expireType, expireTime, expireInterval);

            // 先从Redis中获取缓存的URL Link
            String cachedUrlLinkJson = stringRedisTemplate.opsForValue().get(cacheKey);
            if (cachedUrlLinkJson != null && !cachedUrlLinkJson.isEmpty()) {
                try {
                    CachedUrlLink cachedUrlLink = JSON.parseObject(cachedUrlLinkJson, CachedUrlLink.class);
                    // 检查是否即将过期（提前5分钟刷新）
                    long currentTime = System.currentTimeMillis() / 1000;
                    if (cachedUrlLink.getExpireAt() > currentTime + 300) {
                        log.info("从Redis获取到有效的URL Link: {}", cachedUrlLink.getUrlLink());
                        return GenerateUrlLinkResponse.success(cachedUrlLink.getUrlLink());
                    } else {
                        log.info("Redis中的URL Link即将过期，需要重新生成");
                        // 删除即将过期的缓存
                        stringRedisTemplate.delete(cacheKey);
                    }
                } catch (Exception e) {
                    log.warn("解析缓存的URL Link失败，将重新生成: {}", e.getMessage());
                    stringRedisTemplate.delete(cacheKey);
                }
            }

            // Redis中没有有效的URL Link，重新生成
            log.info("开始生成新的URL Link");
            return generateAndCacheUrlLink(path, query, expireType, expireTime, expireInterval, cacheKey);

        } catch (Exception e) {
            log.error("生成URL Link失败", e);
            return GenerateUrlLinkResponse.error(-1, "生成URL Link失败: " + e.getMessage());
        }
    }

    /**
     * 生成并缓存URL Link
     */
    private GenerateUrlLinkResponse generateAndCacheUrlLink(String path, String query, Integer expireType,
                                                           Long expireTime, Integer expireInterval, String cacheKey) {
        try {
            // 获取access_token
            String accessToken = getAccessToken();
            if (accessToken == null || accessToken.isEmpty()) {
                log.error("获取access_token失败，无法生成URL Link");
                return GenerateUrlLinkResponse.error(-1, "获取access_token失败");
            }

            // 构建请求URL
            String url = GENERATE_URL_LINK_URL + "?access_token=" + accessToken;

            // 构建请求参数
            GenerateUrlLinkRequest request = new GenerateUrlLinkRequest();
            request.setPath(path);
            request.setQuery(query);
            request.setExpire_type(expireType);
            request.setExpire_time(expireTime);
            request.setExpire_interval(expireInterval);

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            // 创建请求实体
            HttpEntity<String> entity = new HttpEntity<>(JSON.toJSONString(request), headers);

            log.info("调用微信API生成URL Link: {}, 请求参数: {}", url, JSON.toJSONString(request));

            // 发送POST请求
            String response = restTemplate.postForObject(url, entity, String.class);
            log.info("微信API响应: {}", response);

            if (response == null || response.isEmpty()) {
                log.error("微信API响应为空");
                return GenerateUrlLinkResponse.error(-1, "微信API响应为空");
            }

            // 解析响应
            JSONObject jsonResponse = JSON.parseObject(response);

            // 检查是否有错误
            if (jsonResponse.containsKey("errcode")) {
                Integer errcode = jsonResponse.getInteger("errcode");
                String errmsg = jsonResponse.getString("errmsg");

                if (errcode != 0) {
                    log.error("微信API返回错误: errcode={}, errmsg={}", errcode, errmsg);
                    return GenerateUrlLinkResponse.error(errcode, errmsg);
                }
            }

            // 获取生成的URL Link
            String urlLink = jsonResponse.getString("url_link");
            if (urlLink == null || urlLink.isEmpty()) {
                log.error("微信API返回的url_link为空");
                return GenerateUrlLinkResponse.error(-1, "生成的URL Link为空");
            }

            // 计算过期时间并缓存到Redis
            long expireAt = calculateExpireTime(expireType, expireTime, expireInterval);
            cacheUrlLink(cacheKey, urlLink, expireAt);

            log.info("成功生成并缓存URL Link: {}", urlLink);
            return GenerateUrlLinkResponse.success(urlLink);

        } catch (Exception e) {
            log.error("生成并缓存URL Link失败", e);
            return GenerateUrlLinkResponse.error(-1, "生成URL Link失败: " + e.getMessage());
        }
    }

    /**
     * 生成缓存key
     */
    private String generateUrlLinkCacheKey(String path, String query, Integer expireType,
                                          Long expireTime, Integer expireInterval) {
        String keyContent = String.format("path=%s&query=%s&expire_type=%s&expire_time=%s&expire_interval=%s",
                path, query, expireType, expireTime, expireInterval);
        String md5 = DigestUtils.md5DigestAsHex(keyContent.getBytes(StandardCharsets.UTF_8));
        return URL_LINK_REDIS_KEY_PREFIX + md5;
    }

    /**
     * 计算过期时间
     */
    private long calculateExpireTime(Integer expireType, Long expireTime, Integer expireInterval) {
        long currentTime = System.currentTimeMillis() / 1000;

        if (expireType != null && expireType == 0 && expireTime != null) {
            // 使用指定的过期时间
            return expireTime;
        } else if (expireType != null && expireType == 1 && expireInterval != null) {
            // 使用间隔天数计算过期时间
            return currentTime + (expireInterval * 24 * 60 * 60);
        } else {
            // 默认30天
            return currentTime + (30 * 24 * 60 * 60);
        }
    }

    /**
     * 缓存URL Link到Redis
     */
    private void cacheUrlLink(String cacheKey, String urlLink, long expireAt) {
        try {
            CachedUrlLink cachedUrlLink = new CachedUrlLink();
            cachedUrlLink.setUrlLink(urlLink);
            cachedUrlLink.setExpireAt(expireAt);
            cachedUrlLink.setCreateAt(System.currentTimeMillis() / 1000);

            String cachedJson = JSON.toJSONString(cachedUrlLink);

            // 计算Redis过期时间（比URL Link过期时间提前5分钟，确保不会返回过期的链接）
            long currentTime = System.currentTimeMillis() / 1000;
            long redisExpireSeconds = expireAt - currentTime - 300; // 提前5分钟过期

            if (redisExpireSeconds > 0) {
                stringRedisTemplate.opsForValue().set(cacheKey, cachedJson, redisExpireSeconds, TimeUnit.SECONDS);
                log.info("URL Link已缓存到Redis，过期时间: {}秒后", redisExpireSeconds);
            } else {
                log.warn("URL Link即将过期，不进行缓存");
            }
        } catch (Exception e) {
            log.error("缓存URL Link到Redis失败", e);
        }
    }

    /**
     * 强制刷新URL Link
     * 清除Redis缓存并重新生成
     */
    public GenerateUrlLinkResponse forceRefreshUrlLink(String path, String query, Integer expireType,
                                                      Long expireTime, Integer expireInterval) {
        try {
            String cacheKey = generateUrlLinkCacheKey(path, query, expireType, expireTime, expireInterval);
            stringRedisTemplate.delete(cacheKey);
            log.info("已清除Redis中的URL Link缓存: {}", cacheKey);

            return generateAndCacheUrlLink(path, query, expireType, expireTime, expireInterval, cacheKey);
        } catch (Exception e) {
            log.error("强制刷新URL Link失败", e);
            return GenerateUrlLinkResponse.error(-1, "强制刷新URL Link失败: " + e.getMessage());
        }
    }

    /**
     * 生成微信小程序URL Link
     * 使用默认的失效时间（30天）
     *
     * @param query 小程序页面的query参数
     * @return GenerateUrlLinkResponse 包含生成的URL Link或错误信息
     */
    public GenerateUrlLinkResponse generateUrlLink(String query) {
        // 默认使用失效间隔天数30天
        return generateUrlLink(SMALL_PROGRAM_LINK, query, 1, null, 30);
    }

    /**
     * 缓存的URL Link数据类
     */
    @Data
    public static class CachedUrlLink {
        private String urlLink;    // URL Link
        private Long expireAt;     // 过期时间（Unix时间戳）
        private Long createAt;     // 创建时间（Unix时间戳）
    }

    /**
     * 生成URL Link请求参数类
     */
    @Data
    public static class GenerateUrlLinkRequest {
        private String path;
        private String query;
        private Integer expire_type;
        private Long expire_time;
        private Integer expire_interval;
    }

    /**
     * 生成URL Link响应类
     */
    @Data
    public static class GenerateUrlLinkResponse {
        private Integer errcode;
        private String errmsg;
        private String url_link;
        private boolean success;

        public static GenerateUrlLinkResponse success(String urlLink) {
            GenerateUrlLinkResponse response = new GenerateUrlLinkResponse();
            response.setSuccess(true);
            response.setErrcode(0);
            response.setErrmsg("ok");
            response.setUrl_link(urlLink);
            return response;
        }

        public static GenerateUrlLinkResponse error(Integer errcode, String errmsg) {
            GenerateUrlLinkResponse response = new GenerateUrlLinkResponse();
            response.setSuccess(false);
            response.setErrcode(errcode);
            response.setErrmsg(errmsg);
            response.setUrl_link(null);
            return response;
        }
    }
}
