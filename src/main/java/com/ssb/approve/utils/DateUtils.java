package com.ssb.approve.utils;

import org.apache.commons.lang3.time.DateFormatUtils;

import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Calendar;
import java.util.Date;

/**
 * @ClassName: DateUtils
 * @Description: 日期工具类
 * @Author: YZK
 * @Date: 2019年12月27日14:43:04
 **/
public class DateUtils extends org.apache.commons.lang3.time.DateUtils {

    private static String[] parsePatterns = { "yyyy-MM-dd", "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm",
            "yyyy/MM/dd", "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm" };

    /**
     * 得到日期字符串 默认格式（yyyy-MM-dd） pattern可以为："yyyy-MM-dd" "HH:mm:ss" "E"
     */
    public static String formatDate(Date date, Object... pattern) {
        String formatDate = null;
        if (pattern != null && pattern.length > 0) {
            formatDate = DateFormatUtils.format(date, pattern[0].toString());
        } else {
            formatDate = DateFormatUtils.format(date, "yyyy-MM-dd");
        }
        return formatDate;
    }

    /**
     * 得到日期字符串
     *
     * @param timestamp
     * @param pattern
     * @return
     */
    public static String formatDate(Timestamp timestamp, Object... pattern){
        String formatDate = null;
        if (pattern != null && pattern.length > 0) {
            formatDate = DateFormatUtils.format(timestamp, pattern[0].toString());
        } else {
            formatDate = DateFormatUtils.format(timestamp, "yyyy-MM-dd");
        }
        return formatDate;
    }

    /**
     * 日期型字符串转化为日期 格式
     * { "yyyy-MM-dd", "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm",
     *   "yyyy/MM/dd", "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm" }
     */
    public static Date parseDate(Object str) {
        if (str == null){
            return null;
        }
        try {
            return parseDate(str.toString(), parsePatterns);
        } catch (ParseException e) {
            return null;
        }
    }

    /**
     * 得到几天前的日期
     * @param d
     * @param day
     * @return
     */
    public static Date getDateBefore(Date d, int day){
        Calendar now =Calendar.getInstance();
        now.setTime(d);
        now.set(Calendar.DATE,now.get(Calendar.DATE)-day);
        return now.getTime();
    }

    /**
     * 获取当前时间 yyyy-MM-dd
     *
     * @return
     */
    public static String getCurrentDate(){
        Calendar c = Calendar.getInstance();
        c.setTime(new Date());
        return getStringDate(c);
    }

    /**
     * 格式化日期
     * @param
     * @return
     */
    public static String getStringDate(Calendar c) {
        return String.format("%4d-%02d-%02d",
                c.get(Calendar.YEAR),
                c.get(Calendar.MONTH) + 1,
                c.get(Calendar.DAY_OF_MONTH));
    }

    /**
     * 获取date日期后a(天/周/月/年)的日期(yyyy-MM-dd格式)
     */
    public static Date getLaterDate(int calendarType, int time, Date baseDate) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(baseDate);
        calendar.add(calendarType, time);
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date date = null;
        try {
            date = simpleDateFormat.parse(simpleDateFormat.format(calendar.getTime()));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return date;
    }

    /**
     * 输入时间是否在当前月
     */
    public static boolean isTimestampInCurrentMonth(Timestamp timestamp) {
        LocalDateTime dateTime = timestamp.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        LocalDate today = LocalDate.now();

        return dateTime.getYear() == today.getYear() && dateTime.getMonth() == today.getMonth();
    }

    /**
     * 输入时间是否早于当月
     * @param timestamp
     * @return
     */
    public static boolean isBeforeCurrentMonth(Timestamp timestamp) {
        LocalDateTime dateTime = timestamp.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        LocalDate firstDayOfCurrentMonth = LocalDate.now().withDayOfMonth(1);

        return dateTime.toLocalDate().isBefore(firstDayOfCurrentMonth);
    }
}
