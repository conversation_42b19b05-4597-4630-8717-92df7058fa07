package com.ssb.approve.utils;

import org.apache.commons.lang3.time.DateFormatUtils;

import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.Calendar;
import java.util.Date;

/**
 * @ClassName: DateUtils
 * @Description: 日期工具类
 * @Author: YZK
 * @Date: 2019年12月27日14:43:04
 **/
public class DateUtils extends org.apache.commons.lang3.time.DateUtils {

    private static String[] parsePatterns = { "yyyy-MM-dd", "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm",
            "yyyy/MM/dd", "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm" };

    /**
     * 得到日期字符串 默认格式（yyyy-MM-dd） pattern可以为："yyyy-MM-dd" "HH:mm:ss" "E"
     */
    public static String formatDate(Date date, Object... pattern) {
        String formatDate = null;
        if (pattern != null && pattern.length > 0) {
            formatDate = DateFormatUtils.format(date, pattern[0].toString());
        } else {
            formatDate = DateFormatUtils.format(date, "yyyy-MM-dd");
        }
        return formatDate;
    }

    /**
     * 得到日期字符串
     *
     * @param timestamp
     * @param pattern
     * @return
     */
    public static String formatDate(Timestamp timestamp, Object... pattern){
        String formatDate = null;
        if (pattern != null && pattern.length > 0) {
            formatDate = DateFormatUtils.format(timestamp, pattern[0].toString());
        } else {
            formatDate = DateFormatUtils.format(timestamp, "yyyy-MM-dd");
        }
        return formatDate;
    }

    /**
     * 日期型字符串转化为日期 格式
     * { "yyyy-MM-dd", "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm",
     *   "yyyy/MM/dd", "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm" }
     */
    public static Date parseDate(Object str) {
        if (str == null){
            return null;
        }
        try {
            return parseDate(str.toString(), parsePatterns);
        } catch (ParseException e) {
            return null;
        }
    }

    /**
     * 得到几天前的日期
     * @param d
     * @param day
     * @return
     */
    public static Date getDateBefore(Date d, int day){
        Calendar now =Calendar.getInstance();
        now.setTime(d);
        now.set(Calendar.DATE,now.get(Calendar.DATE)-day);
        return now.getTime();
    }

    /**
     * 获取当前时间 yyyy-MM-dd
     *
     * @return
     */
    public static String getCurrentDate(){
        Calendar c = Calendar.getInstance();
        c.setTime(new Date());
        return getStringDate(c);
    }

    /**
     * 格式化日期
     * @param
     * @return
     */
    public static String getStringDate(Calendar c) {
        return String.format("%4d-%02d-%02d",
                c.get(Calendar.YEAR),
                c.get(Calendar.MONTH) + 1,
                c.get(Calendar.DAY_OF_MONTH));
    }

    /**
     * 获取date日期后a(天/周/月/年)的日期(yyyy-MM-dd格式)
     */
    public static Date getLaterDate(int calendarType, int time, Date baseDate) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(baseDate);
        calendar.add(calendarType, time);
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date date = null;
        try {
            date = simpleDateFormat.parse(simpleDateFormat.format(calendar.getTime()));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return date;
    }

    /**
     * 输入时间是否在当前月
     */
    public static boolean isTimestampInCurrentMonth(Timestamp timestamp) {
        LocalDateTime dateTime = timestamp.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        LocalDate today = LocalDate.now();

        return dateTime.getYear() == today.getYear() && dateTime.getMonth() == today.getMonth();
    }

    /**
     * 输入时间是否早于当月
     * @param timestamp
     * @return
     */
    public static boolean isBeforeCurrentMonth(Timestamp timestamp) {
        LocalDateTime dateTime = timestamp.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        LocalDate firstDayOfCurrentMonth = LocalDate.now().withDayOfMonth(1);

        return dateTime.toLocalDate().isBefore(firstDayOfCurrentMonth);
    }

    /**
     * 获取指定天数偏移的日期的开始时间（00:00:00）
     * @param days 天数偏移，负数表示前几天，0表示当天，正数表示后几天
     * @return 对应日期的开始时间，类型为Date
     */
    public static Date getStartOfDay(int days) {
        // 获取当前日期，并根据days参数进行偏移
        LocalDate date = LocalDate.now().plusDays(days);
        // 将日期设置为当天的开始时间（00:00:00）
        LocalDateTime startOfDay = LocalDateTime.of(date, LocalTime.MIN);
        // 转换为Date类型并返回，使用系统默认时区
        return Date.from(startOfDay.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 获取指定天数偏移的日期的结束时间（23:59:59）
     * @param days 天数偏移，负数表示前几天，0表示当天，正数表示后几天
     * @return 对应日期的结束时间，类型为Date
     */
    public static Date getEndOfDay(int days) {
        // 获取当前日期，并根据dayss参数进行偏移
        LocalDate date = LocalDate.now().plusDays(days);
        // 将日期设置为当天的结束时间（23:59:59）
        LocalDateTime endOfDay = LocalDateTime.of(date, LocalTime.of(23, 59, 59));
        // 转换为Date类型并返回，使用系统默认时区
        return Date.from(endOfDay.atZone(ZoneId.systemDefault()).toInstant());
    }
}
