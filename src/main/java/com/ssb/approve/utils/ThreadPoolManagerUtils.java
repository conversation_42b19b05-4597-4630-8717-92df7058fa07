package com.ssb.approve.utils;

import java.util.concurrent.*;

/**
 * @ClassName: MyThreadPoolManager
 * @Description:
 * @Author: ZY
 * @Date: 2020-04-08 13:21
 **/
public class ThreadPoolManagerUtils {

    private static final int CPU_COUNT = Runtime.getRuntime().availableProcessors();
    //核心线程数 = CPU核心数 + 1
    private static final int CORE_POOL_SIZE = CPU_COUNT + 1;
    //线程池最大线程数 = CPU核心数 * 2 + 1
    private static final int MAXIMUM_POOL_SIZE = CPU_COUNT * 2 + 1;
    //非核心线程闲置时超时1s
    private static final int KEEP_ALIVE = 1;

    private ThreadPoolManagerUtils() {
    }

    private static ThreadPoolManagerUtils sInstance;

    public synchronized static ThreadPoolManagerUtils getsInstance() {

        if (sInstance == null) {
            sInstance = new ThreadPoolManagerUtils();
        }
        return sInstance;
    }

    // 线程池的对象
    private ThreadPoolExecutor executor;

    // 使用线程池，线程池中线程的创建完全是由线程池自己来维护的，我们不需要创建任何的线程
    public void execute(Runnable r) {

        if (executor == null) {
            /**
             * corePoolSize:核心线程数
             * maximumPoolSize：线程池所容纳最大线程数(workQueue队列满了之后才开启)
             * keepAliveTime：非核心线程闲置时间超时时长
             * unit：keepAliveTime的单位
             * workQueue：等待队列，存储还未执行的任务
             * threadFactory：线程创建的工厂
             * handler：异常处理机制
             */
            executor = new ThreadPoolExecutor(CORE_POOL_SIZE,
                    MAXIMUM_POOL_SIZE,
                    KEEP_ALIVE,
                    TimeUnit.SECONDS,
                    new ArrayBlockingQueue<>(20),
                    Executors.defaultThreadFactory(),
                    new ThreadPoolExecutor.AbortPolicy());
        }

        executor.execute(r);
    }

    public void cancel(Runnable r) {

        if (r != null) {
            //把任务移除等待队列
            executor.getQueue().remove(r);
        }
    }
}
