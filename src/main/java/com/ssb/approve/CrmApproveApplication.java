package com.ssb.approve;

import io.seata.spring.annotation.datasource.EnableAutoDataSourceProxy;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;

@SpringBootApplication
@EnableDiscoveryClient
@MapperScan("com.ssb.approve.dao")
@EnableFeignClients("com.ssb.approve.service.client")
@EnableAutoDataSourceProxy
public class CrmApproveApplication {

    public static void main(String[] args) {
        SpringApplication.run(CrmApproveApplication.class, args);
    }

}
